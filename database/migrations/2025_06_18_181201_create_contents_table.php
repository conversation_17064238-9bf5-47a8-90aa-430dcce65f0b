<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contents', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->boolean('status')->default(true);
            $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
            $table->string('contentable_type');
            $table->unsignedBigInteger('contentable_id');
            $table->timestamps();

            // Add indexes for better performance
            $table->index('category_id');
            $table->index('status');
            $table->index(['contentable_type', 'contentable_id']);
            $table->index(['status', 'category_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contents');
    }
};
