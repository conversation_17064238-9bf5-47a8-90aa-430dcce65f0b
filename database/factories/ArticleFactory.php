<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Article>
 */
class ArticleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'text' => $this->faker->paragraphs(3, true),
            'image' => $this->faker->boolean(60) ? 'articles/' . $this->faker->uuid() . '.jpg' : null,
        ];
    }

    /**
     * Create an article with an image.
     */
    public function withImage(): static
    {
        return $this->state(fn() => [
            'image' => 'articles/' . $this->faker->uuid() . '.jpg',
        ]);
    }

    /**
     * Create an article without an image.
     */
    public function withoutImage(): static
    {
        return $this->state(fn() => [
            'image' => null,
        ]);
    }
}
