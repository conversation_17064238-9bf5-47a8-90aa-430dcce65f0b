<?php

namespace Database\Factories;

use App\Models\Article;
use App\Models\Audio;
use App\Models\Book;
use App\Models\Category;
use App\Models\Video;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Content>
 */
class ContentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $contentableTypes = [Audio::class, Video::class, Book::class, Article::class];
        $contentableType = $this->faker->randomElement($contentableTypes);

        return [
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'status' => $this->faker->boolean(80),
            'category_id' => Category::factory(),
            'contentable_type' => $contentableType,
            'contentable_id' => $contentableType::factory(),
        ];
    }

    /**
     * Create content for audio.
     */
    public function audio(): static
    {
        return $this->state(fn(array $attributes) => [
            'contentable_type' => Audio::class,
            'contentable_id' => Audio::factory(),
        ]);
    }

    /**
     * Create content for video.
     */
    public function video(): static
    {
        return $this->state(fn(array $attributes) => [
            'contentable_type' => Video::class,
            'contentable_id' => Video::factory(),
        ]);
    }

    /**
     * Create content for book.
     */
    public function book(): static
    {
        return $this->state(fn(array $attributes) => [
            'contentable_type' => Book::class,
            'contentable_id' => Book::factory(),
        ]);
    }

    /**
     * Create content for article.
     */
    public function article(): static
    {
        return $this->state(fn(array $attributes) => [
            'contentable_type' => Article::class,
            'contentable_id' => Article::factory(),
        ]);
    }
}
