<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Video>
 */
class VideoFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'youtube_video_id' => $this->faker->unique()->regexify('[a-zA-Z0-9_-]{11}'),
            'duration' => $this->faker->optional(0.8)->randomElement([
                '3:45',
                '5:20',
                '10:15',
                '2:30',
                '8:45',
                '12:30',
                '6:15'
            ]),
        ];
    }

    /**
     * Create a video with a specific YouTube video ID for testing.
     */
    public function withVideoId(string $videoId): static
    {
        return $this->state(fn() => [
            'youtube_video_id' => $videoId,
        ]);
    }
}
