<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true),
            'status' => fake()->boolean(80), // 80% chance of being active
            'parent_id' => null, // Default to no parent
        ];
    }

    /**
     * Indicate that the category should be active.
     */
    public function active(): static
    {
        return $this->state(fn() => [
            'status' => true,
        ]);
    }

    /**
     * Indicate that the category should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn() => [
            'status' => false,
        ]);
    }

    /**
     * Create a category with a specific parent.
     */
    public function withParent(Category $parent): static
    {
        return $this->state(fn() => [
            'parent_id' => $parent->id,
        ]);
    }

    /**
     * Create a parent category (no parent).
     */
    public function parent(): static
    {
        return $this->state(fn() => [
            'parent_id' => null,
        ]);
    }
}
