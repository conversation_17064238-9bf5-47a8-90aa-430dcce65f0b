<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'text' => $this->faker->paragraphs(2, true),
            'image' => $this->faker->boolean(50) ? 'posts/' . $this->faker->uuid() . '.jpg' : null,
            'published_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 month', '+1 week'),
            'url' => $this->faker->optional(0.6)->url(),
        ];
    }

    /**
     * Create a published post.
     */
    public function published(): static
    {
        return $this->state(fn() => [
            'published_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Create an unpublished post.
     */
    public function unpublished(): static
    {
        return $this->state(fn() => [
            'published_at' => null,
        ]);
    }

    /**
     * Create a post with an image.
     */
    public function withImage(): static
    {
        return $this->state(fn() => [
            'image' => 'posts/' . $this->faker->uuid() . '.jpg',
        ]);
    }

    /**
     * Create a post with a URL.
     */
    public function withUrl(): static
    {
        return $this->state(fn() => [
            'url' => $this->faker->url(),
        ]);
    }
}
