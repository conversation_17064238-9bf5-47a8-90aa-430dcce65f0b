<?php

namespace Database\Seeders;

use App\Models\Audio;
use App\Models\Category;
use App\Models\Video;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create parent categories
        $parentCategories = [
            ['name' => 'Audio Content', 'status' => true, 'parent_id' => null],
            ['name' => 'Video Content', 'status' => true, 'parent_id' => null],
            ['name' => 'Reference Materials', 'status' => true, 'parent_id' => null],
            ['name' => 'Social Media', 'status' => true, 'parent_id' => null],
        ];

        $createdParents = [];
        foreach ($parentCategories as $categoryData) {
            $createdParents[] = Category::create($categoryData);
        }

        // Create subcategories for Audio Content
        $audioSubcategories = [
            ['name' => 'Islamic Lectures', 'status' => true, 'parent_id' => $createdParents[0]->id],
            ['name' => 'Quran Recitations', 'status' => true, 'parent_id' => $createdParents[0]->id],
            ['name' => 'Religious Discussions', 'status' => true, 'parent_id' => $createdParents[0]->id],
        ];

        foreach ($audioSubcategories as $categoryData) {
            $category = Category::create($categoryData);
            // Create sample audio content
            Audio::factory(rand(2, 4))->create(['category_id' => $category->id]);
        }

        // Create subcategories for Video Content
        $videoSubcategories = [
            ['name' => 'Educational Videos', 'status' => true, 'parent_id' => $createdParents[1]->id],
            ['name' => 'Documentary Films', 'status' => true, 'parent_id' => $createdParents[1]->id],
            ['name' => 'Live Streams', 'status' => true, 'parent_id' => $createdParents[1]->id],
        ];

        foreach ($videoSubcategories as $categoryData) {
            $category = Category::create($categoryData);
            // Create sample video content
            Video::factory(rand(1, 3))->create(['category_id' => $category->id]);
        }

        // Create subcategories for Reference Materials
        $referenceSubcategories = [
            ['name' => 'Books', 'status' => true, 'parent_id' => $createdParents[2]->id],
            ['name' => 'Articles', 'status' => true, 'parent_id' => $createdParents[2]->id],
            ['name' => 'Research Papers', 'status' => true, 'parent_id' => $createdParents[2]->id],
        ];

        foreach ($referenceSubcategories as $categoryData) {
            Category::create($categoryData);
        }

        // Create subcategories for Social Media
        $socialSubcategories = [
            ['name' => 'Daily Reminders', 'status' => true, 'parent_id' => $createdParents[3]->id],
            ['name' => 'Inspirational Quotes', 'status' => true, 'parent_id' => $createdParents[3]->id],
            ['name' => 'Community Updates', 'status' => true, 'parent_id' => $createdParents[3]->id],
        ];

        foreach ($socialSubcategories as $categoryData) {
            Category::create($categoryData);
        }

        // Create some additional random categories with mixed hierarchy
        $additionalParents = Category::factory(3)->parent()->create();
        foreach ($additionalParents as $parent) {
            // Create 1-3 subcategories for each additional parent
            $subcategoryCount = rand(1, 3);
            for ($i = 0; $i < $subcategoryCount; $i++) {
                $subcategory = Category::factory()->withParent($parent)->create();

                // Randomly add content to some subcategories
                if (rand(1, 100) <= 60) { // 60% chance
                    if (rand(1, 2) === 1) {
                        Audio::factory(rand(1, 3))->create(['category_id' => $subcategory->id]);
                    } else {
                        Video::factory(rand(1, 2))->create(['category_id' => $subcategory->id]);
                    }
                }
            }
        }
    }
}
