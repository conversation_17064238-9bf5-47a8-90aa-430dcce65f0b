<?php

namespace Database\Seeders;

use App\Models\Audio;
use App\Models\Category;
use App\Models\Content;
use App\Models\Video;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create parent categories in Arabic
        $parentCategories = [
            ['name' => 'الدروس', 'status' => true, 'parent_id' => null], // Lessons
            ['name' => 'المحاضرات', 'status' => true, 'parent_id' => null], // Lectures
            ['name' => 'الخطب', 'status' => true, 'parent_id' => null], // Sermons
            ['name' => 'المقالات', 'status' => true, 'parent_id' => null], // Articles
            ['name' => 'الكتب', 'status' => true, 'parent_id' => null], // Books
            ['name' => 'منشورات التواصل الاجتماعي', 'status' => true, 'parent_id' => null], // Social Media Posts
        ];

        $createdParents = [];
        foreach ($parentCategories as $categoryData) {
            $createdParents[] = Category::create($categoryData);
        }

        // Create subcategories for الدروس (Lessons)
        $lessonsSubcategories = [
            ['name' => 'دروس القرآن الكريم', 'status' => true, 'parent_id' => $createdParents[0]->id], // Quran Lessons
            ['name' => 'دروس الحديث الشريف', 'status' => true, 'parent_id' => $createdParents[0]->id], // Hadith Lessons
            ['name' => 'دروس الفقه', 'status' => true, 'parent_id' => $createdParents[0]->id], // Fiqh Lessons
            ['name' => 'دروس العقيدة', 'status' => true, 'parent_id' => $createdParents[0]->id], // Aqeedah Lessons
            ['name' => 'دروس السيرة النبوية', 'status' => true, 'parent_id' => $createdParents[0]->id], // Prophet's Biography Lessons
        ];

        foreach ($lessonsSubcategories as $categoryData) {
            $category = Category::create($categoryData);
            // Create sample audio content through Content model
            for ($i = 0; $i < rand(2, 4); $i++) {
                $audio = Audio::factory()->create();
                Content::create([
                    'title' => fake()->sentence(),
                    'description' => fake()->paragraph(),
                    'status' => true,
                    'category_id' => $category->id,
                    'contentable_type' => 'audio',
                    'contentable_id' => $audio->id,
                ]);
            }
        }

        // Create subcategories for المحاضرات (Lectures)
        $lecturesSubcategories = [
            ['name' => 'محاضرات دينية', 'status' => true, 'parent_id' => $createdParents[1]->id], // Religious Lectures
            ['name' => 'محاضرات تربوية', 'status' => true, 'parent_id' => $createdParents[1]->id], // Educational Lectures
            ['name' => 'محاضرات اجتماعية', 'status' => true, 'parent_id' => $createdParents[1]->id], // Social Lectures
            ['name' => 'محاضرات علمية', 'status' => true, 'parent_id' => $createdParents[1]->id], // Scientific Lectures
        ];

        foreach ($lecturesSubcategories as $categoryData) {
            $category = Category::create($categoryData);
            // Create sample video content through Content model
            for ($i = 0; $i < rand(1, 3); $i++) {
                $video = Video::factory()->create();
                Content::create([
                    'title' => fake()->sentence(),
                    'description' => fake()->paragraph(),
                    'status' => true,
                    'category_id' => $category->id,
                    'contentable_type' => 'video',
                    'contentable_id' => $video->id,
                ]);
            }
        }

        // Create subcategories for الخطب (Sermons)
        $sermonsSubcategories = [
            ['name' => 'خطب الجمعة', 'status' => true, 'parent_id' => $createdParents[2]->id], // Friday Sermons
            ['name' => 'خطب العيدين', 'status' => true, 'parent_id' => $createdParents[2]->id], // Eid Sermons
            ['name' => 'خطب رمضان', 'status' => true, 'parent_id' => $createdParents[2]->id], // Ramadan Sermons
            ['name' => 'خطب المناسبات', 'status' => true, 'parent_id' => $createdParents[2]->id], // Occasion Sermons
        ];

        foreach ($sermonsSubcategories as $categoryData) {
            $category = Category::create($categoryData);
            // Create sample audio content through Content model
            for ($i = 0; $i < rand(2, 3); $i++) {
                $audio = Audio::factory()->create();
                Content::create([
                    'title' => fake()->sentence(),
                    'description' => fake()->paragraph(),
                    'status' => true,
                    'category_id' => $category->id,
                    'contentable_type' => 'audio',
                    'contentable_id' => $audio->id,
                ]);
            }
        }

        // Create subcategories for المقالات (Articles)
        $articlesSubcategories = [
            ['name' => 'مقالات إسلامية', 'status' => true, 'parent_id' => $createdParents[3]->id], // Islamic Articles
            ['name' => 'مقالات فكرية', 'status' => true, 'parent_id' => $createdParents[3]->id], // Intellectual Articles
            ['name' => 'مقالات تربوية', 'status' => true, 'parent_id' => $createdParents[3]->id], // Educational Articles
            ['name' => 'مقالات اجتماعية', 'status' => true, 'parent_id' => $createdParents[3]->id], // Social Articles
        ];

        foreach ($articlesSubcategories as $categoryData) {
            Category::create($categoryData);
        }

        // Create subcategories for الكتب (Books)
        $booksSubcategories = [
            ['name' => 'كتب التفسير', 'status' => true, 'parent_id' => $createdParents[4]->id], // Tafsir Books
            ['name' => 'كتب الحديث', 'status' => true, 'parent_id' => $createdParents[4]->id], // Hadith Books
            ['name' => 'كتب الفقه', 'status' => true, 'parent_id' => $createdParents[4]->id], // Fiqh Books
            ['name' => 'كتب العقيدة', 'status' => true, 'parent_id' => $createdParents[4]->id], // Aqeedah Books
            ['name' => 'كتب السيرة', 'status' => true, 'parent_id' => $createdParents[4]->id], // Biography Books
        ];

        foreach ($booksSubcategories as $categoryData) {
            Category::create($categoryData);
        }

        // Create subcategories for منشورات التواصل الاجتماعي (Social Media Posts)
        $socialMediaSubcategories = [
            ['name' => 'تذكيرات يومية', 'status' => true, 'parent_id' => $createdParents[5]->id], // Daily Reminders
            ['name' => 'اقتباسات ملهمة', 'status' => true, 'parent_id' => $createdParents[5]->id], // Inspirational Quotes
            ['name' => 'نصائح دينية', 'status' => true, 'parent_id' => $createdParents[5]->id], // Religious Tips
            ['name' => 'أخبار المجتمع', 'status' => true, 'parent_id' => $createdParents[5]->id], // Community News
        ];

        foreach ($socialMediaSubcategories as $categoryData) {
            Category::create($categoryData);
        }
    }
}
