// Frontend JavaScript Example for Audio API
// This example shows how to consume the audio API endpoint

class AudioService {
    constructor(baseUrl = 'http://your-domain.com/api/v1') {
        this.baseUrl = baseUrl;
    }

    /**
     * Fetch audio records grouped by categories
     * @param {Object} options - Request options
     * @param {boolean} options.paginated - Whether to paginate categories
     * @param {number} options.perPage - Number of categories per page
     * @param {number} options.itemsPerPage - Number of audio records per category
     * @param {number} options.itemsPage - Page number for audio records within categories
     * @returns {Promise<Object>} API response
     */
    async getAudiosByCategory(options = {}) {
        const {
            paginated = false,
            perPage = 15,
            itemsPerPage = 10,
            itemsPage = 1
        } = options;

        const params = new URLSearchParams();
        if (paginated) {
            params.append('paginated', '1');
            params.append('per_page', perPage.toString());
        }
        if (itemsPerPage !== 10) {
            params.append('items_per_page', itemsPerPage.toString());
        }
        if (itemsPage !== 1) {
            params.append('items_page', itemsPage.toString());
        }

        const url = `${this.baseUrl}/audios/by-category${params.toString() ? '?' + params.toString() : ''}`;

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                if (response.status === 422) {
                    throw new Error(`Validation error: ${JSON.stringify(errorData.errors)}`);
                }
                throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching audio by category:', error);
            throw error;
        }
    }

    /**
     * Fetch audio records for a specific category
     * @param {number} categoryId - Category ID
     * @param {Object} options - Request options
     * @param {number} options.perPage - Number of audio records per page
     * @param {number} options.page - Page number
     * @returns {Promise<Object>} API response
     */
    async getCategoryAudio(categoryId, options = {}) {
        const { perPage = 10, page = 1 } = options;

        const params = new URLSearchParams();
        if (perPage !== 10) {
            params.append('per_page', perPage.toString());
        }
        if (page !== 1) {
            params.append('page', page.toString());
        }

        const url = `${this.baseUrl}/audios/category/${categoryId}${params.toString() ? '?' + params.toString() : ''}`;

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                if (response.status === 404) {
                    throw new Error(errorData.error || 'Category not found');
                }
                if (response.status === 422) {
                    throw new Error(`Validation error: ${JSON.stringify(errorData.errors)}`);
                }
                throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching category audio:', error);
            throw error;
        }
    }
}

// Usage Examples

const audioService = new AudioService();

// Example 1: Basic usage
async function loadAudioCategories() {
    try {
        const response = await audioService.getAudiosByCategory();
        const { categories, total_categories, total_audio_records } = response.data;

        console.log(`Found ${total_categories} categories with ${total_audio_records} audio records`);

        categories.forEach(category => {
            console.log(`Category: ${category.name} (${category.total_audio_count} total audio files)`);
            console.log(`  Showing ${category.audio_records.length} audio files (page ${category.audio_pagination.current_page})`);
            category.audio_records.forEach(audio => {
                console.log(`  - ${audio.title}: ${audio.audio_url}`);
            });
        });
    } catch (error) {
        console.error('Failed to load audio categories:', error);
    }
}

// Example 2: Audio pagination within categories
async function loadAudioCategoriesWithPagination(itemsPerPage = 5, itemsPage = 1) {
    try {
        const response = await audioService.getAudiosByCategory({
            itemsPerPage: itemsPerPage,
            itemsPage: itemsPage
        });

        const { categories } = response.data;

        categories.forEach(category => {
            const { audio_pagination } = category;
            console.log(`Category: ${category.name}`);
            console.log(`  Audio page ${audio_pagination.current_page} of ${Math.ceil(audio_pagination.total / audio_pagination.per_page)}`);
            console.log(`  Has more: ${audio_pagination.has_more}`);
        });

        return response.data;
    } catch (error) {
        console.error('Failed to load audio categories with pagination:', error);
        throw error;
    }
}

// Example 3: Category pagination
async function loadCategoriesPaginated(page = 1, perPage = 5) {
    try {
        const response = await audioService.getAudiosByCategory({
            paginated: true,
            perPage: perPage
        });

        const { categories } = response.data;
        const { current_page, last_page, total } = response.pagination;

        console.log(`Page ${current_page} of ${last_page} (${total} total categories)`);

        return {
            categories,
            pagination: response.pagination
        };
    } catch (error) {
        console.error('Failed to load paginated categories:', error);
        throw error;
    }
}

// Example 4: Get specific category audio
async function loadCategoryAudio(categoryId, page = 1, perPage = 10) {
    try {
        const response = await audioService.getCategoryAudio(categoryId, {
            page: page,
            perPage: perPage
        });

        const { category, audio_records, pagination } = response.data;

        console.log(`Category: ${category.name}`);
        console.log(`Audio records: ${audio_records.length} of ${pagination.total}`);
        console.log(`Page ${pagination.current_page} of ${pagination.last_page}`);

        return response.data;
    } catch (error) {
        console.error('Failed to load category audio:', error);
        throw error;
    }
}

// Example 3: React Component Usage
function AudioCategoriesComponent() {
    const [categories, setCategories] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
        async function fetchData() {
            try {
                setLoading(true);
                const response = await audioService.getAudiosByCategory();
                setCategories(response.data.categories);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        }

        fetchData();
    }, []);

    if (loading) return <div>Loading audio categories...</div>;
    if (error) return <div>Error: {error}</div>;

    return (
        <div className="audio-categories">
            {categories.map(category => (
                <div key={category.id} className="category">
                    <h3>{category.name} ({category.audio_count} files)</h3>
                    <div className="audio-list">
                        {category.audio_records.map(audio => (
                            <div key={audio.id} className="audio-item">
                                <h4>{audio.title}</h4>
                                <p>{audio.description}</p>
                                <audio controls>
                                    <source src={audio.audio_url} type="audio/mpeg" />
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
}

// Example 4: Vue.js Composition API Usage
function useAudioCategories() {
    const categories = Vue.ref([]);
    const loading = Vue.ref(true);
    const error = Vue.ref(null);

    const fetchCategories = async () => {
        try {
            loading.value = true;
            error.value = null;
            const response = await audioService.getAudiosByCategory();
            categories.value = response.data.categories;
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    };

    Vue.onMounted(fetchCategories);

    return {
        categories: Vue.readonly(categories),
        loading: Vue.readonly(loading),
        error: Vue.readonly(error),
        refetch: fetchCategories
    };
}

// Call the examples
loadAudioCategories();
loadAudioCategoriesPaginated(1, 3);
