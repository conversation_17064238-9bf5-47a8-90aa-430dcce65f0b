// ignore_for_file: always_put_control_body_on_new_line

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_storage/get_storage.dart';
import 'package:stack_trace/stack_trace.dart' as stack_trace;

import 'core/error/global_error_handler.dart';
import 'core/logging/app_logger.dart';
import 'data/sqlite/sqlite.dart';
import 'data/sqlite/sqlite_helper.dart';
import 'di/components/service_locator.dart';
import 'my_app.dart';

/// Try using const constructors as much as possible!

/// This class is used to bypass SSL certificate validation
/// CRITICAL: Only use this in development - NEVER in production
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // ONLY bypass SSL in debug mode for development
        if (kDebugMode) {
          debugPrint(
              '⚠️ WARNING: Bypassing SSL certificate for $host in DEBUG mode');
          return true;
        }
        // In production, always validate certificates
        return false;
      };
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logging system first
  await AppLogger.initialize();
  AppLogger.info('🚀 Starting Dr. Al-Farih App');

  // Initialize global error handling
  GlobalErrorHandler.initialize();

  // Set up stack trace formatting (move before other initializations)
  FlutterError.demangleStackTrace = (StackTrace stack) {
    if (stack is stack_trace.Trace) return stack.vmTrace;
    if (stack is stack_trace.Chain) return stack.toTrace().vmTrace;
    return stack;
  };

  // Override HTTP client ONLY in debug mode (CRITICAL SECURITY FIX)
  if (kDebugMode) {
    HttpOverrides.global = MyHttpOverrides();
    // AppLogger.warning('⚠️ SSL bypass enabled for DEBUG mode only');
  }

  // Initialize storage and database
  await GetStorage.init();
  // AppLogger.debug('💾 GetStorage initialized');

  await initSQLite();
  // AppLogger.debug('🗄️ SQLite initialized');

  await configureDependencies();
  // AppLogger.debug('🔧 Dependencies configured');

  await setPreferredOrientations();
  // AppLogger.debug('📱 Orientations set');

  // Set status bar color to kPrimaryLight with white icons
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.dark,
  ));

  getIt<SQLiteHelper>().initSQLite();

  // Set high refresh rate for Android
  if (!kIsWeb) {
    if (Platform.isAndroid) {
      await FlutterDisplayMode.setHighRefreshRate();
      // AppLogger.debug('📱 High refresh rate enabled');
    }
  }

  // Clear old logs in production
  if (kReleaseMode) {
    AppLogger.clearOldLogs();
  }

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}
