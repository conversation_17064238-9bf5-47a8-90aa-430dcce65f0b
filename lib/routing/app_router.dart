// ignore_for_file: prefer_function_declarations_over_variables

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:injectable/injectable.dart';

import '../data/enums/media_type_enum.dart';
import '../data/getstore/get_store_helper.dart';
import '../di/components/service_locator.dart';
import '../features/about_us/about_us_screen.dart';
import '../features/auth/presentation/forgot_password_page.dart';
import '../features/auth/presentation/login_page.dart';
import '../features/auth/presentation/profile_page.dart';
import '../features/auth/presentation/register_page.dart';
import '../features/home/<USER>/category_items_page.dart';
import '../features/home/<USER>/home.dart';
import '../features/home/<USER>/media_list_view.dart';
import '../features/media_player/presentation/media_player_screen.dart';
import '../features/search/presentation/search_screen.dart';
import '../features/settings/presentation/favorites_screen.dart';
import '../features/settings/presentation/history_screen.dart';
import '../features/settings/presentation/progress_screen.dart';
import '../features/settings/presentation/settings_screen.dart';
import '../features/splash/presentation/splash_screen.dart';
import '../features/surah_player/presentation/surah_player_screen.dart';
import '../features/text_media_viewer/presentation/pdf_info_screen.dart';
import '../features/text_media_viewer/presentation/text_media_router_screen_riverpod.dart';
import '../features/tweets/presentation/tweet_details_screen.dart';
import '../features/tweets/presentation/tweets_list_screen.dart';
import 'fade_extension.dart';

GetStoreHelper getStoreHelper = getIt<GetStoreHelper>();

enum SGRoute {
  splash,
  home,
  settings,
  login,
  register,
  forgotPassword,
  profile,
  editProfile,
  changePassword,
  mediaPlayer,
  surahPlayer,
  textMediaViewer,
  tweets,
  favorites,
  history,
  progress,
  search,
  aboutUs,
  pdfInfo,
  ;

  String get route {
    final String routeName = toString().replaceAll('SGRoute.', '');
    // debugPrint('Generated route for $this: /$routeName');
    return '/$routeName';
  }

  String get name => toString().replaceAll('SGRoute.', '');
}

@Singleton()
class SGGoRouter {
  final GoRouter goRoute = GoRouter(
    initialLocation: SGRoute.splash.route,
    // debugLogDiagnostics: true, // Enable debug logging
    routes: <GoRoute>[
      // Splash screen route
      GoRoute(
        path: SGRoute.splash.route,
        name: 'splash',
        builder: (BuildContext context, GoRouterState state) =>
            const SplashScreen(),
      ).fade(),
      // Define specific named routes first
      GoRoute(
        path: SGRoute.home.route,
        builder: (BuildContext context, GoRouterState state) =>
            const HomeScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.settings.route,
        builder: (BuildContext context, GoRouterState state) =>
            const SettingsScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.aboutUs.route,
        builder: (BuildContext context, GoRouterState state) =>
            const AboutUsScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.login.route,
        name: 'login',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to LoginPage');
          return const LoginPage();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.register.route,
        name: 'register',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to RegisterPage');
          return const RegisterPage();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.forgotPassword.route,
        name: 'forgotPassword',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to ForgotPasswordPage');
          return const ForgotPasswordPage();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.profile.route,
        name: 'profile',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to ProfilePage');
          return const ProfilePage();
        },
      ).fade(),
      // New Route for SurahPlayerScreen (for audio) - Moved up before generic routes
      GoRoute(
        path: '${SGRoute.surahPlayer.route}/:id',
        name: 'surahPlayer', // Give it a name
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to SurahPlayerScreen with id: $id');
          return SurahPlayerScreen(surahId: id); // Pass the ID
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.mediaPlayer.route}/:id',
        name: 'mediaPlayer',
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to MediaPlayerScreen with id: $id');
          // MediaPlayerScreen will still exist but MediaNavigationHelper
          // will redirect audio to surahPlayer.
          return MediaPlayerScreen(mediaId: id);
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.textMediaViewer.route}/:id',
        name: 'textMediaViewer', // Add a name for easier debugging
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to TextMediaRouterScreen with id: $id');
          return TextMediaRouterScreen(mediaId: id);
        },
      ).fade(),
      GoRoute(
        path: SGRoute.tweets.route,
        name: 'tweets',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to TweetsListScreen');
          return const TweetsListScreen();
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.tweets.route}/:id',
        name: 'tweetDetails',
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to TweetDetailsScreen with id: $id');
          return TweetDetailsScreen(tweetId: id);
        },
      ).fade(),
      GoRoute(
        path: SGRoute.favorites.route,
        name: 'favorites',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to FavoritesScreen');
          return const FavoritesScreen();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.history.route,
        name: 'history',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to HistoryScreen');
          return const HistoryScreen();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.progress.route,
        name: 'progress',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to ProgressScreen');
          return const ProgressScreen();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.search.route,
        name: 'search',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to SearchScreen');
          return const SearchScreen();
        },
      ).fade(),

      // Specific PDF route - must be defined before generic routes
      GoRoute(
        path: '${SGRoute.pdfInfo.route}/:id', // Takes mediaId as parameter
        name: SGRoute.pdfInfo.name,
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to PdfInfoScreen with id: $id');
          return PdfInfoScreen(mediaId: id);
        },
      ).fade(),

      // Define generic pattern routes last
      GoRoute(
        path: '/:type/:category',
        name: 'categoryItems',
        builder: (BuildContext context, GoRouterState state) {
          final String type = state.pathParameters['type']!;
          final String category = state.pathParameters['category']!;
          debugPrint(
              'Navigating to CategoryItemsPage with type: $type, category: $category');
          return CategoryItemsPage(
              type: MediaType.fromString(type),
              category: MediaCategory.fromString(category));
        },
      ).fade(),
      GoRoute(
        path: '/:type',
        name: 'items',
        builder: (BuildContext context, GoRouterState state) {
          final String type = state.pathParameters['type']!;
          debugPrint('Navigating to ItemsPage with type: $type');
          return ItemsPage(type: type);
        },
      ).fade(),
    ],
  );
  GoRouter get getGoRouter => goRoute;
}

/// Example: Auth guard for Route Protection. GetStoreHelper is used to get token.

final String? Function(BuildContext context, GoRouterState state) _authGuard =
    (BuildContext context, GoRouterState state) {
  if (!(getStoreHelper.getToken() != null)) {
    return SGRoute.login.route;
  }
  return null;
};
