import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:injectable/injectable.dart';

import '../services/secure_storage_service.dart';

const String emailKey = 'email';
const String themeKey = 'theme_mode';
const String languageKey = 'language';
const String lastSyncKey = 'last_sync';

@injectable

/// Secure storage helper that separates sensitive and non-sensitive data
/// - Sensitive data (tokens, user IDs) → SecureStorageService (encrypted)
/// - Non-sensitive data (preferences, cache) → GetStorage (fast access)
class GetStoreHelper {
  GetStoreHelper(this.getStorage, this.secureStorage);

  final GetStorage getStorage;
  final SecureStorageService secureStorage;

  // === SECURE DATA METHODS (using SecureStorageService) ===

  /// Save authentication token securely
  Future<void> saveToken(String token) async {
    await secureStorage.saveAuthToken(token);
  }

  /// Get authentication token securely
  Future<String?> getToken() async {
    return secureStorage.getAuthToken();
  }

  /// Get authentication token synchronously (cached version)
  /// This is used for immediate checks like route guards
  String? getTokenSync() {
    // This returns the cached token or null if not available
    // For actual authentication, always use the async getToken()
    return secureStorage.getAuthTokenSync();
  }

  /// Save user ID securely
  Future<void> saveUserId(String userId) async {
    await secureStorage.saveUserId(userId);
  }

  /// Get user ID securely
  Future<String?> getUserId() async {
    return secureStorage.getUserId();
  }

  /// Clear all secure data (logout)
  Future<void> clearSecureData() async {
    await secureStorage.clearAll();
  }

  // === NON-SENSITIVE DATA METHODS (using GetStorage) ===

  /// Save email (non-sensitive for auto-fill)
  Future<void> saveEmail(String email) async {
    await getStorage.write(emailKey, email);
  }

  /// Get saved email
  String? getEmail() {
    return getStorage.read(emailKey);
  }

  /// Save theme mode preference
  Future<void> saveThemeMode(String themeMode) async {
    await getStorage.write(themeKey, themeMode);
  }

  /// Get theme mode preference
  String? getThemeMode() {
    return getStorage.read(themeKey);
  }

  /// Save language preference
  Future<void> saveLanguage(String language) async {
    await getStorage.write(languageKey, language);
  }

  /// Get language preference
  String? getLanguage() {
    return getStorage.read(languageKey);
  }

  /// Save last sync timestamp
  Future<void> saveLastSync(DateTime timestamp) async {
    await getStorage.write(lastSyncKey, timestamp.toIso8601String());
  }

  /// Get last sync timestamp
  DateTime? getLastSync() {
    final String? timestamp = getStorage.read(lastSyncKey);
    return timestamp != null ? DateTime.tryParse(timestamp) : null;
  }

  /// Clear all non-sensitive data
  void clearNonSensitiveData() {
    getStorage.erase();
  }

  /// Clear all data (both secure and non-sensitive)
  Future<void> clearAll() async {
    await clearSecureData();
    clearNonSensitiveData();
  }

  // === DEPRECATED METHODS (for backward compatibility) ===

  @Deprecated('Use saveEmail() instead. Passwords should never be stored.')
  Future<void> saveEmailAndPassword(String email, String password) async {
    // Only save email, ignore password for security
    await saveEmail(email);
    // Log warning about deprecated usage
    debugPrint(
        'WARNING: saveEmailAndPassword is deprecated. Password storage is not secure.');
  }

  @Deprecated(
      'Passwords should never be stored. Use secure authentication tokens instead.')
  String? getPassword() {
    // Always return null - passwords should not be stored
    debugPrint(
        'WARNING: getPassword is deprecated. Passwords should not be stored.');
    return null;
  }

  @Deprecated('Use clearAll() instead')
  void clear() {
    clearNonSensitiveData();
  }
}
