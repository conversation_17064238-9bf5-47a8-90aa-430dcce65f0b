import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

import '../../core/utils/password_util.dart';
import '../enums/action_type_enum.dart';
import '../enums/media_type_enum.dart';
import '../models/user.dart';
import '../models/user_data_models.dart';

@injectable
class SQLiteHelper {
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) {
      return _database!;
    }
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final String path = join(await getDatabasesPath(), 'app_database.db');
    return openDatabase(
      path,
      version: 2,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Update users table with new fields
      await db.execute('ALTER TABLE users ADD COLUMN password TEXT');
      await db.execute('ALTER TABLE users ADD COLUMN token TEXT');
      await db.execute('ALTER TABLE users ADD COLUMN created_at TEXT');
      await db.execute('ALTER TABLE users ADD COLUMN updated_at TEXT');

      // Create favorites table
      await db.execute('''
        CREATE TABLE favorites(
          id TEXT PRIMARY KEY,
          user_id TEXT,
          item_id TEXT,
          type TEXT,
          created_at TEXT,
          sync_status TEXT,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
      ''');

      // Create progress table
      await db.execute('''
        CREATE TABLE progress(
          id TEXT PRIMARY KEY,
          user_id TEXT,
          item_id TEXT,
          position_seconds REAL,
          last_updated TEXT,
          sync_status TEXT,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
      ''');

      // Create history table
      await db.execute('''
        CREATE TABLE history(
          id TEXT PRIMARY KEY,
          user_id TEXT,
          item_id TEXT,
          action_type TEXT,
          timestamp TEXT,
          sync_status TEXT,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
      ''');
    }
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create preferences table
    await db.execute('''
      CREATE TABLE prefs(
        key TEXT PRIMARY KEY,
        value TEXT
      )
    ''');

    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id TEXT PRIMARY KEY,
        name TEXT,
        email TEXT,
        password TEXT,
        token TEXT,
        role TEXT,
        verified INTEGER,
        created_at TEXT,
        updated_at TEXT
      )
    ''');

    // Create favorites table
    await db.execute('''
      CREATE TABLE favorites(
        id TEXT PRIMARY KEY,
        user_id TEXT,
        item_id TEXT,
        type TEXT,
        created_at TEXT,
        sync_status TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Create progress table
    await db.execute('''
      CREATE TABLE progress(
        id TEXT PRIMARY KEY,
        user_id TEXT,
        item_id TEXT,
        position_seconds REAL,
        last_updated TEXT,
        sync_status TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Create history table
    await db.execute('''
      CREATE TABLE history(
        id TEXT PRIMARY KEY,
        user_id TEXT,
        item_id TEXT,
        action_type TEXT,
        timestamp TEXT,
        sync_status TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');
  }

  // Preferences methods
  Future<void> savePreference(String key, String value) async {
    final Database db = await database;
    await db.insert(
      'prefs',
      <String, Object?>{'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getPreference(String key, {String? defaultValue}) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'prefs',
      columns: <String>['value'],
      where: 'key = ?',
      whereArgs: <Object?>[key],
    );

    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return defaultValue;
  }

  // User authentication methods
  Future<void> saveUser(UserModel user) async {
    final Database db = await database;
    final Map<String, Object?> userData = <String, Object?>{
      'id': user.id,
      'name': user.name,
      'email': user.email,
      'role': user.role,
      'verified': user.verified ? 1 : 0,
    };

    if (user.password != null) {
      userData['password'] = user.password;
    }

    if (user.token != null) {
      userData['token'] = user.token;
    }

    if (user.createdAt != null) {
      userData['created_at'] = user.createdAt!.toIso8601String();
    }

    if (user.updatedAt != null) {
      userData['updated_at'] = user.updatedAt!.toIso8601String();
    }

    await db.insert(
      'users',
      userData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserModel?> getUserById(String id) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return _userFromMap(maps.first);
    }
    return null;
  }

  Future<UserModel?> getUserByToken(String token) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'token = ?',
      whereArgs: <Object?>[token],
    );

    if (maps.isNotEmpty) {
      return _userFromMap(maps.first);
    }
    return null;
  }

  Future<UserModel?> getUserByEmail(String email) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: <Object?>[email],
    );

    if (maps.isNotEmpty) {
      return _userFromMap(maps.first);
    }
    return null;
  }

  Future<UserModel?> getCurrentUser() async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');

    if (maps.isNotEmpty) {
      return _userFromMap(maps.first);
    }
    return null;
  }

  UserModel _userFromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] as String,
      name: map['name'] as String,
      email: map['email'] as String,
      password: map['password'] as String?,
      token: map['token'] as String?,
      role: map['role'] as String,
      verified: map['verified'] == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  // User authentication
  Future<UserModel?> registerUser({
    required String name,
    required String email,
    required String password,
  }) async {
    final Database db = await database;

    // Check if user with this email already exists
    final List<Map<String, dynamic>> existingUsers = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: <Object?>[email],
    );

    if (existingUsers.isNotEmpty) {
      return null; // User already exists
    }

    final String userId = const Uuid().v4();
    final String token = const Uuid().v4();
    final DateTime now = DateTime.now();

    // Generate salt and hash the password
    final String salt = PasswordUtil.generateSalt();
    final String hashedPassword = PasswordUtil.hashPassword(password, salt);

    // Store password as a JSON string containing hash and salt
    final String securePassword = jsonEncode(<String, String>{
      'hash': hashedPassword,
      'salt': salt,
    });

    final UserModel newUser = UserModel(
      id: userId,
      name: name,
      email: email,
      password: securePassword, // Store the secure password
      token: token,
      role: 'user',
      verified: true,
      createdAt: now,
      updatedAt: now,
    );

    await saveUser(newUser);
    return newUser;
  }

  Future<UserModel?> loginUser({
    required String email,
    required String password,
  }) async {
    final Database db = await database;

    // First, find the user by email only
    final List<Map<String, dynamic>> users = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: <Object?>[email],
    );

    if (users.isEmpty) {
      return null; // User not found
    }

    final UserModel user = _userFromMap(users.first);

    // Verify the password
    if (user.password == null) {
      return null; // Password not set
    }

    try {
      // Parse the stored password JSON
      final Map<String, dynamic> passwordData =
          jsonDecode(user.password!) as Map<String, dynamic>;

      final String storedHash = passwordData['hash'] as String;
      final String storedSalt = passwordData['salt'] as String;

      // Verify the password
      final bool isPasswordValid =
          PasswordUtil.verifyPassword(password, storedHash, storedSalt);

      if (!isPasswordValid) {
        return null; // Invalid password
      }

      // Password is valid, update token and last login
      final String newToken = const Uuid().v4();
      final DateTime now = DateTime.now();

      await db.update(
        'users',
        <String, Object?>{
          'token': newToken,
          'updated_at': now.toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: <Object?>[user.id],
      );

      return user.copyWith(token: newToken, updatedAt: now);
    } catch (e) {
      // Handle legacy passwords or invalid password format
      // This allows for backward compatibility if needed
      if (user.password == password) {
        // Legacy plain text password match
        // Update to new secure format
        final String salt = PasswordUtil.generateSalt();
        final String hashedPassword = PasswordUtil.hashPassword(password, salt);
        final String securePassword = jsonEncode(<String, String>{
          'hash': hashedPassword,
          'salt': salt,
        });

        final String newToken = const Uuid().v4();
        final DateTime now = DateTime.now();

        await db.update(
          'users',
          <String, Object?>{
            'password': securePassword,
            'token': newToken,
            'updated_at': now.toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: <Object?>[user.id],
        );

        return user.copyWith(
            password: securePassword, token: newToken, updatedAt: now);
      }

      return null; // Invalid password or format
    }
  }

  Future<bool> changePassword({
    required String userId,
    required String currentPassword,
    required String newPassword,
  }) async {
    final Database db = await database;

    // Get the user first
    final List<Map<String, dynamic>> users = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: <Object?>[userId],
    );

    if (users.isEmpty) {
      return false; // User not found
    }

    final UserModel user = _userFromMap(users.first);

    if (user.password == null) {
      return false; // Password not set
    }

    bool isCurrentPasswordValid = false;

    try {
      // Parse the stored password JSON
      final Map<String, dynamic> passwordData =
          jsonDecode(user.password!) as Map<String, dynamic>;

      final String storedHash = passwordData['hash'] as String;
      final String storedSalt = passwordData['salt'] as String;

      // Verify the current password
      isCurrentPasswordValid =
          PasswordUtil.verifyPassword(currentPassword, storedHash, storedSalt);
    } catch (e) {
      // Handle legacy passwords
      isCurrentPasswordValid = (user.password == currentPassword);
    }

    if (!isCurrentPasswordValid) {
      return false; // Current password is incorrect
    }

    // Generate new salt and hash for the new password
    final String salt = PasswordUtil.generateSalt();
    final String hashedPassword = PasswordUtil.hashPassword(newPassword, salt);
    final String securePassword = jsonEncode(<String, String>{
      'hash': hashedPassword,
      'salt': salt,
    });

    await db.update(
      'users',
      <String, Object?>{
        'password': securePassword,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: <Object?>[userId],
    );

    return true;
  }

  Future<bool> updateUserProfile({
    required String userId,
    String? name,
    String? email,
  }) async {
    final Database db = await database;

    final Map<String, Object?> updates = <String, Object?>{
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (name != null) {
      updates['name'] = name;
    }

    if (email != null) {
      // Check if email is already used by another user
      final List<Map<String, dynamic>> existingUsers = await db.query(
        'users',
        where: 'email = ? AND id != ?',
        whereArgs: <Object?>[email, userId],
      );

      if (existingUsers.isNotEmpty) {
        return false; // Email already in use
      }

      updates['email'] = email;
    }

    await db.update(
      'users',
      updates,
      where: 'id = ?',
      whereArgs: <Object?>[userId],
    );

    return true;
  }

  Future<void> logoutUser(String userId) async {
    final Database db = await database;

    await db.update(
      'users',
      <String, Object?>{
        'token': null,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: <Object?>[userId],
    );
  }

  // Favorites methods
  Future<String> addFavorite({
    required String userId,
    required String itemId,
    required String type,
  }) async {
    final Database db = await database;
    final String id = const Uuid().v4();
    final DateTime now = DateTime.now();

    await db.insert(
      'favorites',
      <String, Object?>{
        'id': id,
        'user_id': userId,
        'item_id': itemId,
        'type': type,
        'created_at': now.toIso8601String(),
        'sync_status': SyncStatus.pending.toString().split('.').last,
      },
    );

    return id;
  }

  Future<bool> removeFavorite({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;

    final int count = await db.delete(
      'favorites',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );

    return count > 0;
  }

  Future<List<FavoriteItem>> getUserFavorites(String userId) async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      where: 'user_id = ?',
      whereArgs: <Object?>[userId],
      orderBy: 'created_at DESC',
    );

    return maps.map((Map<String, dynamic> map) {
      return FavoriteItem(
        id: map['id'] as String,
        userId: map['user_id'] as String,
        itemId: map['item_id'] as String,
        type: MediaType.fromString(map['type'] as String),
        createdAt: DateTime.parse(map['created_at'] as String),
        syncStatus: SyncStatus.values.firstWhere(
          (SyncStatus status) =>
              status.toString().split('.').last == map['sync_status'],
          orElse: () => SyncStatus.pending,
        ),
      );
    }).toList();
  }

  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );

    return maps.isNotEmpty;
  }

  // Progress methods
  Future<String> saveProgress({
    required String userId,
    required String itemId,
    required double positionSeconds,
  }) async {
    final Database db = await database;
    final DateTime now = DateTime.now();

    // Check if progress already exists
    final List<Map<String, dynamic>> existingProgress = await db.query(
      'progress',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );

    if (existingProgress.isNotEmpty) {
      // Update existing progress
      await db.update(
        'progress',
        <String, Object?>{
          'position_seconds': positionSeconds,
          'last_updated': now.toIso8601String(),
          'sync_status': SyncStatus.pending.toString().split('.').last,
        },
        where: 'user_id = ? AND item_id = ?',
        whereArgs: <Object?>[userId, itemId],
      );

      return existingProgress.first['id'] as String;
    } else {
      // Create new progress
      final String id = const Uuid().v4();

      await db.insert(
        'progress',
        <String, Object?>{
          'id': id,
          'user_id': userId,
          'item_id': itemId,
          'position_seconds': positionSeconds,
          'last_updated': now.toIso8601String(),
          'sync_status': SyncStatus.pending.toString().split('.').last,
        },
      );

      return id;
    }
  }

  Future<ProgressItem?> getProgress({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'progress',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );

    if (maps.isEmpty) {
      return null;
    }

    final Map<String, dynamic> map = maps.first;

    return ProgressItem(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      itemId: map['item_id'] as String,
      positionSeconds: map['position_seconds'] as double,
      lastUpdated: DateTime.parse(map['last_updated'] as String),
      syncStatus: SyncStatus.values.firstWhere(
        (SyncStatus status) =>
            status.toString().split('.').last == map['sync_status'],
        orElse: () => SyncStatus.pending,
      ),
    );
  }

  Future<List<ProgressItem>> getUserProgress(String userId) async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'progress',
      where: 'user_id = ?',
      whereArgs: <Object?>[userId],
      orderBy: 'last_updated DESC',
    );

    return maps.map((Map<String, dynamic> map) {
      return ProgressItem(
        id: map['id'] as String,
        userId: map['user_id'] as String,
        itemId: map['item_id'] as String,
        positionSeconds: map['position_seconds'] as double,
        lastUpdated: DateTime.parse(map['last_updated'] as String),
        syncStatus: SyncStatus.values.firstWhere(
          (SyncStatus status) =>
              status.toString().split('.').last == map['sync_status'],
          orElse: () => SyncStatus.pending,
        ),
      );
    }).toList();
  }

  // History methods
  Future<String> addHistoryItem({
    required String userId,
    required String itemId,
    required ActionType actionType,
  }) async {
    final Database db = await database;
    final String id = const Uuid().v4();
    final DateTime now = DateTime.now();

    await db.insert(
      'history',
      <String, Object?>{
        'id': id,
        'user_id': userId,
        'item_id': itemId,
        'action_type': actionType.toJson(),
        'timestamp': now.toIso8601String(),
        'sync_status': SyncStatus.pending.toString().split('.').last,
      },
    );

    return id;
  }

  Future<List<HistoryItem>> getUserHistory(String userId) async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'history',
      where: 'user_id = ?',
      whereArgs: <Object?>[userId],
      orderBy: 'timestamp DESC',
    );

    return maps.map((Map<String, dynamic> map) {
      return HistoryItem(
        id: map['id'] as String,
        userId: map['user_id'] as String,
        itemId: map['item_id'] as String,
        actionType: ActionType.fromString(map['action_type'] as String?),
        timestamp: DateTime.parse(map['timestamp'] as String),
        syncStatus: SyncStatus.values.firstWhere(
          (SyncStatus status) =>
              status.toString().split('.').last == map['sync_status'],
          orElse: () => SyncStatus.pending,
        ),
      );
    }).toList();
  }

  // Sync methods
  Future<List<FavoriteItem>> getPendingFavoritesSync() async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      where: 'sync_status = ?',
      whereArgs: <Object?>[SyncStatus.pending.toString().split('.').last],
    );

    return maps.map((Map<String, dynamic> map) {
      return FavoriteItem(
        id: map['id'] as String,
        userId: map['user_id'] as String,
        itemId: map['item_id'] as String,
        type: MediaType.fromString(map['type'] as String),
        createdAt: DateTime.parse(map['created_at'] as String),
        // Default value for syncStatus is already SyncStatus.pending
      );
    }).toList();
  }

  Future<List<ProgressItem>> getPendingProgressSync() async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'progress',
      where: 'sync_status = ?',
      whereArgs: <Object?>[SyncStatus.pending.toString().split('.').last],
    );

    return maps.map((Map<String, dynamic> map) {
      return ProgressItem(
        id: map['id'] as String,
        userId: map['user_id'] as String,
        itemId: map['item_id'] as String,
        positionSeconds: map['position_seconds'] as double,
        lastUpdated: DateTime.parse(map['last_updated'] as String),
        // Default value for syncStatus is already SyncStatus.pending
      );
    }).toList();
  }

  Future<List<HistoryItem>> getPendingHistorySync() async {
    final Database db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'history',
      where: 'sync_status = ?',
      whereArgs: <Object?>[SyncStatus.pending.toString().split('.').last],
    );

    return maps.map((Map<String, dynamic> map) {
      return HistoryItem(
        id: map['id'] as String,
        userId: map['user_id'] as String,
        itemId: map['item_id'] as String,
        actionType: ActionType.fromString(map['action_type'] as String?),
        timestamp: DateTime.parse(map['timestamp'] as String),
        // Default value for syncStatus is already SyncStatus.pending
      );
    }).toList();
  }

  Future<void> markAsSynced(String table, String id) async {
    final Database db = await database;

    await db.update(
      table,
      <String, Object?>{
        'sync_status': SyncStatus.synced.toString().split('.').last,
      },
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  // Clear all data
  Future<void> clearDatabase() async {
    final Database db = await database;
    await db.delete('prefs');
    await db.delete('users');
    await db.delete('favorites');
    await db.delete('progress');
    await db.delete('history');
  }

  // Initialize database
  Future<void> initSQLite() async {
    await database;
  }
}
