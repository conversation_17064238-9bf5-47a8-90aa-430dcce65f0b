// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MediaResponse _$MediaResponseFromJson(Map<String, dynamic> json) =>
    _MediaResponse(
      metadata: MediaResponseMetadata.fromJson(
          json['metadata'] as Map<String, dynamic>),
      mediaItems: (json['mediaItems'] as List<dynamic>)
          .map((e) => MediaItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MediaResponseToJson(_MediaResponse instance) =>
    <String, dynamic>{
      'metadata': instance.metadata,
      'mediaItems': instance.mediaItems,
    };

_MediaResponseMetadata _$MediaResponseMetadataFromJson(
        Map<String, dynamic> json) =>
    _MediaResponseMetadata(
      schemaVersion: json['schemaVersion'] as String,
      generatedAt: json['generatedAt'] as String,
      source: json['source'] as String,
      checksum: json['checksum'] as String,
    );

Map<String, dynamic> _$MediaResponseMetadataToJson(
        _MediaResponseMetadata instance) =>
    <String, dynamic>{
      'schemaVersion': instance.schemaVersion,
      'generatedAt': instance.generatedAt,
      'source': instance.source,
      'checksum': instance.checksum,
    };
