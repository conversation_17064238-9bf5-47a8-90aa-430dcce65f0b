import 'dart:math';

import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../enums/media_type_enum.dart';
import '../enums/opentions_format_enum.dart';
import 'media_item.dart';
import 'media_item_converter.dart';
import 'media_metadata.dart';

part 'media_ui_model.freezed.dart';
part 'media_ui_model.g.dart';

@freezed
abstract class MediaUiModel with _$MediaUiModel {
  const factory MediaUiModel({
    required String id,
    required String title,
    required MediaType type, // Changed from String
    required MediaCategory category, // Changed from String
    String? description,
    String? thumbnailUrl,
    // Media URLs - organized by type
    String? audioUrl,
    String? videoUrl,
    String? documentUrl,
    // Content for different media types
    String? articleText,
    String? htmlContent,
    // Tweet-specific fields
    String? tweetContent,
    String? tweetAuthor,
    DateTime? tweetDate,
    String? tweetPostLink,
    String? tweetImageUrl,
    String? tweetUserId,
    String? tweetUserPageLink,
    // Metadata and options
    @MediaMetadataConverter() MediaMetadata? metadata,
    List<MediaOption>? options,
    // File information
    int? fileSize,
    String? fileName,
    // Surah-specific fields (previously in SurahDetails)

    // Reading view fields
  }) = _MediaUiModel;

  factory MediaUiModel.fromJson(Map<String, dynamic> json) =>
      _$MediaUiModelFromJson(json);

  factory MediaUiModel.fromDomain(MediaItem item) {
    // Use extension methods for cleaner code
    final MediaOption? audioOption = item.primaryAudioOption;
    final MediaOption? videoOption = item.primaryVideoOption;
    final MediaOption? documentOption = item.primaryDocumentOption;
    final MediaOption? tweetOption = item.tweetOption;
    final MediaOption? htmlOption = item.htmlOption;

    // Extract HTML content more cleanly
    final String? htmlContent = htmlOption?.html ?? item.metadata?.html;

    // Extract article text using a helper function
    final String? articleText = _extractArticleText(item);

    // Extract tweet data using a helper function
    final Map<String, dynamic> tweetData = _extractTweetData(item, tweetOption);

    return MediaUiModel(
      id: item.id,
      title: item.title,
      type: item.type,
      category: item.category,
      description: item.description,
      thumbnailUrl: item.thumbnailUrl,
      audioUrl: audioOption?.url,
      videoUrl: videoOption?.url,
      documentUrl: documentOption?.url,
      articleText: articleText ?? item.description,
      htmlContent: htmlContent,
      tweetContent: tweetData['content'] as String?,
      tweetAuthor: tweetData['author'] as String?,
      tweetDate: tweetData['date'] as DateTime?,
      tweetPostLink: tweetData['postLink'] as String?,
      tweetImageUrl: tweetData['imageUrl'] as String?,
      tweetUserId: tweetData['userId'] as String?,
      tweetUserPageLink: tweetData['userPageLink'] as String?,
      metadata: _createMetadata(item, htmlContent),
      options: item.options,
      fileSize: audioOption?.fileSize ??
          videoOption?.fileSize ??
          documentOption?.fileSize,
      fileName: audioOption?.fileName ??
          videoOption?.fileName ??
          documentOption?.fileName,
    );
  }

  // Helper method to extract article text
  static String? _extractArticleText(MediaItem item) {
    return item.options
        .where((MediaOption opt) =>
            opt.format == OptionFormat.article || opt.role == 'article')
        .firstOrNull
        ?.description;
  }

  // Helper method to extract tweet data
  static Map<String, dynamic> _extractTweetData(
      MediaItem item, MediaOption? tweetOption) {
    if (tweetOption == null) {
      return <String, dynamic>{};
    }

    return <String, dynamic>{
      'content': tweetOption.postContent ?? tweetOption.description,
      'postLink': tweetOption.postLink,
      'imageUrl': tweetOption.postImage,
      'author': tweetOption.tweetAuthorName ?? 'Dr. Al Farih',
      'userId': tweetOption.tweetAuthorId,
      'userPageLink': tweetOption.tweetAuthorPageLink,
      'date': item.createdAt,
    };
  }

  // Helper method to get author from options
  static String? _getAuthorFromOptions(MediaItem item) {
    final MediaOption authorOption = item.options.firstWhere(
      (MediaOption opt) => opt.author != null && opt.author!.isNotEmpty,
      orElse: () =>
          const MediaOption(optionId: '', format: OptionFormat.unknown),
    );

    if (authorOption != null && authorOption.optionId.isNotEmpty) {
      return authorOption.author;
    }
    return null;
  }

  // Helper method to create metadata
  static MediaMetadata _createMetadata(MediaItem item, String? htmlContent) {
    return MediaMetadata(
      description: item.description,
      html: htmlContent,
      additionalInfo: <String, dynamic>{
        'language': item.language,
        'duration': item.durationSeconds,
        'license': item.license,
        'createdAt': item.createdAt.toIso8601String(),
        'updatedAt': item.updatedAt.toIso8601String(),
      },
    );
  }
}

/// Extension to add helper methods to MediaUiModel
extension MediaUiModelExtensions on MediaUiModel {
  // Type checking helpers
  MediaType get mediaType => type; // Direct access
  bool get isAudio => type == MediaType.audio;
  bool get isVideo => type == MediaType.video;
  bool get isPdf => type == MediaType.pdf;
  bool get isDocument => type == MediaType.document;
  bool get isText => type == MediaType.text;
  bool get isTweetMediaType => type == MediaType.tweet; // Renamed
  bool get isHtmlMediaType => type == MediaType.html; // Renamed

  // Content availability checks
  bool get hasAudioContent => audioUrl != null && audioUrl!.isNotEmpty;
  bool get hasVideoContent => videoUrl != null && videoUrl!.isNotEmpty;
  bool get hasDocumentContent => documentUrl != null && documentUrl!.isNotEmpty;
  bool get hasHtmlContent => htmlContent != null && htmlContent!.isNotEmpty;
  bool get hasTweetContent => tweetContent != null && tweetContent!.isNotEmpty;
  bool get hasArticleText => articleText != null && articleText!.isNotEmpty;

  // Get primary content URL based on type
  String? get primaryContentUrl {
    switch (mediaType) {
      case MediaType.audio:
        return audioUrl;
      case MediaType.video:
        return videoUrl;
      case MediaType.pdf:
      case MediaType.document:
        return documentUrl;
      case MediaType.tweet:
        return tweetPostLink;
      case MediaType.text:
      case MediaType.html:
        return documentUrl ?? audioUrl ?? videoUrl;
      case MediaType.unknown:
        // For unknown types, try to determine the best URL based on what's available
        if (documentUrl != null && documentUrl!.isNotEmpty) {
          return documentUrl;
        } else if (audioUrl != null && audioUrl!.isNotEmpty) {
          return audioUrl;
        } else if (videoUrl != null && videoUrl!.isNotEmpty) {
          return videoUrl;
        } else if (tweetPostLink != null && tweetPostLink!.isNotEmpty) {
          return tweetPostLink;
        } else {
          // Try to find any URL in options
          final String? optionUrl = options
              ?.firstWhereOrNull(
                (MediaOption opt) => opt.url != null && opt.url!.isNotEmpty,
              )
              ?.url;

          return optionUrl;
        }
    }
  }

  // Get display content based on type
  String? get displayContent {
    switch (mediaType) {
      case MediaType.tweet:
        return tweetContent;
      case MediaType.text:
      case MediaType.html:
        return htmlContent ?? articleText;
      case MediaType.audio:
      case MediaType.video:
      case MediaType.pdf:
      case MediaType.document:
        return articleText ?? description;
      case MediaType.unknown:
        // For unknown types, try to find the best content to display
        if (htmlContent != null && htmlContent!.isNotEmpty) {
          return htmlContent;
        } else if (articleText != null && articleText!.isNotEmpty) {
          return articleText;
        } else if (tweetContent != null && tweetContent!.isNotEmpty) {
          return tweetContent;
        } else {
          return description;
        }
    }
  }

  // Check if media has any playable content
  bool get hasPlayableContent => hasAudioContent || hasVideoContent;

  // Check if media has readable content
  bool get hasReadableContent =>
      hasHtmlContent || hasArticleText || hasTweetContent;

  // Get duration from metadata
  int? get durationSeconds {
    return metadata?.additionalInfo?['duration'] as int?;
  }

  // Get created date from metadata
  DateTime? get createdAt {
    final String? dateStr = metadata?.additionalInfo?['createdAt'] as String?;
    if (dateStr == null) {
      return null;
    }
    return DateTime.tryParse(dateStr);
  }

  // Get formatted duration if available
  String? get formattedDuration {
    final int? duration = durationSeconds;
    if (duration == null || duration <= 0) {
      return null;
    }

    final int minutes = duration ~/ 60;
    final int seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Duration? get durationSecondsAsDuration {
    final int? currentDurationSeconds =
        durationSeconds; // Access the existing field
    if (currentDurationSeconds == null || currentDurationSeconds <= 0) {
      return null;
    }
    return Duration(seconds: currentDurationSeconds);
  }

  String? get formattedFileSize {
    // Renamed for clarity and correct return type
    final int? currentFileSize =
        fileSize; // 'fileSize' should be a field in your class/object
    const int decimals = 1; // Or make this a parameter if you need variability

    if (currentFileSize == null || currentFileSize <= 0) {
      return '0 B'; // Or return null if you prefer for invalid/zero sizes
    }

    const List<String> suffixes = <String>[
      'B',
      'KB',
      'MB',
      'GB',
      'TB',
      'PB',
      'EB',
      'ZB',
      'YB'
    ];

    if (currentFileSize <= 0) {
      return '0 B';
    }

    final int i = (log(currentFileSize) / log(1024)).floor();

    if (i >= suffixes.length || i < 0) {
      if (i >= suffixes.length) {
        return '${(currentFileSize / pow(1024, suffixes.length - 1)).toStringAsFixed(decimals)} ${suffixes[suffixes.length - 1]}';
      }
      return '$currentFileSize B'; // Fallback for unexpected 'i'
    }

    final double sizeInUnit = currentFileSize / pow(1024, i);

    return '${suffixes[i]} ${sizeInUnit.toStringAsFixed(decimals)} ';
  }

  // Get all available download options
  List<MediaOption> get downloadableOptions {
    return options?.where((MediaOption opt) => opt.hasValidContent).toList() ??
        <MediaOption>[];
  }

  // Get best quality audio option
  MediaOption? get bestAudioOption {
    final List<MediaOption> audioOptions =
        options?.where((MediaOption opt) => opt.isAudio).toList() ??
            <MediaOption>[];
    if (audioOptions.isEmpty) {
      return null;
    }

    audioOptions.sort((MediaOption a, MediaOption b) =>
        b.qualityScore.compareTo(a.qualityScore));
    return audioOptions.first;
  }

  // Get best quality video option
  MediaOption? get bestVideoOption {
    final List<MediaOption> videoOptions =
        options?.where((MediaOption opt) => opt.isVideo).toList() ??
            <MediaOption>[];
    if (videoOptions.isEmpty) {
      return null;
    }

    videoOptions.sort((MediaOption a, MediaOption b) =>
        b.qualityScore.compareTo(a.qualityScore));
    return videoOptions.first;
  }

  // Validation
  bool get isValid =>
      id.isNotEmpty &&
      title.isNotEmpty &&
      (hasPlayableContent || hasReadableContent || hasDocumentContent);

  // Search helper
  bool matchesQuery(String query) {
    final String lowerQuery = query.toLowerCase();
    return title.toLowerCase().contains(lowerQuery) ||
        (description?.toLowerCase().contains(lowerQuery) ?? false) ||
        (articleText?.toLowerCase().contains(lowerQuery) ?? false) ||
        (tweetContent?.toLowerCase().contains(lowerQuery) ?? false);
  }
}
