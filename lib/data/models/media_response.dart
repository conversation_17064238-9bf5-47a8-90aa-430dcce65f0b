import 'package:freezed_annotation/freezed_annotation.dart';

import 'media_item.dart';

part 'media_response.freezed.dart';
part 'media_response.g.dart';

@freezed
abstract class MediaResponse with _$MediaResponse {
  const factory MediaResponse({
    required MediaResponseMetadata metadata,
    required List<MediaItem> mediaItems,
  }) = _MediaResponse;

  factory MediaResponse.fromJson(Map<String, dynamic> json) =>
      _$MediaResponseFromJson(json);
}

@freezed
abstract class MediaResponseMetadata with _$MediaResponseMetadata {
  const factory MediaResponseMetadata({
    required String schemaVersion,
    required String generatedAt,
    required String source,
    required String checksum,
  }) = _MediaResponseMetadata;

  factory MediaResponseMetadata.fromJson(Map<String, dynamic> json) =>
      _$MediaResponseMetadataFromJson(json);
}
