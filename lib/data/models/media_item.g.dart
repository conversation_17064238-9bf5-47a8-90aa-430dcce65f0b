// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MediaItem _$MediaItemFromJson(Map<String, dynamic> json) => _MediaItem(
      id: json['id'] as String,
      slug: json['slug'] as String,
      type: $enumDecode(_$MediaTypeEnumMap, json['type']),
      category: $enumDecode(_$MediaCategoryEnumMap, json['category']),
      title: json['title'] as String,
      description: json['description'] as String?,
      language: json['language'] as String,
      searchableText: json['searchableText'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      durationSeconds: (json['durationSeconds'] as num?)?.toInt(),
      thumbnailUrl: json['thumbnailUrl'] as String?,
      license: json['license'] as String?,
      options: (json['options'] as List<dynamic>)
          .map((e) => MediaOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      metadata: const MediaMetadataConverter()
          .fromJson(json['metadata'] as Map<String, dynamic>?),
    );

Map<String, dynamic> _$MediaItemToJson(_MediaItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'slug': instance.slug,
      'type': instance.type,
      'category': instance.category,
      'title': instance.title,
      'description': instance.description,
      'language': instance.language,
      'searchableText': instance.searchableText,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'durationSeconds': instance.durationSeconds,
      'thumbnailUrl': instance.thumbnailUrl,
      'license': instance.license,
      'options': instance.options,
      'metadata': const MediaMetadataConverter().toJson(instance.metadata),
    };

const _$MediaTypeEnumMap = {
  MediaType.audio: 'audio',
  MediaType.video: 'video',
  MediaType.text: 'text',
  MediaType.pdf: 'pdf',
  MediaType.document: 'document',
  MediaType.tweet: 'tweet',
  MediaType.html: 'html',
  MediaType.unknown: 'unknown',
};

const _$MediaCategoryEnumMap = {
  MediaCategory.lessons: 'lessons',
  MediaCategory.sermons: 'sermons',
  MediaCategory.lectures: 'lectures',
  MediaCategory.radio: 'radio',
  MediaCategory.youtubeVideos: 'youtube_videos',
  MediaCategory.books: 'books',
  MediaCategory.htmlArticles: 'html_articles',
  MediaCategory.recentTweets: 'recent_tweets',
  MediaCategory.photos: 'photos',
  MediaCategory.general: 'general',
  MediaCategory.tweetCategory: 'tweet_category',
  MediaCategory.unknown: 'unknown',
};

_MediaOption _$MediaOptionFromJson(Map<String, dynamic> json) => _MediaOption(
      optionId: json['optionId'] as String,
      format: _formatFromJson(json['format']),
      url: json['url'] as String?,
      description: json['description'] as String?,
      role: json['role'] as String?,
      bitrate: (json['bitrate'] as num?)?.toInt(),
      html: json['html'] as String?,
      postLink: json['post_link'] as String?,
      postContent: json['post_content'] as String?,
      userInfo: json['user_info'] as Map<String, dynamic>?,
      postImage: json['post_image'] as String?,
      author: json['author'] as String?,
      section: json['section'] as String?,
      publisher: json['publisher'] as String?,
      releaseDate: json['releaseDate'] as String?,
      totalPages: json['totalPages'],
      fileSize: (json['fileSize'] as num?)?.toInt(),
      fileName: json['fileName'] as String?,
    );

Map<String, dynamic> _$MediaOptionToJson(_MediaOption instance) =>
    <String, dynamic>{
      'optionId': instance.optionId,
      'format': _formatToJson(instance.format),
      'url': instance.url,
      'description': instance.description,
      'role': instance.role,
      'bitrate': instance.bitrate,
      'html': instance.html,
      'post_link': instance.postLink,
      'post_content': instance.postContent,
      'user_info': instance.userInfo,
      'post_image': instance.postImage,
      'author': instance.author,
      'section': instance.section,
      'publisher': instance.publisher,
      'releaseDate': instance.releaseDate,
      'totalPages': instance.totalPages,
      'fileSize': instance.fileSize,
      'fileName': instance.fileName,
    };
