import 'package:freezed_annotation/freezed_annotation.dart';

import '../enums/media_type_enum.dart'; // Import enums
import '../enums/opentions_format_enum.dart';
import 'media_item_converter.dart';
import 'media_metadata.dart';

part 'media_item.freezed.dart';
part 'media_item.g.dart';

@freezed
abstract class MediaItem with _$MediaItem {
  const factory MediaItem({
    required String id,
    required String slug,
    required MediaType type, // Changed from String
    required MediaCategory category, // Changed from String
    required String title,
    String? description,
    required String language,
    required String searchableText,
    required DateTime createdAt,
    required DateTime updatedAt,
    int? durationSeconds,
    String? thumbnailUrl,
    String? license,
    required List<MediaOption> options,
    @MediaMetadataConverter() MediaMetadata? metadata,
  }) = _MediaItem;

  factory MediaItem.fromJson(Map<String, dynamic> json) =>
      _$MediaItemFromJson(json);
}

@freezed
abstract class MediaOption with _$MediaOption {
  const factory MediaOption({
    required String optionId,
    @JsonKey(fromJson: _formatFromJson, toJson: _formatToJson)
    required OptionFormat format,
    String? url,
    String? description,
    String? role,
    int? bitrate,
    // HTML content field
    String? html,
    // New fields for tweets
    @JsonKey(name: 'post_link') String? postLink,
    @JsonKey(name: 'post_content') String? postContent,
    @JsonKey(name: 'user_info') Map<String, dynamic>? userInfo,
    @JsonKey(name: 'post_image') String? postImage,
    // Book-specific fields
    String? author,
    String? section,
    String? publisher,
    String? releaseDate,
    dynamic totalPages,
    // File information
    int? fileSize,
    String? fileName,
  }) = _MediaOption;

  factory MediaOption.fromJson(Map<String, dynamic> json) =>
      _$MediaOptionFromJson(json);
}

// Custom JSON conversion functions for OptionFormat
OptionFormat _formatFromJson(dynamic json) {
  if (json == null) {
    return OptionFormat.unknown;
  }
  if (json is String) {
    return OptionFormat.fromString(json);
  }
  return OptionFormat.unknown;
}

String _formatToJson(OptionFormat format) {
  return format.toJson();
}

/// Extension to add helper methods to MediaOption
extension MediaOptionExtensions on MediaOption {
  // Check format types using OptionFormat helper methods
  bool get isAudio => format.isAudio;
  bool get isVideo => format.isVideo;
  bool get isPdf => format == OptionFormat.pdf;
  bool get isDocument => format.isDocument;
  bool get isHtml => format == OptionFormat.html;
  bool get isTweet => format == OptionFormat.tweet;
  bool get isImage => format.isImage;
  bool get isArticle => format == OptionFormat.article;

  // Get quality indicator (higher is better)
  int get qualityScore {
    if (bitrate != null) {
      return bitrate!;
    }
    final String formatStr = format.toJson();
    if (formatStr.contains('hd') || formatStr.contains('1080')) {
      return 1000;
    }
    if (formatStr.contains('720')) {
      return 720;
    }
    return 0;
  }

  // Check if this option has valid content
  bool get hasValidContent {
    return (url != null && url!.isNotEmpty) ||
        (html != null && html!.isNotEmpty) ||
        (postContent != null && postContent!.isNotEmpty);
  }

  // Get tweet author name safely
  String? get tweetAuthorName {
    if (userInfo == null) {
      return null;
    }
    return userInfo!['name'] as String?;
  }

  // Get tweet author ID safely
  String? get tweetAuthorId {
    if (userInfo == null) {
      return null;
    }
    return userInfo!['id'] as String?;
  }

  // Get tweet author page link safely
  String? get tweetAuthorPageLink {
    if (userInfo == null) {
      return null;
    }
    return userInfo!['page_link'] as String?;
  }
}

/// Extension to add helper methods to MediaItem
extension MediaItemExtensions on MediaItem {
  // Computed properties for better type safety
  MediaType get mediaType => type; // Direct access

  // Helper methods for common operations
  bool get hasAudio => options.any((MediaOption opt) => opt.isAudio);
  bool get hasVideo => options.any((MediaOption opt) => opt.isVideo);
  bool get hasPdf => options.any((MediaOption opt) => opt.isPdf);
  bool get hasHtml => options.any((MediaOption opt) => opt.isHtml);
  bool get isTweetMedia => mediaType == MediaType.tweet; // Renamed for clarity

  // Get specific option types using enum-based filtering
  MediaOption? get primaryAudioOption =>
      options.where((MediaOption opt) => opt.isAudio).firstOrNull;
  MediaOption? get primaryVideoOption =>
      options.where((MediaOption opt) => opt.isVideo).firstOrNull;

  // Prioritize PDF files over other document types
  MediaOption? get primaryDocumentOption {
    final List<MediaOption> documentOptions =
        options.where((MediaOption opt) => opt.isDocument).toList();

    if (documentOptions.isEmpty) {
      return null;
    }

    // First, try to get the best PDF option
    final MediaOption? bestPdf = bestPdfOption;
    if (bestPdf != null) {
      return bestPdf;
    }

    // If no PDF, return the first document option
    return documentOptions.first;
  }

  MediaOption? get tweetOption => options
      .where((MediaOption opt) => opt.format == OptionFormat.tweet)
      .firstOrNull;
  MediaOption? get htmlOption => options
      .where((MediaOption opt) => opt.format == OptionFormat.html)
      .firstOrNull;

  // Validation methods
  bool get isValid => id.isNotEmpty && title.isNotEmpty && options.isNotEmpty;

  // Search helper
  bool matchesQuery(String query) {
    final String lowerQuery = query.toLowerCase();
    return title.toLowerCase().contains(lowerQuery) ||
        (description?.toLowerCase().contains(lowerQuery) ?? false) ||
        searchableText.toLowerCase().contains(lowerQuery);
  }

  // Get all URLs for a specific format type
  List<String> getUrlsForFormat(OptionFormat format) {
    return options
        .where((MediaOption opt) => opt.format == format)
        .map((MediaOption opt) => opt.url)
        .where((String? url) => url != null && url.isNotEmpty)
        .cast<String>()
        .toList();
  }

  // Get all URLs for audio formats
  List<String> get audioUrls {
    return options
        .where((MediaOption opt) => opt.isAudio)
        .map((MediaOption opt) => opt.url)
        .where((String? url) => url != null && url.isNotEmpty)
        .cast<String>()
        .toList();
  }

  // Get all URLs for video formats
  List<String> get videoUrls {
    return options
        .where((MediaOption opt) => opt.isVideo)
        .map((MediaOption opt) => opt.url)
        .where((String? url) => url != null && url.isNotEmpty)
        .cast<String>()
        .toList();
  }

  // Get the best quality option for a specific format category
  MediaOption? getBestQualityOption(String mimeCategory) {
    final List<MediaOption> matchingOptions = options
        .where((MediaOption opt) => opt.format.mimeCategory == mimeCategory)
        .toList();

    if (matchingOptions.isEmpty) {
      return null;
    }

    // Sort by bitrate (higher is better) or return first if no bitrate info
    matchingOptions.sort((MediaOption a, MediaOption b) {
      if (a.bitrate == null && b.bitrate == null) {
        return 0;
      }
      if (a.bitrate == null) {
        return 1;
      }
      if (b.bitrate == null) {
        return -1;
      }
      return b.bitrate!.compareTo(a.bitrate!);
    });

    return matchingOptions.first;
  }

  // Get the best quality audio option
  MediaOption? get bestAudioOption => getBestQualityOption('audio');

  // Get the best quality video option
  MediaOption? get bestVideoOption => getBestQualityOption('video');

  // Get the best PDF option (prioritizes by metadata like page count, then by order)
  MediaOption? get bestPdfOption {
    final List<MediaOption> pdfOptions =
        options.where((MediaOption opt) => opt.isPdf).toList();

    if (pdfOptions.isEmpty) {
      return null;
    }

    if (pdfOptions.length == 1) {
      return pdfOptions.first;
    }

    // Sort by metadata quality - prefer options with more metadata
    pdfOptions.sort((MediaOption a, MediaOption b) {
      // Prioritize options with page count information
      final bool aHasPages = a.totalPages != null;
      final bool bHasPages = b.totalPages != null;

      if (aHasPages && !bHasPages) {
        return -1;
      }
      if (!aHasPages && bHasPages) {
        return 1;
      }

      // If both have page info, prefer the one with more pages (likely more complete)
      if (aHasPages && bHasPages) {
        final int aPages = a.totalPages is int ? a.totalPages as int : 0;
        final int bPages = b.totalPages is int ? b.totalPages as int : 0;
        if (aPages != bPages) {
          return bPages.compareTo(aPages);
        }
      }

      // Prioritize options with author information
      final bool aHasAuthor = a.author != null && a.author!.isNotEmpty;
      final bool bHasAuthor = b.author != null && b.author!.isNotEmpty;

      if (aHasAuthor && !bHasAuthor) {
        return -1;
      }
      if (!aHasAuthor && bHasAuthor) {
        return 1;
      }

      // If all else is equal, maintain original order
      return 0;
    });

    return pdfOptions.first;
  }
}
