import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:just_audio/just_audio.dart';

import '../models/media_ui_model.dart';
import 'media_item.dart';

part 'media_player_state.freezed.dart';

@freezed
abstract class MediaPlayerState with _$MediaPlayerState {
  const factory MediaPlayerState({
    required MediaUiModel? mediaItem,
    required bool isLoading,
    required bool isPlaying,
    required bool isBuffering,
    required Duration currentPosition,
    required Duration totalDuration,
    required double volume,
    required double playbackSpeed,
    String? errorMessage,
    @Default(false) bool? isDownloading,
    double? downloadProgress,
    // PDF specific states
    int? currentPage,
    int? totalPages,
    double? pdfZoom,
    // Article specific states
    double? articleScrollPosition,
    double? articleFontSize,
    bool? isDarkMode,
    // Tweet specific states
    bool? isLiked,
    int? likeCount,
    int? retweetCount,
    // Document states
    String? documentLoadError,
    bool? isDocumentLoaded,
    int? currentViewIndex,
    @Default(LoopMode.off) LoopMode loopMode,
    List<MediaOption>? mediaOptions,
    String? downloadedFilePath,
  }) = _MediaPlayerState;

  factory MediaPlayerState.initial() => const MediaPlayerState(
        mediaItem: null,
        isLoading: true,
        isPlaying: false,
        isBuffering: false,
        currentPosition: Duration.zero,
        totalDuration: Duration.zero,
        volume: 1.0,
        playbackSpeed: 1.0,
        downloadProgress: 0.0,
        currentPage: 1,
        totalPages: 0,
        pdfZoom: 1.0,
        articleScrollPosition: 0.0,
        articleFontSize: 16.0,
        isDarkMode: false,
        isLiked: false,
        likeCount: 0,
        retweetCount: 0,
        isDocumentLoaded: false,
        currentViewIndex: 0,
      );
}
