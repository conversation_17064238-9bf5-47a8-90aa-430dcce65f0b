import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../routing/app_router.dart';
import '../../enums/media_type_enum.dart';
import '../../enums/opentions_format_enum.dart';
import '../media_item.dart';
import '../media_ui_model.dart';
import 'media_provider.dart';

/// Helper class for media navigation operations

/// Navigates to the appropriate media player based on the media type
class MediaNavigationHelper {
  const MediaNavigationHelper();

  void navigateToMediaPlayer(
    BuildContext context,
    MediaUiModel mediaItem, {
    WidgetRef? ref,
  }) {
    final String mediaId = mediaItem.id;
    final MediaType mediaType = mediaItem.type;
    final MediaCategory mediaCategory = mediaItem.category;

    // Update the selected media if ref is provided
    if (ref != null) {
      ref.read(selectedMediaNotifierProvider.notifier).selectedMedia = mediaId;
    }

    if (mediaType == MediaType.audio ||
        (mediaItem.audioUrl != null && mediaItem.audioUrl!.isNotEmpty)) {
      debugPrint(
          'Navigating to SurahPlayerScreen (named route) for audio ID: $mediaId');

      context.pushNamed(
        'surahPlayer',
        pathParameters: <String, String>{'id': mediaId},
      );
    } else if (mediaType == MediaType.video ||
        (mediaItem.videoUrl != null && mediaItem.videoUrl!.isNotEmpty)) {
      debugPrint('Navigating to MediaPlayerScreen for video ID: $mediaId');
      // You might also want to use pushNamed here if 'mediaPlayer' is a named route
      // context.pushNamed(
      //   'mediaPlayer',
      //   pathParameters: <String, String>{'id': mediaId},
      // );
      context.pushNamed(
        'surahPlayer',
        pathParameters: <String, String>{'id': mediaId},
      );
    } else if (mediaType == MediaType.tweet ||
        (mediaType == MediaType.text &&
            mediaCategory == MediaCategory.tweetCategory) ||
        mediaItem.tweetContent != null) {
      debugPrint('Navigating to tweet details with ID: $mediaId');
      context.pushNamed('tweetDetails',
          pathParameters: <String, String>{'id': mediaId});
    } else if (mediaType.isDocument ||
        (mediaType == MediaType.text &&
            mediaCategory != MediaCategory.htmlArticles) ||
        (mediaItem.documentUrl != null && mediaItem.documentUrl!.isNotEmpty)) {
      debugPrint('Navigating to text media viewer with ID: $mediaId');
      context.pushNamed(
        SGRoute.pdfInfo.name,
        pathParameters: <String, String>{'id': mediaId},
      );
    } else if ((mediaType == MediaType.text || mediaType == MediaType.html) &&
        (mediaCategory == MediaCategory.htmlArticles ||
            (mediaItem.metadata != null && mediaItem.metadata!.html != null) ||
            _hasHtmlContent(mediaItem))) {
      debugPrint(
          'Navigating to article reader (via textMediaViewer) with ID: $mediaId');
      context.pushNamed(
        SGRoute.textMediaViewer.name,
        pathParameters: <String, String>{'id': mediaId},
      );
    } else if (mediaType == MediaType.unknown) {
      // Handle unknown media type specifically
      debugPrint('Handling unknown media type for ID: $mediaId');

      // Try to determine the best route based on available content
      if (mediaItem.documentUrl != null && mediaItem.documentUrl!.isNotEmpty) {
        debugPrint(
            'Unknown media type has document URL, navigating to PDF viewer');
        context.pushNamed(
          SGRoute.pdfInfo.name,
          pathParameters: <String, String>{'id': mediaId},
        );
      } else if (mediaItem.audioUrl != null && mediaItem.audioUrl!.isNotEmpty) {
        debugPrint(
            'Unknown media type has audio URL, navigating to audio player');
        context.pushNamed(
          'surahPlayer',
          pathParameters: <String, String>{'id': mediaId},
        );
      } else if (mediaItem.videoUrl != null && mediaItem.videoUrl!.isNotEmpty) {
        debugPrint(
            'Unknown media type has video URL, navigating to video player');
        context.pushNamed(
          'surahPlayer',
          pathParameters: <String, String>{'id': mediaId},
        );
      } else {
        // If we can't determine a good route, show an error dialog
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('نوع الوسائط غير معروف'),
              content: Text(
                  'نوع الوسائط "${mediaItem.type.name}" غير معروف ولا يمكن تحديد كيفية عرضه.'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('حسنًا'),
                ),
              ],
            );
          },
        );
      }
    } else {
      // For other unsupported media types
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('نوع الوسائط غير مدعوم'),
            content: Text(
                'نوع الوسائط "${mediaItem.type.name}" غير مدعوم للتشغيل مباشرة هنا.'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسنًا'),
              ),
            ],
          );
        },
      );
    }
  }

  /// Checks if a media item has HTML content
  bool _hasHtmlContent(MediaUiModel mediaItem) {
    // Check if the category is 'html'
    if (mediaItem.category == MediaCategory.htmlArticles) {
      return true;
    }

    // Check if we have HTML content in metadata
    if (mediaItem.metadata != null && mediaItem.metadata!.html != null) {
      return true;
    }

    // Check if we have HTML content in options
    if (mediaItem.options != null && mediaItem.options!.isNotEmpty) {
      for (final MediaOption option in mediaItem.options!) {
        final String format = option.format.toJson().toLowerCase();
        if (option.format == OptionFormat.html || format.contains('html')) {
          return true;
        }

        // Check if option has HTML content
        if (option.html != null && option.html!.isNotEmpty) {
          return true;
        }
      }
    }

    // Check if article text looks like HTML
    if (mediaItem.articleText != null) {
      final String text = mediaItem.articleText!.trim().toLowerCase();
      if (text.startsWith('<') &&
          (text.contains('</html>') ||
              text.contains('</body>') ||
              text.contains('</div>') ||
              text.contains('</p>'))) {
        return true;
      }
    }

    return false;
  }
}
