import 'package:flutter/widgets.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../domain/repositories/media_repository_interface.dart';
import '../../repository/media_repository.dart';
import '../media_item.dart';
import '../media_ui_model.dart';

part 'media_provider.g.dart';

/// 1) AsyncNotifier for loading raw domain items
/// This provider should be kept alive since it's the source of truth for media data
/// and is used across multiple screens
@Riverpod(keepAlive: true)
class MediaList extends _$MediaList {
  @override
  Future<List<MediaItem>> build() async {
    final MediaRepositoryInterface repo = ref.read(mediaRepositoryProvider);
    try {
      final List<MediaItem> items = await repo.getAllMediaItems();
      return items;
    } catch (e) {
      debugPrint('mediaListProvider: Error loading items: $e');
      // allow UI to show error
      throw Exception('Failed to load media items: $e');
    }
  }
}

/// 2) AsyncNotifier for converting domain → UI models
/// This provider should be kept alive since it's derived from MediaList
/// and used across multiple screens
@Riverpod(keepAlive: true)
class MediaUiList extends _$MediaUiList {
  @override
  Future<List<MediaUiModel>> build() async {
    final List<MediaItem> items = await ref.watch(mediaListProvider.future);

    final List<MediaUiModel> uiModels =
        items.map(MediaUiModel.fromDomain).toList();
    return uiModels;
  }
}

/// 3) Simple String state for search query
/// Using autoDispose since search state doesn't need to persist across app restarts
/// and should be reset when the user navigates away from the search screen
@riverpod
class MediaSearch extends _$MediaSearch {
  @override
  String build() => '';

  // Getter for the search query value
  String get value => state;

  // Setter for the search query value
  set value(String query) {
    // Debounce logic could be added here for better performance
    // with large datasets, but keeping it simple for now

    // Only update state if the value has changed
    if (state != query) {
      state = query;
    }
  }

  // Method to reset the search query
  void reset() {
    // Only reset if not already empty
    if (state.isNotEmpty) {
      state = '';
    }
  }
}

/// 4) Computed provider that filters the UI list by the search query
/// Using autoDispose since it depends on the search query which is also autoDispose
@riverpod
List<MediaUiModel> filteredMedia(Ref ref) {
  // Watch the search query with a try-catch to handle potential circular dependencies
  String query;
  try {
    query = ref.watch(mediaSearchProvider).toLowerCase();
  } catch (e) {
    debugPrint('filteredMedia: Error watching mediaSearchProvider: $e');
    query = ''; // Default to empty query if there's an error
  }

  // Watch the UI list provider with error handling
  try {
    return ref.watch(mediaUiListProvider).when(
      data: (List<MediaUiModel> data) {
        // If query is empty, return all data without filtering
        if (query.isEmpty) {
          return data;
        }

        // Use matchesQuery method from MediaUiModelExtensions for more efficient filtering
        final List<MediaUiModel> filtered = data
            .where((MediaUiModel item) => item.matchesQuery(query))
            .toList();

        return filtered;
      },
      loading: () {
        return <MediaUiModel>[];
      },
      error: (Object error, StackTrace stackTrace) {
        debugPrint('filteredMedia: Error state - $error');
        return <MediaUiModel>[];
      },
    );
  } catch (e) {
    debugPrint('filteredMedia: Error watching mediaUiListProvider: $e');
    return <MediaUiModel>[];
  }
}

/// Provider for the currently selected media item
/// This is kept alive to maintain selection state across screens
@Riverpod(keepAlive: true)
class SelectedMediaNotifier extends _$SelectedMediaNotifier {
  @override
  String? build() {
    return null;
  }

  // Getter for the selected media ID
  String? get selectedMediaId => state;

  // Setter for the selected media ID
  set selectedMedia(String? id) {
    state = id;
  }

  // Method to clear the selected media
  void clearSelection() {
    state = null;
  }

  // Toggle selection (if the same item is selected, deselect it)
  void toggleSelection(String mediaId) {
    state = state == mediaId ? null : mediaId;
  }

  // Reload media (temporarily clear and then reselect)
  void reloadMedia() {
    final String? currentId = state;
    state = null;

    // Use a delay to ensure the widget rebuilds
    Future<void>.delayed(const Duration(milliseconds: 300), () {
      state = currentId;
    });
  }
}

/// Provider that returns the selected media item
@riverpod
Future<MediaUiModel?> selectedMedia(Ref ref) async {
  final String? selectedId = ref.watch(selectedMediaNotifierProvider);

  if (selectedId == null) {
    return null;
  }

  // Get the media repository
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);

  // Fetch the selected media item
  return mediaRepo.getMediaById(selectedId);
}
