// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// 1) AsyncNotifier for loading raw domain items
/// This provider should be kept alive since it's the source of truth for media data
/// and is used across multiple screens
@ProviderFor(MediaList)
const mediaListProvider = MediaListProvider._();

/// 1) AsyncNotifier for loading raw domain items
/// This provider should be kept alive since it's the source of truth for media data
/// and is used across multiple screens
final class MediaListProvider
    extends $AsyncNotifierProvider<MediaList, List<MediaItem>> {
  /// 1) AsyncNotifier for loading raw domain items
  /// This provider should be kept alive since it's the source of truth for media data
  /// and is used across multiple screens
  const MediaListProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaListProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaListHash();

  @$internal
  @override
  MediaList create() => MediaList();

  @$internal
  @override
  $AsyncNotifierProviderElement<MediaList, List<MediaItem>> $createElement(
          $ProviderPointer pointer) =>
      $AsyncNotifierProviderElement(pointer);
}

String _$mediaListHash() => r'9da158824ce6f12383711228661f82386ffc6f03';

abstract class _$MediaList extends $AsyncNotifier<List<MediaItem>> {
  FutureOr<List<MediaItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<MediaItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<MediaItem>>>,
        AsyncValue<List<MediaItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

/// 2) AsyncNotifier for converting domain → UI models
/// This provider should be kept alive since it's derived from MediaList
/// and used across multiple screens
@ProviderFor(MediaUiList)
const mediaUiListProvider = MediaUiListProvider._();

/// 2) AsyncNotifier for converting domain → UI models
/// This provider should be kept alive since it's derived from MediaList
/// and used across multiple screens
final class MediaUiListProvider
    extends $AsyncNotifierProvider<MediaUiList, List<MediaUiModel>> {
  /// 2) AsyncNotifier for converting domain → UI models
  /// This provider should be kept alive since it's derived from MediaList
  /// and used across multiple screens
  const MediaUiListProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaUiListProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaUiListHash();

  @$internal
  @override
  MediaUiList create() => MediaUiList();

  @$internal
  @override
  $AsyncNotifierProviderElement<MediaUiList, List<MediaUiModel>> $createElement(
          $ProviderPointer pointer) =>
      $AsyncNotifierProviderElement(pointer);
}

String _$mediaUiListHash() => r'ff80a5c2b2e794e7a3f8b707724eef1ad881e832';

abstract class _$MediaUiList extends $AsyncNotifier<List<MediaUiModel>> {
  FutureOr<List<MediaUiModel>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<MediaUiModel>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<MediaUiModel>>>,
        AsyncValue<List<MediaUiModel>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

/// 3) Simple String state for search query
/// Using autoDispose since search state doesn't need to persist across app restarts
/// and should be reset when the user navigates away from the search screen
@ProviderFor(MediaSearch)
const mediaSearchProvider = MediaSearchProvider._();

/// 3) Simple String state for search query
/// Using autoDispose since search state doesn't need to persist across app restarts
/// and should be reset when the user navigates away from the search screen
final class MediaSearchProvider extends $NotifierProvider<MediaSearch, String> {
  /// 3) Simple String state for search query
  /// Using autoDispose since search state doesn't need to persist across app restarts
  /// and should be reset when the user navigates away from the search screen
  const MediaSearchProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaSearchProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaSearchHash();

  @$internal
  @override
  MediaSearch create() => MediaSearch();

  @$internal
  @override
  $NotifierProviderElement<MediaSearch, String> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<String>(value),
    );
  }
}

String _$mediaSearchHash() => r'6148744df6d15080aa4d0f96c480a90fcf7703f9';

abstract class _$MediaSearch extends $Notifier<String> {
  String build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<String>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<String>, String, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// 4) Computed provider that filters the UI list by the search query
/// Using autoDispose since it depends on the search query which is also autoDispose
@ProviderFor(filteredMedia)
const filteredMediaProvider = FilteredMediaProvider._();

/// 4) Computed provider that filters the UI list by the search query
/// Using autoDispose since it depends on the search query which is also autoDispose
final class FilteredMediaProvider
    extends $FunctionalProvider<List<MediaUiModel>, List<MediaUiModel>>
    with $Provider<List<MediaUiModel>> {
  /// 4) Computed provider that filters the UI list by the search query
  /// Using autoDispose since it depends on the search query which is also autoDispose
  const FilteredMediaProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'filteredMediaProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$filteredMediaHash();

  @$internal
  @override
  $ProviderElement<List<MediaUiModel>> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<MediaUiModel> create(Ref ref) {
    return filteredMedia(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<MediaUiModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<List<MediaUiModel>>(value),
    );
  }
}

String _$filteredMediaHash() => r'5881cc74528a97bb25b3ac204dd7633e00727f1b';

/// Provider for the currently selected media item
/// This is kept alive to maintain selection state across screens
@ProviderFor(SelectedMediaNotifier)
const selectedMediaNotifierProvider = SelectedMediaNotifierProvider._();

/// Provider for the currently selected media item
/// This is kept alive to maintain selection state across screens
final class SelectedMediaNotifierProvider
    extends $NotifierProvider<SelectedMediaNotifier, String?> {
  /// Provider for the currently selected media item
  /// This is kept alive to maintain selection state across screens
  const SelectedMediaNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedMediaNotifierProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedMediaNotifierHash();

  @$internal
  @override
  SelectedMediaNotifier create() => SelectedMediaNotifier();

  @$internal
  @override
  $NotifierProviderElement<SelectedMediaNotifier, String?> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<String?>(value),
    );
  }
}

String _$selectedMediaNotifierHash() =>
    r'787d058c47ab893090a404cfb0f31e6703cc57dc';

abstract class _$SelectedMediaNotifier extends $Notifier<String?> {
  String? build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<String?>;
    final element = ref.element as $ClassProviderElement<AnyNotifier<String?>,
        String?, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Provider that returns the selected media item
@ProviderFor(selectedMedia)
const selectedMediaProvider = SelectedMediaProvider._();

/// Provider that returns the selected media item
final class SelectedMediaProvider extends $FunctionalProvider<
        AsyncValue<MediaUiModel?>, FutureOr<MediaUiModel?>>
    with $FutureModifier<MediaUiModel?>, $FutureProvider<MediaUiModel?> {
  /// Provider that returns the selected media item
  const SelectedMediaProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedMediaProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedMediaHash();

  @$internal
  @override
  $FutureProviderElement<MediaUiModel?> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<MediaUiModel?> create(Ref ref) {
    return selectedMedia(ref);
  }
}

String _$selectedMediaHash() => r'fb61fb0ca9975e7730ba405c7af0ace01f7ada21';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
