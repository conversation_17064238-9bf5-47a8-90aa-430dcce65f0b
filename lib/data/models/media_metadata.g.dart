// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_metadata.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MediaMetadata _$MediaMetadataFromJson(Map<String, dynamic> json) =>
    _MediaMetadata(
      description: json['description'] as String?,
      html: json['html'] as String?,
      likeCount: (json['likeCount'] as num?)?.toInt(),
      retweetCount: (json['retweetCount'] as num?)?.toInt(),
      commentCount: (json['commentCount'] as num?)?.toInt(),
      shareCount: (json['shareCount'] as num?)?.toInt(),
      viewCount: (json['viewCount'] as num?)?.toInt(),
      resolution: json['resolution'] as String?,
      codec: json['codec'] as String?,
      artist: json['artist'] as String?,
      album: json['album'] as String?,
      genre: json['genre'] as String?,
      bitrate: (json['bitrate'] as num?)?.toInt(),
      author: json['author'] as String?,
      publisher: json['publisher'] as String?,
      publishDate: json['publishDate'] == null
          ? null
          : DateTime.parse(json['publishDate'] as String),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$MediaMetadataToJson(_MediaMetadata instance) =>
    <String, dynamic>{
      'description': instance.description,
      'html': instance.html,
      'likeCount': instance.likeCount,
      'retweetCount': instance.retweetCount,
      'commentCount': instance.commentCount,
      'shareCount': instance.shareCount,
      'viewCount': instance.viewCount,
      'resolution': instance.resolution,
      'codec': instance.codec,
      'artist': instance.artist,
      'album': instance.album,
      'genre': instance.genre,
      'bitrate': instance.bitrate,
      'author': instance.author,
      'publisher': instance.publisher,
      'publishDate': instance.publishDate?.toIso8601String(),
      'tags': instance.tags,
      'additionalInfo': instance.additionalInfo,
    };
