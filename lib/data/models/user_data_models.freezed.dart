// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_data_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FavoriteItem {
  String get id;
  String get userId;
  String get itemId;
  MediaType get type;
  DateTime get createdAt;
  SyncStatus get syncStatus;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FavoriteItemCopyWith<FavoriteItem> get copyWith =>
      _$FavoriteItemCopyWithImpl<FavoriteItem>(
          this as FavoriteItem, _$identity);

  /// Serializes this FavoriteItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FavoriteItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, userId, itemId, type, createdAt, syncStatus);

  @override
  String toString() {
    return 'FavoriteItem(id: $id, userId: $userId, itemId: $itemId, type: $type, createdAt: $createdAt, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class $FavoriteItemCopyWith<$Res> {
  factory $FavoriteItemCopyWith(
          FavoriteItem value, $Res Function(FavoriteItem) _then) =
      _$FavoriteItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      MediaType type,
      DateTime createdAt,
      SyncStatus syncStatus});
}

/// @nodoc
class _$FavoriteItemCopyWithImpl<$Res> implements $FavoriteItemCopyWith<$Res> {
  _$FavoriteItemCopyWithImpl(this._self, this._then);

  final FavoriteItem _self;
  final $Res Function(FavoriteItem) _then;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? type = null,
    Object? createdAt = null,
    Object? syncStatus = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as MediaType,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FavoriteItem extends FavoriteItem {
  _FavoriteItem(
      {required this.id,
      required this.userId,
      required this.itemId,
      required this.type,
      required this.createdAt,
      this.syncStatus = SyncStatus.pending})
      : super._();
  factory _FavoriteItem.fromJson(Map<String, dynamic> json) =>
      _$FavoriteItemFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String itemId;
  @override
  final MediaType type;
  @override
  final DateTime createdAt;
  @override
  @JsonKey()
  final SyncStatus syncStatus;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FavoriteItemCopyWith<_FavoriteItem> get copyWith =>
      __$FavoriteItemCopyWithImpl<_FavoriteItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FavoriteItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FavoriteItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, userId, itemId, type, createdAt, syncStatus);

  @override
  String toString() {
    return 'FavoriteItem(id: $id, userId: $userId, itemId: $itemId, type: $type, createdAt: $createdAt, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class _$FavoriteItemCopyWith<$Res>
    implements $FavoriteItemCopyWith<$Res> {
  factory _$FavoriteItemCopyWith(
          _FavoriteItem value, $Res Function(_FavoriteItem) _then) =
      __$FavoriteItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      MediaType type,
      DateTime createdAt,
      SyncStatus syncStatus});
}

/// @nodoc
class __$FavoriteItemCopyWithImpl<$Res>
    implements _$FavoriteItemCopyWith<$Res> {
  __$FavoriteItemCopyWithImpl(this._self, this._then);

  final _FavoriteItem _self;
  final $Res Function(_FavoriteItem) _then;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? type = null,
    Object? createdAt = null,
    Object? syncStatus = null,
  }) {
    return _then(_FavoriteItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as MediaType,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
mixin _$ProgressItem {
  String get id;
  String get userId;
  String get itemId;
  double get positionSeconds;
  DateTime get lastUpdated;
  SyncStatus get syncStatus;

  /// Create a copy of ProgressItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProgressItemCopyWith<ProgressItem> get copyWith =>
      _$ProgressItemCopyWithImpl<ProgressItem>(
          this as ProgressItem, _$identity);

  /// Serializes this ProgressItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProgressItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.positionSeconds, positionSeconds) ||
                other.positionSeconds == positionSeconds) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, itemId,
      positionSeconds, lastUpdated, syncStatus);

  @override
  String toString() {
    return 'ProgressItem(id: $id, userId: $userId, itemId: $itemId, positionSeconds: $positionSeconds, lastUpdated: $lastUpdated, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class $ProgressItemCopyWith<$Res> {
  factory $ProgressItemCopyWith(
          ProgressItem value, $Res Function(ProgressItem) _then) =
      _$ProgressItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      double positionSeconds,
      DateTime lastUpdated,
      SyncStatus syncStatus});
}

/// @nodoc
class _$ProgressItemCopyWithImpl<$Res> implements $ProgressItemCopyWith<$Res> {
  _$ProgressItemCopyWithImpl(this._self, this._then);

  final ProgressItem _self;
  final $Res Function(ProgressItem) _then;

  /// Create a copy of ProgressItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? positionSeconds = null,
    Object? lastUpdated = null,
    Object? syncStatus = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      positionSeconds: null == positionSeconds
          ? _self.positionSeconds
          : positionSeconds // ignore: cast_nullable_to_non_nullable
              as double,
      lastUpdated: null == lastUpdated
          ? _self.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ProgressItem extends ProgressItem {
  _ProgressItem(
      {required this.id,
      required this.userId,
      required this.itemId,
      required this.positionSeconds,
      required this.lastUpdated,
      this.syncStatus = SyncStatus.pending})
      : super._();
  factory _ProgressItem.fromJson(Map<String, dynamic> json) =>
      _$ProgressItemFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String itemId;
  @override
  final double positionSeconds;
  @override
  final DateTime lastUpdated;
  @override
  @JsonKey()
  final SyncStatus syncStatus;

  /// Create a copy of ProgressItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProgressItemCopyWith<_ProgressItem> get copyWith =>
      __$ProgressItemCopyWithImpl<_ProgressItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ProgressItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProgressItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.positionSeconds, positionSeconds) ||
                other.positionSeconds == positionSeconds) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, itemId,
      positionSeconds, lastUpdated, syncStatus);

  @override
  String toString() {
    return 'ProgressItem(id: $id, userId: $userId, itemId: $itemId, positionSeconds: $positionSeconds, lastUpdated: $lastUpdated, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class _$ProgressItemCopyWith<$Res>
    implements $ProgressItemCopyWith<$Res> {
  factory _$ProgressItemCopyWith(
          _ProgressItem value, $Res Function(_ProgressItem) _then) =
      __$ProgressItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      double positionSeconds,
      DateTime lastUpdated,
      SyncStatus syncStatus});
}

/// @nodoc
class __$ProgressItemCopyWithImpl<$Res>
    implements _$ProgressItemCopyWith<$Res> {
  __$ProgressItemCopyWithImpl(this._self, this._then);

  final _ProgressItem _self;
  final $Res Function(_ProgressItem) _then;

  /// Create a copy of ProgressItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? positionSeconds = null,
    Object? lastUpdated = null,
    Object? syncStatus = null,
  }) {
    return _then(_ProgressItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      positionSeconds: null == positionSeconds
          ? _self.positionSeconds
          : positionSeconds // ignore: cast_nullable_to_non_nullable
              as double,
      lastUpdated: null == lastUpdated
          ? _self.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
mixin _$HistoryItem {
  String get id;
  String get userId;
  String get itemId;
  ActionType get actionType;
  DateTime get timestamp;
  SyncStatus get syncStatus;

  /// Create a copy of HistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HistoryItemCopyWith<HistoryItem> get copyWith =>
      _$HistoryItemCopyWithImpl<HistoryItem>(this as HistoryItem, _$identity);

  /// Serializes this HistoryItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HistoryItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.actionType, actionType) ||
                other.actionType == actionType) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, userId, itemId, actionType, timestamp, syncStatus);

  @override
  String toString() {
    return 'HistoryItem(id: $id, userId: $userId, itemId: $itemId, actionType: $actionType, timestamp: $timestamp, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class $HistoryItemCopyWith<$Res> {
  factory $HistoryItemCopyWith(
          HistoryItem value, $Res Function(HistoryItem) _then) =
      _$HistoryItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      ActionType actionType,
      DateTime timestamp,
      SyncStatus syncStatus});
}

/// @nodoc
class _$HistoryItemCopyWithImpl<$Res> implements $HistoryItemCopyWith<$Res> {
  _$HistoryItemCopyWithImpl(this._self, this._then);

  final HistoryItem _self;
  final $Res Function(HistoryItem) _then;

  /// Create a copy of HistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? actionType = null,
    Object? timestamp = null,
    Object? syncStatus = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      actionType: null == actionType
          ? _self.actionType
          : actionType // ignore: cast_nullable_to_non_nullable
              as ActionType,
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _HistoryItem extends HistoryItem {
  _HistoryItem(
      {required this.id,
      required this.userId,
      required this.itemId,
      required this.actionType,
      required this.timestamp,
      this.syncStatus = SyncStatus.pending})
      : super._();
  factory _HistoryItem.fromJson(Map<String, dynamic> json) =>
      _$HistoryItemFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String itemId;
  @override
  final ActionType actionType;
  @override
  final DateTime timestamp;
  @override
  @JsonKey()
  final SyncStatus syncStatus;

  /// Create a copy of HistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$HistoryItemCopyWith<_HistoryItem> get copyWith =>
      __$HistoryItemCopyWithImpl<_HistoryItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$HistoryItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _HistoryItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.actionType, actionType) ||
                other.actionType == actionType) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, userId, itemId, actionType, timestamp, syncStatus);

  @override
  String toString() {
    return 'HistoryItem(id: $id, userId: $userId, itemId: $itemId, actionType: $actionType, timestamp: $timestamp, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class _$HistoryItemCopyWith<$Res>
    implements $HistoryItemCopyWith<$Res> {
  factory _$HistoryItemCopyWith(
          _HistoryItem value, $Res Function(_HistoryItem) _then) =
      __$HistoryItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      ActionType actionType,
      DateTime timestamp,
      SyncStatus syncStatus});
}

/// @nodoc
class __$HistoryItemCopyWithImpl<$Res> implements _$HistoryItemCopyWith<$Res> {
  __$HistoryItemCopyWithImpl(this._self, this._then);

  final _HistoryItem _self;
  final $Res Function(_HistoryItem) _then;

  /// Create a copy of HistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? actionType = null,
    Object? timestamp = null,
    Object? syncStatus = null,
  }) {
    return _then(_HistoryItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      actionType: null == actionType
          ? _self.actionType
          : actionType // ignore: cast_nullable_to_non_nullable
              as ActionType,
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

// dart format on
