// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
abstract class LoginResponse with _$LoginResponse {
  const factory LoginResponse({
    required String token,
    required UserModel user,
  }) = _LoginResponse;

  factory LoginResponse.fromJson(Map<String, Object?> json) =>
      _$LoginResponseFromJson(json);
}

@freezed
abstract class UserModel with _$UserModel {
  factory UserModel({
    @JsonKey(name: '_id') required String id,
    required String name,
    required String email,
    String? password,
    String? token,
    required String role,
    required bool verified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _UserModel;
  UserModel._();

  factory UserModel.fromJson(Map<String, Object?> json) =>
      _$UserModelFromJson(json);
}
