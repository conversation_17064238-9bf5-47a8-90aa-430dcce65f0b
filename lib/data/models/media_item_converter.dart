import 'package:freezed_annotation/freezed_annotation.dart';

import 'media_metadata.dart';

/// A JSON converter for MediaMetadata to handle conversion between
/// Map<String, dynamic> and MediaMetadata
class MediaMetadataConverter
    implements JsonConverter<MediaMetadata?, Map<String, dynamic>?> {
  const MediaMetadataConverter();

  @override
  MediaMetadata? fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return null;
    }
    return MediaMetadata.fromMap(json);
  }

  @override
  Map<String, dynamic>? toJson(MediaMetadata? metadata) {
    if (metadata == null) {
      return null;
    }
    return metadata.toMap();
  }
}
