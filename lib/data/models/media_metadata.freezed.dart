// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_metadata.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MediaMetadata {
// Document-related metadata
  String? get description; // HTML content
  String? get html; // Social media metrics
  int? get likeCount;
  int? get retweetCount;
  int? get commentCount;
  int? get shareCount; // Video-specific metadata
  int? get viewCount;
  String? get resolution;
  String? get codec; // Audio-specific metadata
  String? get artist;
  String? get album;
  String? get genre;
  int? get bitrate; // Publication metadata
  String? get author;
  String? get publisher;
  DateTime? get publishDate;
  List<String>?
      get tags; // Additional fields that don't fit the categories above
  Map<String, dynamic>? get additionalInfo;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaMetadataCopyWith<MediaMetadata> get copyWith =>
      _$MediaMetadataCopyWithImpl<MediaMetadata>(
          this as MediaMetadata, _$identity);

  /// Serializes this MediaMetadata to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaMetadata &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.html, html) || other.html == html) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.retweetCount, retweetCount) ||
                other.retweetCount == retweetCount) &&
            (identical(other.commentCount, commentCount) ||
                other.commentCount == commentCount) &&
            (identical(other.shareCount, shareCount) ||
                other.shareCount == shareCount) &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution) &&
            (identical(other.codec, codec) || other.codec == codec) &&
            (identical(other.artist, artist) || other.artist == artist) &&
            (identical(other.album, album) || other.album == album) &&
            (identical(other.genre, genre) || other.genre == genre) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.author, author) || other.author == author) &&
            (identical(other.publisher, publisher) ||
                other.publisher == publisher) &&
            (identical(other.publishDate, publishDate) ||
                other.publishDate == publishDate) &&
            const DeepCollectionEquality().equals(other.tags, tags) &&
            const DeepCollectionEquality()
                .equals(other.additionalInfo, additionalInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      description,
      html,
      likeCount,
      retweetCount,
      commentCount,
      shareCount,
      viewCount,
      resolution,
      codec,
      artist,
      album,
      genre,
      bitrate,
      author,
      publisher,
      publishDate,
      const DeepCollectionEquality().hash(tags),
      const DeepCollectionEquality().hash(additionalInfo));

  @override
  String toString() {
    return 'MediaMetadata(description: $description, html: $html, likeCount: $likeCount, retweetCount: $retweetCount, commentCount: $commentCount, shareCount: $shareCount, viewCount: $viewCount, resolution: $resolution, codec: $codec, artist: $artist, album: $album, genre: $genre, bitrate: $bitrate, author: $author, publisher: $publisher, publishDate: $publishDate, tags: $tags, additionalInfo: $additionalInfo)';
  }
}

/// @nodoc
abstract mixin class $MediaMetadataCopyWith<$Res> {
  factory $MediaMetadataCopyWith(
          MediaMetadata value, $Res Function(MediaMetadata) _then) =
      _$MediaMetadataCopyWithImpl;
  @useResult
  $Res call(
      {String? description,
      String? html,
      int? likeCount,
      int? retweetCount,
      int? commentCount,
      int? shareCount,
      int? viewCount,
      String? resolution,
      String? codec,
      String? artist,
      String? album,
      String? genre,
      int? bitrate,
      String? author,
      String? publisher,
      DateTime? publishDate,
      List<String>? tags,
      Map<String, dynamic>? additionalInfo});
}

/// @nodoc
class _$MediaMetadataCopyWithImpl<$Res>
    implements $MediaMetadataCopyWith<$Res> {
  _$MediaMetadataCopyWithImpl(this._self, this._then);

  final MediaMetadata _self;
  final $Res Function(MediaMetadata) _then;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? html = freezed,
    Object? likeCount = freezed,
    Object? retweetCount = freezed,
    Object? commentCount = freezed,
    Object? shareCount = freezed,
    Object? viewCount = freezed,
    Object? resolution = freezed,
    Object? codec = freezed,
    Object? artist = freezed,
    Object? album = freezed,
    Object? genre = freezed,
    Object? bitrate = freezed,
    Object? author = freezed,
    Object? publisher = freezed,
    Object? publishDate = freezed,
    Object? tags = freezed,
    Object? additionalInfo = freezed,
  }) {
    return _then(_self.copyWith(
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      html: freezed == html
          ? _self.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      likeCount: freezed == likeCount
          ? _self.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int?,
      retweetCount: freezed == retweetCount
          ? _self.retweetCount
          : retweetCount // ignore: cast_nullable_to_non_nullable
              as int?,
      commentCount: freezed == commentCount
          ? _self.commentCount
          : commentCount // ignore: cast_nullable_to_non_nullable
              as int?,
      shareCount: freezed == shareCount
          ? _self.shareCount
          : shareCount // ignore: cast_nullable_to_non_nullable
              as int?,
      viewCount: freezed == viewCount
          ? _self.viewCount
          : viewCount // ignore: cast_nullable_to_non_nullable
              as int?,
      resolution: freezed == resolution
          ? _self.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String?,
      codec: freezed == codec
          ? _self.codec
          : codec // ignore: cast_nullable_to_non_nullable
              as String?,
      artist: freezed == artist
          ? _self.artist
          : artist // ignore: cast_nullable_to_non_nullable
              as String?,
      album: freezed == album
          ? _self.album
          : album // ignore: cast_nullable_to_non_nullable
              as String?,
      genre: freezed == genre
          ? _self.genre
          : genre // ignore: cast_nullable_to_non_nullable
              as String?,
      bitrate: freezed == bitrate
          ? _self.bitrate
          : bitrate // ignore: cast_nullable_to_non_nullable
              as int?,
      author: freezed == author
          ? _self.author
          : author // ignore: cast_nullable_to_non_nullable
              as String?,
      publisher: freezed == publisher
          ? _self.publisher
          : publisher // ignore: cast_nullable_to_non_nullable
              as String?,
      publishDate: freezed == publishDate
          ? _self.publishDate
          : publishDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tags: freezed == tags
          ? _self.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      additionalInfo: freezed == additionalInfo
          ? _self.additionalInfo
          : additionalInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MediaMetadata extends MediaMetadata {
  const _MediaMetadata(
      {this.description,
      this.html,
      this.likeCount,
      this.retweetCount,
      this.commentCount,
      this.shareCount,
      this.viewCount,
      this.resolution,
      this.codec,
      this.artist,
      this.album,
      this.genre,
      this.bitrate,
      this.author,
      this.publisher,
      this.publishDate,
      final List<String>? tags,
      final Map<String, dynamic>? additionalInfo})
      : _tags = tags,
        _additionalInfo = additionalInfo,
        super._();
  factory _MediaMetadata.fromJson(Map<String, dynamic> json) =>
      _$MediaMetadataFromJson(json);

// Document-related metadata
  @override
  final String? description;
// HTML content
  @override
  final String? html;
// Social media metrics
  @override
  final int? likeCount;
  @override
  final int? retweetCount;
  @override
  final int? commentCount;
  @override
  final int? shareCount;
// Video-specific metadata
  @override
  final int? viewCount;
  @override
  final String? resolution;
  @override
  final String? codec;
// Audio-specific metadata
  @override
  final String? artist;
  @override
  final String? album;
  @override
  final String? genre;
  @override
  final int? bitrate;
// Publication metadata
  @override
  final String? author;
  @override
  final String? publisher;
  @override
  final DateTime? publishDate;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Additional fields that don't fit the categories above
  final Map<String, dynamic>? _additionalInfo;
// Additional fields that don't fit the categories above
  @override
  Map<String, dynamic>? get additionalInfo {
    final value = _additionalInfo;
    if (value == null) return null;
    if (_additionalInfo is EqualUnmodifiableMapView) return _additionalInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaMetadataCopyWith<_MediaMetadata> get copyWith =>
      __$MediaMetadataCopyWithImpl<_MediaMetadata>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MediaMetadataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaMetadata &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.html, html) || other.html == html) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.retweetCount, retweetCount) ||
                other.retweetCount == retweetCount) &&
            (identical(other.commentCount, commentCount) ||
                other.commentCount == commentCount) &&
            (identical(other.shareCount, shareCount) ||
                other.shareCount == shareCount) &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution) &&
            (identical(other.codec, codec) || other.codec == codec) &&
            (identical(other.artist, artist) || other.artist == artist) &&
            (identical(other.album, album) || other.album == album) &&
            (identical(other.genre, genre) || other.genre == genre) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.author, author) || other.author == author) &&
            (identical(other.publisher, publisher) ||
                other.publisher == publisher) &&
            (identical(other.publishDate, publishDate) ||
                other.publishDate == publishDate) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality()
                .equals(other._additionalInfo, _additionalInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      description,
      html,
      likeCount,
      retweetCount,
      commentCount,
      shareCount,
      viewCount,
      resolution,
      codec,
      artist,
      album,
      genre,
      bitrate,
      author,
      publisher,
      publishDate,
      const DeepCollectionEquality().hash(_tags),
      const DeepCollectionEquality().hash(_additionalInfo));

  @override
  String toString() {
    return 'MediaMetadata(description: $description, html: $html, likeCount: $likeCount, retweetCount: $retweetCount, commentCount: $commentCount, shareCount: $shareCount, viewCount: $viewCount, resolution: $resolution, codec: $codec, artist: $artist, album: $album, genre: $genre, bitrate: $bitrate, author: $author, publisher: $publisher, publishDate: $publishDate, tags: $tags, additionalInfo: $additionalInfo)';
  }
}

/// @nodoc
abstract mixin class _$MediaMetadataCopyWith<$Res>
    implements $MediaMetadataCopyWith<$Res> {
  factory _$MediaMetadataCopyWith(
          _MediaMetadata value, $Res Function(_MediaMetadata) _then) =
      __$MediaMetadataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? description,
      String? html,
      int? likeCount,
      int? retweetCount,
      int? commentCount,
      int? shareCount,
      int? viewCount,
      String? resolution,
      String? codec,
      String? artist,
      String? album,
      String? genre,
      int? bitrate,
      String? author,
      String? publisher,
      DateTime? publishDate,
      List<String>? tags,
      Map<String, dynamic>? additionalInfo});
}

/// @nodoc
class __$MediaMetadataCopyWithImpl<$Res>
    implements _$MediaMetadataCopyWith<$Res> {
  __$MediaMetadataCopyWithImpl(this._self, this._then);

  final _MediaMetadata _self;
  final $Res Function(_MediaMetadata) _then;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? description = freezed,
    Object? html = freezed,
    Object? likeCount = freezed,
    Object? retweetCount = freezed,
    Object? commentCount = freezed,
    Object? shareCount = freezed,
    Object? viewCount = freezed,
    Object? resolution = freezed,
    Object? codec = freezed,
    Object? artist = freezed,
    Object? album = freezed,
    Object? genre = freezed,
    Object? bitrate = freezed,
    Object? author = freezed,
    Object? publisher = freezed,
    Object? publishDate = freezed,
    Object? tags = freezed,
    Object? additionalInfo = freezed,
  }) {
    return _then(_MediaMetadata(
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      html: freezed == html
          ? _self.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      likeCount: freezed == likeCount
          ? _self.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int?,
      retweetCount: freezed == retweetCount
          ? _self.retweetCount
          : retweetCount // ignore: cast_nullable_to_non_nullable
              as int?,
      commentCount: freezed == commentCount
          ? _self.commentCount
          : commentCount // ignore: cast_nullable_to_non_nullable
              as int?,
      shareCount: freezed == shareCount
          ? _self.shareCount
          : shareCount // ignore: cast_nullable_to_non_nullable
              as int?,
      viewCount: freezed == viewCount
          ? _self.viewCount
          : viewCount // ignore: cast_nullable_to_non_nullable
              as int?,
      resolution: freezed == resolution
          ? _self.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String?,
      codec: freezed == codec
          ? _self.codec
          : codec // ignore: cast_nullable_to_non_nullable
              as String?,
      artist: freezed == artist
          ? _self.artist
          : artist // ignore: cast_nullable_to_non_nullable
              as String?,
      album: freezed == album
          ? _self.album
          : album // ignore: cast_nullable_to_non_nullable
              as String?,
      genre: freezed == genre
          ? _self.genre
          : genre // ignore: cast_nullable_to_non_nullable
              as String?,
      bitrate: freezed == bitrate
          ? _self.bitrate
          : bitrate // ignore: cast_nullable_to_non_nullable
              as int?,
      author: freezed == author
          ? _self.author
          : author // ignore: cast_nullable_to_non_nullable
              as String?,
      publisher: freezed == publisher
          ? _self.publisher
          : publisher // ignore: cast_nullable_to_non_nullable
              as String?,
      publishDate: freezed == publishDate
          ? _self.publishDate
          : publishDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tags: freezed == tags
          ? _self._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      additionalInfo: freezed == additionalInfo
          ? _self._additionalInfo
          : additionalInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

// dart format on
