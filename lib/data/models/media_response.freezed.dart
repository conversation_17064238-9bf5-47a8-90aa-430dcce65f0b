// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MediaResponse {
  MediaResponseMetadata get metadata;
  List<MediaItem> get mediaItems;

  /// Create a copy of MediaResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaResponseCopyWith<MediaResponse> get copyWith =>
      _$MediaResponseCopyWithImpl<MediaResponse>(
          this as MediaResponse, _$identity);

  /// Serializes this MediaResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaResponse &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            const DeepCollectionEquality()
                .equals(other.mediaItems, mediaItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, metadata, const DeepCollectionEquality().hash(mediaItems));

  @override
  String toString() {
    return 'MediaResponse(metadata: $metadata, mediaItems: $mediaItems)';
  }
}

/// @nodoc
abstract mixin class $MediaResponseCopyWith<$Res> {
  factory $MediaResponseCopyWith(
          MediaResponse value, $Res Function(MediaResponse) _then) =
      _$MediaResponseCopyWithImpl;
  @useResult
  $Res call({MediaResponseMetadata metadata, List<MediaItem> mediaItems});

  $MediaResponseMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class _$MediaResponseCopyWithImpl<$Res>
    implements $MediaResponseCopyWith<$Res> {
  _$MediaResponseCopyWithImpl(this._self, this._then);

  final MediaResponse _self;
  final $Res Function(MediaResponse) _then;

  /// Create a copy of MediaResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? metadata = null,
    Object? mediaItems = null,
  }) {
    return _then(_self.copyWith(
      metadata: null == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as MediaResponseMetadata,
      mediaItems: null == mediaItems
          ? _self.mediaItems
          : mediaItems // ignore: cast_nullable_to_non_nullable
              as List<MediaItem>,
    ));
  }

  /// Create a copy of MediaResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaResponseMetadataCopyWith<$Res> get metadata {
    return $MediaResponseMetadataCopyWith<$Res>(_self.metadata, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MediaResponse implements MediaResponse {
  const _MediaResponse(
      {required this.metadata, required final List<MediaItem> mediaItems})
      : _mediaItems = mediaItems;
  factory _MediaResponse.fromJson(Map<String, dynamic> json) =>
      _$MediaResponseFromJson(json);

  @override
  final MediaResponseMetadata metadata;
  final List<MediaItem> _mediaItems;
  @override
  List<MediaItem> get mediaItems {
    if (_mediaItems is EqualUnmodifiableListView) return _mediaItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mediaItems);
  }

  /// Create a copy of MediaResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaResponseCopyWith<_MediaResponse> get copyWith =>
      __$MediaResponseCopyWithImpl<_MediaResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MediaResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaResponse &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            const DeepCollectionEquality()
                .equals(other._mediaItems, _mediaItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, metadata, const DeepCollectionEquality().hash(_mediaItems));

  @override
  String toString() {
    return 'MediaResponse(metadata: $metadata, mediaItems: $mediaItems)';
  }
}

/// @nodoc
abstract mixin class _$MediaResponseCopyWith<$Res>
    implements $MediaResponseCopyWith<$Res> {
  factory _$MediaResponseCopyWith(
          _MediaResponse value, $Res Function(_MediaResponse) _then) =
      __$MediaResponseCopyWithImpl;
  @override
  @useResult
  $Res call({MediaResponseMetadata metadata, List<MediaItem> mediaItems});

  @override
  $MediaResponseMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class __$MediaResponseCopyWithImpl<$Res>
    implements _$MediaResponseCopyWith<$Res> {
  __$MediaResponseCopyWithImpl(this._self, this._then);

  final _MediaResponse _self;
  final $Res Function(_MediaResponse) _then;

  /// Create a copy of MediaResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? metadata = null,
    Object? mediaItems = null,
  }) {
    return _then(_MediaResponse(
      metadata: null == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as MediaResponseMetadata,
      mediaItems: null == mediaItems
          ? _self._mediaItems
          : mediaItems // ignore: cast_nullable_to_non_nullable
              as List<MediaItem>,
    ));
  }

  /// Create a copy of MediaResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaResponseMetadataCopyWith<$Res> get metadata {
    return $MediaResponseMetadataCopyWith<$Res>(_self.metadata, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

/// @nodoc
mixin _$MediaResponseMetadata {
  String get schemaVersion;
  String get generatedAt;
  String get source;
  String get checksum;

  /// Create a copy of MediaResponseMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaResponseMetadataCopyWith<MediaResponseMetadata> get copyWith =>
      _$MediaResponseMetadataCopyWithImpl<MediaResponseMetadata>(
          this as MediaResponseMetadata, _$identity);

  /// Serializes this MediaResponseMetadata to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaResponseMetadata &&
            (identical(other.schemaVersion, schemaVersion) ||
                other.schemaVersion == schemaVersion) &&
            (identical(other.generatedAt, generatedAt) ||
                other.generatedAt == generatedAt) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.checksum, checksum) ||
                other.checksum == checksum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, schemaVersion, generatedAt, source, checksum);

  @override
  String toString() {
    return 'MediaResponseMetadata(schemaVersion: $schemaVersion, generatedAt: $generatedAt, source: $source, checksum: $checksum)';
  }
}

/// @nodoc
abstract mixin class $MediaResponseMetadataCopyWith<$Res> {
  factory $MediaResponseMetadataCopyWith(MediaResponseMetadata value,
          $Res Function(MediaResponseMetadata) _then) =
      _$MediaResponseMetadataCopyWithImpl;
  @useResult
  $Res call(
      {String schemaVersion,
      String generatedAt,
      String source,
      String checksum});
}

/// @nodoc
class _$MediaResponseMetadataCopyWithImpl<$Res>
    implements $MediaResponseMetadataCopyWith<$Res> {
  _$MediaResponseMetadataCopyWithImpl(this._self, this._then);

  final MediaResponseMetadata _self;
  final $Res Function(MediaResponseMetadata) _then;

  /// Create a copy of MediaResponseMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? schemaVersion = null,
    Object? generatedAt = null,
    Object? source = null,
    Object? checksum = null,
  }) {
    return _then(_self.copyWith(
      schemaVersion: null == schemaVersion
          ? _self.schemaVersion
          : schemaVersion // ignore: cast_nullable_to_non_nullable
              as String,
      generatedAt: null == generatedAt
          ? _self.generatedAt
          : generatedAt // ignore: cast_nullable_to_non_nullable
              as String,
      source: null == source
          ? _self.source
          : source // ignore: cast_nullable_to_non_nullable
              as String,
      checksum: null == checksum
          ? _self.checksum
          : checksum // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MediaResponseMetadata implements MediaResponseMetadata {
  const _MediaResponseMetadata(
      {required this.schemaVersion,
      required this.generatedAt,
      required this.source,
      required this.checksum});
  factory _MediaResponseMetadata.fromJson(Map<String, dynamic> json) =>
      _$MediaResponseMetadataFromJson(json);

  @override
  final String schemaVersion;
  @override
  final String generatedAt;
  @override
  final String source;
  @override
  final String checksum;

  /// Create a copy of MediaResponseMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaResponseMetadataCopyWith<_MediaResponseMetadata> get copyWith =>
      __$MediaResponseMetadataCopyWithImpl<_MediaResponseMetadata>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MediaResponseMetadataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaResponseMetadata &&
            (identical(other.schemaVersion, schemaVersion) ||
                other.schemaVersion == schemaVersion) &&
            (identical(other.generatedAt, generatedAt) ||
                other.generatedAt == generatedAt) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.checksum, checksum) ||
                other.checksum == checksum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, schemaVersion, generatedAt, source, checksum);

  @override
  String toString() {
    return 'MediaResponseMetadata(schemaVersion: $schemaVersion, generatedAt: $generatedAt, source: $source, checksum: $checksum)';
  }
}

/// @nodoc
abstract mixin class _$MediaResponseMetadataCopyWith<$Res>
    implements $MediaResponseMetadataCopyWith<$Res> {
  factory _$MediaResponseMetadataCopyWith(_MediaResponseMetadata value,
          $Res Function(_MediaResponseMetadata) _then) =
      __$MediaResponseMetadataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String schemaVersion,
      String generatedAt,
      String source,
      String checksum});
}

/// @nodoc
class __$MediaResponseMetadataCopyWithImpl<$Res>
    implements _$MediaResponseMetadataCopyWith<$Res> {
  __$MediaResponseMetadataCopyWithImpl(this._self, this._then);

  final _MediaResponseMetadata _self;
  final $Res Function(_MediaResponseMetadata) _then;

  /// Create a copy of MediaResponseMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? schemaVersion = null,
    Object? generatedAt = null,
    Object? source = null,
    Object? checksum = null,
  }) {
    return _then(_MediaResponseMetadata(
      schemaVersion: null == schemaVersion
          ? _self.schemaVersion
          : schemaVersion // ignore: cast_nullable_to_non_nullable
              as String,
      generatedAt: null == generatedAt
          ? _self.generatedAt
          : generatedAt // ignore: cast_nullable_to_non_nullable
              as String,
      source: null == source
          ? _self.source
          : source // ignore: cast_nullable_to_non_nullable
              as String,
      checksum: null == checksum
          ? _self.checksum
          : checksum // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
