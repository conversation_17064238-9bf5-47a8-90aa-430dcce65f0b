import 'package:flutter/material.dart';

import '../../enums/media_type_enum.dart';

/// Model class for main tab categories
class MainTabCategory {
  const MainTabCategory({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.icon,
    required this.secondaryTabs,
  });

  final String id;
  final String title;
  final String titleAr;
  final IconData icon;
  final List<SecondaryTabCategory> secondaryTabs;
}

/// Model class for secondary tab categories
class SecondaryTabCategory {
  const SecondaryTabCategory({
    required this.id, // This is likely the string value of a MediaCategory
    required this.title,
    required this.titleAr,
    required this.mediaType, // This refers to the MediaType (e.g. audio, video)
  });

  final String id;
  final String title;
  final String titleAr;
  final MediaType mediaType;
}
