import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../gen/assets.gen.dart';
import '../../enums/media_type_enum.dart';
import '../media_items/media_provider.dart';
import '../media_ui_model.dart';
import 'tab_category_model.dart';

part 'tab_category_provider.g.dart';

/// Provider for main tab categories
@riverpod
Future<List<MainTabCategory>> mainTabCategories(Ref ref) async {
  // Load the JSON data
  final String jsonString = await rootBundle.loadString(Assets.jsons.samples);
  final Map<String, dynamic> jsonData =
      json.decode(jsonString) as Map<String, dynamic>;

  // Extract media items
  final List<dynamic> mediaItems = jsonData['mediaItems'] as List<dynamic>;

  final Set<MediaCategory> audioCategories = <MediaCategory>{};
  final Set<MediaCategory> videoCategories = <MediaCategory>{};
  final Set<MediaCategory> textCategories = <MediaCategory>{};
  final Set<MediaCategory> tweetCategories = <MediaCategory>{};

  for (final dynamic item in mediaItems) {
    final Map<String, dynamic> mediaItem = item as Map<String, dynamic>;
    final MediaType type = MediaType.fromString(mediaItem['type'] as String?);
    final MediaCategory category =
        MediaCategory.fromString(mediaItem['category'] as String?);

    if (type == MediaType.audio) {
      audioCategories.add(category);
    } else if (type == MediaType.video) {
      videoCategories.add(category);
    } else if (type == MediaType.text) {
      textCategories.add(category);
    } else if (type == MediaType.html) {
      // HTML content can be categorized under a more general text/articles category
      // or a specific MediaCategory.htmlArticles if defined.
      textCategories.add(
          category); // Assuming 'html' category string maps to a MediaCategory
    } else if (type == MediaType.tweet) {
      tweetCategories.add(category);
    }
  }

  // Create main tab categories
  final List<MainTabCategory> mainCategories = <MainTabCategory>[
    // Audio tab
    MainTabCategory(
      id: 'audio', // Keep string ID for the main tab itself if used for routing/keys
      title: 'Audio',
      titleAr: 'الصوتيات',
      icon: Icons.audiotrack,
      secondaryTabs: audioCategories.map((MediaCategory category) {
        // Iterate over MediaCategory enums
        String titleAr;
        // Compare with MediaCategory enum values
        switch (category) {
          case MediaCategory.lessons:
            titleAr = 'الدروس العلمية';
            break;
          case MediaCategory.sermons:
            titleAr = 'الخطب';
            break;
          case MediaCategory.lectures:
            titleAr = 'المحاضرات';
            break;
          case MediaCategory.radio:
            titleAr = 'الإذاعة';
            break;
          // default:
          //   titleAr =
          //       category.toJson().replaceAll('_', ' '); // Basic formatting
          case MediaCategory.youtubeVideos:
            // TODO(ameen): Handle YouTube videos category.
            titleAr = 'فيديوهات اليوتيوب';
            break;
          case MediaCategory.books:
            // TODO(ameen): Handle books category.
            titleAr = 'الكتب';
            break;
          case MediaCategory.htmlArticles:
            // TODO(ameen): Handle HTML articles category.
            titleAr = 'المقالات';
            break;
          case MediaCategory.recentTweets:
            // TODO(ameen): Handle recent tweets category.
            titleAr = 'التغريدات الحديثة';
            break;
          case MediaCategory.photos:
            // TODO(ameen): Handle photos category.
            titleAr = 'الصور';
            break;
          case MediaCategory.general:
            // TODO(ameen): Handle general category.
            titleAr = 'عام';
            break;
          case MediaCategory.tweetCategory:
            // TODO(ameen): Handle tweet category.
            titleAr = 'التغريدات';
            break;
          case MediaCategory.unknown:
            // TODO(ameen): Handle unknown category.
            titleAr = 'غير معروف';
            break;
        }

        return SecondaryTabCategory(
          id: category
              .toJson(), // Use the string representation of the enum for ID
          title: _formatCategoryName(category.toJson()),
          titleAr: titleAr,
          mediaType: MediaType.audio,
        );
      }).toList(),
    ),

    // Video tab
    MainTabCategory(
      id: 'video',
      title: 'Video',
      titleAr: 'المرئيات',
      icon: Icons.videocam,
      secondaryTabs: videoCategories.map((MediaCategory category) {
        String titleAr;
        switch (category) {
          case MediaCategory
                .lectures: // Assuming 'lectures' maps to MediaCategory.lectures
            titleAr = 'الدروس المرئية';
            break;
          case MediaCategory
                .youtubeVideos: // Assuming 'youtube_videos' maps to MediaCategory.youtubeVideos
            titleAr = 'من اليوتيوب';
            break;

          case MediaCategory.lessons:
            titleAr = 'الدروس العلمية';
            break;
          case MediaCategory.sermons:
            titleAr = 'الخطب';
            break;
          case MediaCategory.radio:
            titleAr = 'الإذاعة';
            break;
          case MediaCategory.books:
            titleAr = 'الكتب';
            break;
          case MediaCategory.htmlArticles:
            titleAr = 'المقالات';
            break;
          case MediaCategory.recentTweets:
            titleAr = 'التغريدات الحديثة';
            break;
          case MediaCategory.photos:
            titleAr = 'الصور';
            break;
          case MediaCategory.general:
            titleAr = 'عام';
            break;
          case MediaCategory.tweetCategory:
            titleAr = 'التغريدات';
            break;
          case MediaCategory.unknown:
            titleAr = 'غير معروف';
            break;
        }

        return SecondaryTabCategory(
          id: category.toJson(),
          title: _formatCategoryName(category.toJson()),
          titleAr: titleAr,
          mediaType: MediaType.video,
        );
      }).toList(),
    ),

    // References tab
    MainTabCategory(
      id: 'references',
      title: 'References',
      titleAr: 'المرجعيات',
      icon: Icons.menu_book,
      secondaryTabs: textCategories.map((MediaCategory category) {
        String titleAr;
        switch (category) {
          case MediaCategory.books:
            titleAr = 'الكتب';
            break;
          case MediaCategory.htmlArticles:
            titleAr = 'المقالات';
            break;

          case MediaCategory.lessons:
            titleAr = 'الدروس العلمية';
            break;
          case MediaCategory.sermons:
            titleAr = 'الخطب';
            break;
          case MediaCategory.lectures:
            titleAr = 'المحاضرات';
            break;
          case MediaCategory.radio:
            titleAr = 'الإذاعة';
            break;
          case MediaCategory.youtubeVideos:
            titleAr = 'فيديوهات اليوتيوب';
            break;
          case MediaCategory.recentTweets:
            titleAr = 'التغريدات الحديثة';
            break;
          case MediaCategory.photos:
            titleAr = 'الصور';
            break;
          case MediaCategory.general:
            titleAr = 'عام';
            break;
          case MediaCategory.tweetCategory:
            titleAr = 'التغريدات';
            break;
          case MediaCategory.unknown:
            titleAr = 'غير معروف';
            break;
        }

        return SecondaryTabCategory(
          id: category.toJson(),
          title: _formatCategoryName(category.toJson()),
          titleAr: titleAr,
          mediaType: MediaType.text, // Or MediaType.html if specific
        );
      }).toList(),
    ),

    // Tweets tab (dedicated tab for tweets)
    MainTabCategory(
      id: 'tweets', // Main tab ID
      title: 'Tweets',
      titleAr: 'التغريدات',
      icon: Icons.format_quote,
      secondaryTabs: <SecondaryTabCategory>[
        SecondaryTabCategory(
          id: MediaCategory.recentTweets
              .toJson(), // Use enum's string value for ID
          title: 'Recent Tweets',
          titleAr: 'التغريدات الحديثة',
          mediaType: MediaType.tweet,
        ),
        // Example if you had more tweet sub-categories
        // SecondaryTabCategory(
        //   id: MediaCategory.popularTweets.toJson(),
        //   title: 'Popular Tweets',
        //   titleAr: 'التغريدات الشائعة',
        //   mediaType: MediaType.tweet,
        // ),
      ],
    ),
  ];

  return mainCategories;
}

/// Provider for filtered media items based on selected category
@riverpod
List<MediaUiModel> categoryFilteredMedia(
  Ref ref, {
  required MediaType mediaType,
  required MediaCategory category,
}) {
  // Get all media items from the filtered provider (which handles search)
  final List<MediaUiModel> allMedia = ref.watch(filteredMediaProvider);

  // Create a more efficient filtering function based on the media type
  bool Function(MediaUiModel) filterFunction;

  if (mediaType == MediaType.audio) {
    filterFunction = (MediaUiModel item) =>
        (item.type == MediaType.audio ||
            (item.type == MediaType.tweet && item.hasAudioContent)) &&
        item.category == category;
  } else if (mediaType == MediaType.text &&
      category == MediaCategory.htmlArticles) {
    // Special case for HTML content in the text tab
    filterFunction = (MediaUiModel item) =>
        (item.type == MediaType.text || item.type == MediaType.html) &&
        item.category == category;
  } else {
    filterFunction = (MediaUiModel item) =>
        item.type == mediaType && item.category == category;
  }

  // Apply the filter function once
  return allMedia.where(filterFunction).toList();
}

/// Helper function to format category names from their string ID (enum.toJson())
String _formatCategoryName(String categoryId) {
  // Capitalize first letter of each word
  return categoryId.split('_').map((String word) {
    if (word.isEmpty) {
      return '';
    }
    return word[0].toUpperCase() + word.substring(1);
  }).join(' ');
}

/// Provider for the selected main tab index
/// Using autoDispose (default) since this state is only needed while the app is running
/// and doesn't need to persist across app restarts
@riverpod
class SelectedMainTab extends _$SelectedMainTab {
  @override
  int build() {
    return 0;
  }

  void setTab(int index) {
    state = index;
  }
}

/// Provider for the selected secondary tab index
/// Using autoDispose (default) since this state is only needed while the app is running
/// and doesn't need to persist across app restarts
@riverpod
class SelectedSecondaryTab extends _$SelectedSecondaryTab {
  @override
  int build() {
    return 0;
  }

  void setTab(int index) {
    state = index;
  }
}

/// Provider for the selected media ID in the mini player
/// This can use keepAlive since we want the mini player state to persist
/// even when navigating between different screens
@Riverpod(keepAlive: true)
class MiniPlayerMediaId extends _$MiniPlayerMediaId {
  @override
  String? build() {
    return null;
  }

  void setMediaId(String? id) {
    state = id;
  }

  void clearMediaId() {
    state = null;
  }
}
