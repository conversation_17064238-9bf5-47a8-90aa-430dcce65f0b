// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_player_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MediaPlayerState {
  MediaUiModel? get mediaItem;
  bool get isLoading;
  bool get isPlaying;
  bool get isBuffering;
  Duration get currentPosition;
  Duration get totalDuration;
  double get volume;
  double get playbackSpeed;
  String? get errorMessage;
  bool? get isDownloading;
  double? get downloadProgress; // PDF specific states
  int? get currentPage;
  int? get totalPages;
  double? get pdfZoom; // Article specific states
  double? get articleScrollPosition;
  double? get articleFontSize;
  bool? get isDarkMode; // Tweet specific states
  bool? get isLiked;
  int? get likeCount;
  int? get retweetCount; // Document states
  String? get documentLoadError;
  bool? get isDocumentLoaded;
  int? get currentViewIndex;
  LoopMode get loopMode;
  List<MediaOption>? get mediaOptions;
  String? get downloadedFilePath;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaPlayerStateCopyWith<MediaPlayerState> get copyWith =>
      _$MediaPlayerStateCopyWithImpl<MediaPlayerState>(
          this as MediaPlayerState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaPlayerState &&
            (identical(other.mediaItem, mediaItem) ||
                other.mediaItem == mediaItem) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isPlaying, isPlaying) ||
                other.isPlaying == isPlaying) &&
            (identical(other.isBuffering, isBuffering) ||
                other.isBuffering == isBuffering) &&
            (identical(other.currentPosition, currentPosition) ||
                other.currentPosition == currentPosition) &&
            (identical(other.totalDuration, totalDuration) ||
                other.totalDuration == totalDuration) &&
            (identical(other.volume, volume) || other.volume == volume) &&
            (identical(other.playbackSpeed, playbackSpeed) ||
                other.playbackSpeed == playbackSpeed) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.downloadProgress, downloadProgress) ||
                other.downloadProgress == downloadProgress) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.pdfZoom, pdfZoom) || other.pdfZoom == pdfZoom) &&
            (identical(other.articleScrollPosition, articleScrollPosition) ||
                other.articleScrollPosition == articleScrollPosition) &&
            (identical(other.articleFontSize, articleFontSize) ||
                other.articleFontSize == articleFontSize) &&
            (identical(other.isDarkMode, isDarkMode) ||
                other.isDarkMode == isDarkMode) &&
            (identical(other.isLiked, isLiked) || other.isLiked == isLiked) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.retweetCount, retweetCount) ||
                other.retweetCount == retweetCount) &&
            (identical(other.documentLoadError, documentLoadError) ||
                other.documentLoadError == documentLoadError) &&
            (identical(other.isDocumentLoaded, isDocumentLoaded) ||
                other.isDocumentLoaded == isDocumentLoaded) &&
            (identical(other.currentViewIndex, currentViewIndex) ||
                other.currentViewIndex == currentViewIndex) &&
            (identical(other.loopMode, loopMode) ||
                other.loopMode == loopMode) &&
            const DeepCollectionEquality()
                .equals(other.mediaOptions, mediaOptions) &&
            (identical(other.downloadedFilePath, downloadedFilePath) ||
                other.downloadedFilePath == downloadedFilePath));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        mediaItem,
        isLoading,
        isPlaying,
        isBuffering,
        currentPosition,
        totalDuration,
        volume,
        playbackSpeed,
        errorMessage,
        isDownloading,
        downloadProgress,
        currentPage,
        totalPages,
        pdfZoom,
        articleScrollPosition,
        articleFontSize,
        isDarkMode,
        isLiked,
        likeCount,
        retweetCount,
        documentLoadError,
        isDocumentLoaded,
        currentViewIndex,
        loopMode,
        const DeepCollectionEquality().hash(mediaOptions),
        downloadedFilePath
      ]);

  @override
  String toString() {
    return 'MediaPlayerState(mediaItem: $mediaItem, isLoading: $isLoading, isPlaying: $isPlaying, isBuffering: $isBuffering, currentPosition: $currentPosition, totalDuration: $totalDuration, volume: $volume, playbackSpeed: $playbackSpeed, errorMessage: $errorMessage, isDownloading: $isDownloading, downloadProgress: $downloadProgress, currentPage: $currentPage, totalPages: $totalPages, pdfZoom: $pdfZoom, articleScrollPosition: $articleScrollPosition, articleFontSize: $articleFontSize, isDarkMode: $isDarkMode, isLiked: $isLiked, likeCount: $likeCount, retweetCount: $retweetCount, documentLoadError: $documentLoadError, isDocumentLoaded: $isDocumentLoaded, currentViewIndex: $currentViewIndex, loopMode: $loopMode, mediaOptions: $mediaOptions, downloadedFilePath: $downloadedFilePath)';
  }
}

/// @nodoc
abstract mixin class $MediaPlayerStateCopyWith<$Res> {
  factory $MediaPlayerStateCopyWith(
          MediaPlayerState value, $Res Function(MediaPlayerState) _then) =
      _$MediaPlayerStateCopyWithImpl;
  @useResult
  $Res call(
      {MediaUiModel? mediaItem,
      bool isLoading,
      bool isPlaying,
      bool isBuffering,
      Duration currentPosition,
      Duration totalDuration,
      double volume,
      double playbackSpeed,
      String? errorMessage,
      bool? isDownloading,
      double? downloadProgress,
      int? currentPage,
      int? totalPages,
      double? pdfZoom,
      double? articleScrollPosition,
      double? articleFontSize,
      bool? isDarkMode,
      bool? isLiked,
      int? likeCount,
      int? retweetCount,
      String? documentLoadError,
      bool? isDocumentLoaded,
      int? currentViewIndex,
      LoopMode loopMode,
      List<MediaOption>? mediaOptions,
      String? downloadedFilePath});

  $MediaUiModelCopyWith<$Res>? get mediaItem;
}

/// @nodoc
class _$MediaPlayerStateCopyWithImpl<$Res>
    implements $MediaPlayerStateCopyWith<$Res> {
  _$MediaPlayerStateCopyWithImpl(this._self, this._then);

  final MediaPlayerState _self;
  final $Res Function(MediaPlayerState) _then;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaItem = freezed,
    Object? isLoading = null,
    Object? isPlaying = null,
    Object? isBuffering = null,
    Object? currentPosition = null,
    Object? totalDuration = null,
    Object? volume = null,
    Object? playbackSpeed = null,
    Object? errorMessage = freezed,
    Object? isDownloading = freezed,
    Object? downloadProgress = freezed,
    Object? currentPage = freezed,
    Object? totalPages = freezed,
    Object? pdfZoom = freezed,
    Object? articleScrollPosition = freezed,
    Object? articleFontSize = freezed,
    Object? isDarkMode = freezed,
    Object? isLiked = freezed,
    Object? likeCount = freezed,
    Object? retweetCount = freezed,
    Object? documentLoadError = freezed,
    Object? isDocumentLoaded = freezed,
    Object? currentViewIndex = freezed,
    Object? loopMode = null,
    Object? mediaOptions = freezed,
    Object? downloadedFilePath = freezed,
  }) {
    return _then(_self.copyWith(
      mediaItem: freezed == mediaItem
          ? _self.mediaItem
          : mediaItem // ignore: cast_nullable_to_non_nullable
              as MediaUiModel?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isPlaying: null == isPlaying
          ? _self.isPlaying
          : isPlaying // ignore: cast_nullable_to_non_nullable
              as bool,
      isBuffering: null == isBuffering
          ? _self.isBuffering
          : isBuffering // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPosition: null == currentPosition
          ? _self.currentPosition
          : currentPosition // ignore: cast_nullable_to_non_nullable
              as Duration,
      totalDuration: null == totalDuration
          ? _self.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      volume: null == volume
          ? _self.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as double,
      playbackSpeed: null == playbackSpeed
          ? _self.playbackSpeed
          : playbackSpeed // ignore: cast_nullable_to_non_nullable
              as double,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isDownloading: freezed == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool?,
      downloadProgress: freezed == downloadProgress
          ? _self.downloadProgress
          : downloadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
      currentPage: freezed == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPages: freezed == totalPages
          ? _self.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int?,
      pdfZoom: freezed == pdfZoom
          ? _self.pdfZoom
          : pdfZoom // ignore: cast_nullable_to_non_nullable
              as double?,
      articleScrollPosition: freezed == articleScrollPosition
          ? _self.articleScrollPosition
          : articleScrollPosition // ignore: cast_nullable_to_non_nullable
              as double?,
      articleFontSize: freezed == articleFontSize
          ? _self.articleFontSize
          : articleFontSize // ignore: cast_nullable_to_non_nullable
              as double?,
      isDarkMode: freezed == isDarkMode
          ? _self.isDarkMode
          : isDarkMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      isLiked: freezed == isLiked
          ? _self.isLiked
          : isLiked // ignore: cast_nullable_to_non_nullable
              as bool?,
      likeCount: freezed == likeCount
          ? _self.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int?,
      retweetCount: freezed == retweetCount
          ? _self.retweetCount
          : retweetCount // ignore: cast_nullable_to_non_nullable
              as int?,
      documentLoadError: freezed == documentLoadError
          ? _self.documentLoadError
          : documentLoadError // ignore: cast_nullable_to_non_nullable
              as String?,
      isDocumentLoaded: freezed == isDocumentLoaded
          ? _self.isDocumentLoaded
          : isDocumentLoaded // ignore: cast_nullable_to_non_nullable
              as bool?,
      currentViewIndex: freezed == currentViewIndex
          ? _self.currentViewIndex
          : currentViewIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      loopMode: null == loopMode
          ? _self.loopMode
          : loopMode // ignore: cast_nullable_to_non_nullable
              as LoopMode,
      mediaOptions: freezed == mediaOptions
          ? _self.mediaOptions
          : mediaOptions // ignore: cast_nullable_to_non_nullable
              as List<MediaOption>?,
      downloadedFilePath: freezed == downloadedFilePath
          ? _self.downloadedFilePath
          : downloadedFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaUiModelCopyWith<$Res>? get mediaItem {
    if (_self.mediaItem == null) {
      return null;
    }

    return $MediaUiModelCopyWith<$Res>(_self.mediaItem!, (value) {
      return _then(_self.copyWith(mediaItem: value));
    });
  }
}

/// @nodoc

class _MediaPlayerState implements MediaPlayerState {
  const _MediaPlayerState(
      {required this.mediaItem,
      required this.isLoading,
      required this.isPlaying,
      required this.isBuffering,
      required this.currentPosition,
      required this.totalDuration,
      required this.volume,
      required this.playbackSpeed,
      this.errorMessage,
      this.isDownloading = false,
      this.downloadProgress,
      this.currentPage,
      this.totalPages,
      this.pdfZoom,
      this.articleScrollPosition,
      this.articleFontSize,
      this.isDarkMode,
      this.isLiked,
      this.likeCount,
      this.retweetCount,
      this.documentLoadError,
      this.isDocumentLoaded,
      this.currentViewIndex,
      this.loopMode = LoopMode.off,
      final List<MediaOption>? mediaOptions,
      this.downloadedFilePath})
      : _mediaOptions = mediaOptions;

  @override
  final MediaUiModel? mediaItem;
  @override
  final bool isLoading;
  @override
  final bool isPlaying;
  @override
  final bool isBuffering;
  @override
  final Duration currentPosition;
  @override
  final Duration totalDuration;
  @override
  final double volume;
  @override
  final double playbackSpeed;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final bool? isDownloading;
  @override
  final double? downloadProgress;
// PDF specific states
  @override
  final int? currentPage;
  @override
  final int? totalPages;
  @override
  final double? pdfZoom;
// Article specific states
  @override
  final double? articleScrollPosition;
  @override
  final double? articleFontSize;
  @override
  final bool? isDarkMode;
// Tweet specific states
  @override
  final bool? isLiked;
  @override
  final int? likeCount;
  @override
  final int? retweetCount;
// Document states
  @override
  final String? documentLoadError;
  @override
  final bool? isDocumentLoaded;
  @override
  final int? currentViewIndex;
  @override
  @JsonKey()
  final LoopMode loopMode;
  final List<MediaOption>? _mediaOptions;
  @override
  List<MediaOption>? get mediaOptions {
    final value = _mediaOptions;
    if (value == null) return null;
    if (_mediaOptions is EqualUnmodifiableListView) return _mediaOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? downloadedFilePath;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaPlayerStateCopyWith<_MediaPlayerState> get copyWith =>
      __$MediaPlayerStateCopyWithImpl<_MediaPlayerState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaPlayerState &&
            (identical(other.mediaItem, mediaItem) ||
                other.mediaItem == mediaItem) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isPlaying, isPlaying) ||
                other.isPlaying == isPlaying) &&
            (identical(other.isBuffering, isBuffering) ||
                other.isBuffering == isBuffering) &&
            (identical(other.currentPosition, currentPosition) ||
                other.currentPosition == currentPosition) &&
            (identical(other.totalDuration, totalDuration) ||
                other.totalDuration == totalDuration) &&
            (identical(other.volume, volume) || other.volume == volume) &&
            (identical(other.playbackSpeed, playbackSpeed) ||
                other.playbackSpeed == playbackSpeed) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.downloadProgress, downloadProgress) ||
                other.downloadProgress == downloadProgress) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.pdfZoom, pdfZoom) || other.pdfZoom == pdfZoom) &&
            (identical(other.articleScrollPosition, articleScrollPosition) ||
                other.articleScrollPosition == articleScrollPosition) &&
            (identical(other.articleFontSize, articleFontSize) ||
                other.articleFontSize == articleFontSize) &&
            (identical(other.isDarkMode, isDarkMode) ||
                other.isDarkMode == isDarkMode) &&
            (identical(other.isLiked, isLiked) || other.isLiked == isLiked) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.retweetCount, retweetCount) ||
                other.retweetCount == retweetCount) &&
            (identical(other.documentLoadError, documentLoadError) ||
                other.documentLoadError == documentLoadError) &&
            (identical(other.isDocumentLoaded, isDocumentLoaded) ||
                other.isDocumentLoaded == isDocumentLoaded) &&
            (identical(other.currentViewIndex, currentViewIndex) ||
                other.currentViewIndex == currentViewIndex) &&
            (identical(other.loopMode, loopMode) ||
                other.loopMode == loopMode) &&
            const DeepCollectionEquality()
                .equals(other._mediaOptions, _mediaOptions) &&
            (identical(other.downloadedFilePath, downloadedFilePath) ||
                other.downloadedFilePath == downloadedFilePath));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        mediaItem,
        isLoading,
        isPlaying,
        isBuffering,
        currentPosition,
        totalDuration,
        volume,
        playbackSpeed,
        errorMessage,
        isDownloading,
        downloadProgress,
        currentPage,
        totalPages,
        pdfZoom,
        articleScrollPosition,
        articleFontSize,
        isDarkMode,
        isLiked,
        likeCount,
        retweetCount,
        documentLoadError,
        isDocumentLoaded,
        currentViewIndex,
        loopMode,
        const DeepCollectionEquality().hash(_mediaOptions),
        downloadedFilePath
      ]);

  @override
  String toString() {
    return 'MediaPlayerState(mediaItem: $mediaItem, isLoading: $isLoading, isPlaying: $isPlaying, isBuffering: $isBuffering, currentPosition: $currentPosition, totalDuration: $totalDuration, volume: $volume, playbackSpeed: $playbackSpeed, errorMessage: $errorMessage, isDownloading: $isDownloading, downloadProgress: $downloadProgress, currentPage: $currentPage, totalPages: $totalPages, pdfZoom: $pdfZoom, articleScrollPosition: $articleScrollPosition, articleFontSize: $articleFontSize, isDarkMode: $isDarkMode, isLiked: $isLiked, likeCount: $likeCount, retweetCount: $retweetCount, documentLoadError: $documentLoadError, isDocumentLoaded: $isDocumentLoaded, currentViewIndex: $currentViewIndex, loopMode: $loopMode, mediaOptions: $mediaOptions, downloadedFilePath: $downloadedFilePath)';
  }
}

/// @nodoc
abstract mixin class _$MediaPlayerStateCopyWith<$Res>
    implements $MediaPlayerStateCopyWith<$Res> {
  factory _$MediaPlayerStateCopyWith(
          _MediaPlayerState value, $Res Function(_MediaPlayerState) _then) =
      __$MediaPlayerStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {MediaUiModel? mediaItem,
      bool isLoading,
      bool isPlaying,
      bool isBuffering,
      Duration currentPosition,
      Duration totalDuration,
      double volume,
      double playbackSpeed,
      String? errorMessage,
      bool? isDownloading,
      double? downloadProgress,
      int? currentPage,
      int? totalPages,
      double? pdfZoom,
      double? articleScrollPosition,
      double? articleFontSize,
      bool? isDarkMode,
      bool? isLiked,
      int? likeCount,
      int? retweetCount,
      String? documentLoadError,
      bool? isDocumentLoaded,
      int? currentViewIndex,
      LoopMode loopMode,
      List<MediaOption>? mediaOptions,
      String? downloadedFilePath});

  @override
  $MediaUiModelCopyWith<$Res>? get mediaItem;
}

/// @nodoc
class __$MediaPlayerStateCopyWithImpl<$Res>
    implements _$MediaPlayerStateCopyWith<$Res> {
  __$MediaPlayerStateCopyWithImpl(this._self, this._then);

  final _MediaPlayerState _self;
  final $Res Function(_MediaPlayerState) _then;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? mediaItem = freezed,
    Object? isLoading = null,
    Object? isPlaying = null,
    Object? isBuffering = null,
    Object? currentPosition = null,
    Object? totalDuration = null,
    Object? volume = null,
    Object? playbackSpeed = null,
    Object? errorMessage = freezed,
    Object? isDownloading = freezed,
    Object? downloadProgress = freezed,
    Object? currentPage = freezed,
    Object? totalPages = freezed,
    Object? pdfZoom = freezed,
    Object? articleScrollPosition = freezed,
    Object? articleFontSize = freezed,
    Object? isDarkMode = freezed,
    Object? isLiked = freezed,
    Object? likeCount = freezed,
    Object? retweetCount = freezed,
    Object? documentLoadError = freezed,
    Object? isDocumentLoaded = freezed,
    Object? currentViewIndex = freezed,
    Object? loopMode = null,
    Object? mediaOptions = freezed,
    Object? downloadedFilePath = freezed,
  }) {
    return _then(_MediaPlayerState(
      mediaItem: freezed == mediaItem
          ? _self.mediaItem
          : mediaItem // ignore: cast_nullable_to_non_nullable
              as MediaUiModel?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isPlaying: null == isPlaying
          ? _self.isPlaying
          : isPlaying // ignore: cast_nullable_to_non_nullable
              as bool,
      isBuffering: null == isBuffering
          ? _self.isBuffering
          : isBuffering // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPosition: null == currentPosition
          ? _self.currentPosition
          : currentPosition // ignore: cast_nullable_to_non_nullable
              as Duration,
      totalDuration: null == totalDuration
          ? _self.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      volume: null == volume
          ? _self.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as double,
      playbackSpeed: null == playbackSpeed
          ? _self.playbackSpeed
          : playbackSpeed // ignore: cast_nullable_to_non_nullable
              as double,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isDownloading: freezed == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool?,
      downloadProgress: freezed == downloadProgress
          ? _self.downloadProgress
          : downloadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
      currentPage: freezed == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPages: freezed == totalPages
          ? _self.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int?,
      pdfZoom: freezed == pdfZoom
          ? _self.pdfZoom
          : pdfZoom // ignore: cast_nullable_to_non_nullable
              as double?,
      articleScrollPosition: freezed == articleScrollPosition
          ? _self.articleScrollPosition
          : articleScrollPosition // ignore: cast_nullable_to_non_nullable
              as double?,
      articleFontSize: freezed == articleFontSize
          ? _self.articleFontSize
          : articleFontSize // ignore: cast_nullable_to_non_nullable
              as double?,
      isDarkMode: freezed == isDarkMode
          ? _self.isDarkMode
          : isDarkMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      isLiked: freezed == isLiked
          ? _self.isLiked
          : isLiked // ignore: cast_nullable_to_non_nullable
              as bool?,
      likeCount: freezed == likeCount
          ? _self.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int?,
      retweetCount: freezed == retweetCount
          ? _self.retweetCount
          : retweetCount // ignore: cast_nullable_to_non_nullable
              as int?,
      documentLoadError: freezed == documentLoadError
          ? _self.documentLoadError
          : documentLoadError // ignore: cast_nullable_to_non_nullable
              as String?,
      isDocumentLoaded: freezed == isDocumentLoaded
          ? _self.isDocumentLoaded
          : isDocumentLoaded // ignore: cast_nullable_to_non_nullable
              as bool?,
      currentViewIndex: freezed == currentViewIndex
          ? _self.currentViewIndex
          : currentViewIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      loopMode: null == loopMode
          ? _self.loopMode
          : loopMode // ignore: cast_nullable_to_non_nullable
              as LoopMode,
      mediaOptions: freezed == mediaOptions
          ? _self._mediaOptions
          : mediaOptions // ignore: cast_nullable_to_non_nullable
              as List<MediaOption>?,
      downloadedFilePath: freezed == downloadedFilePath
          ? _self.downloadedFilePath
          : downloadedFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaUiModelCopyWith<$Res>? get mediaItem {
    if (_self.mediaItem == null) {
      return null;
    }

    return $MediaUiModelCopyWith<$Res>(_self.mediaItem!, (value) {
      return _then(_self.copyWith(mediaItem: value));
    });
  }
}

// dart format on
