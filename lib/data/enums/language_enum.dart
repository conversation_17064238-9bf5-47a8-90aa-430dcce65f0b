import 'package:freezed_annotation/freezed_annotation.dart';

enum Language {
  @JsonValue('ar')
  arabic,
  @JsonValue('en')
  english,
  unknown;

  static Language fromString(String? value) {
    if (value == 'ar') {
      return Language.arabic;
    }
    if (value == 'en') {
      return Language.english;
    }
    return Language.unknown;
  }

  String toJson() {
    switch (this) {
      case Language.arabic:
        return 'ar';
      case Language.english:
        return 'en';
      case Language.unknown:
        return 'unknown';
    }
  }
}
