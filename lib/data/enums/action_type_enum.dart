import 'package:freezed_annotation/freezed_annotation.dart';

/// Enum representing different types of user actions in the application
enum ActionType {
  @JsonValue('view')
  view,

  @JsonValue('play')
  play,

  @JsonValue('download')
  download,

  @JsonValue('share')
  share,

  @JsonValue('favorite')
  favorite,

  @JsonValue('unfavorite')
  unfavorite,

  @JsonValue('seek')
  seek,

  @JsonValue('pause')
  pause,

  @JsonValue('resume')
  resume,

  @JsonValue('complete')
  complete,

  @JsonValue('unknown')
  unknown;

  /// Convert a string to ActionType enum
  static ActionType fromString(String? value) {
    if (value == null) {
      return ActionType.unknown;
    }
    final String normalizedValue = value.toLowerCase();
    for (final ActionType type in values) {
      if (type.name == normalizedValue || type.toJson() == normalizedValue) {
        return type;
      }
    }
    return ActionType.unknown;
  }

  /// Convert an ActionType enum to its JSON string representation
  String toJson() {
    switch (this) {
      case ActionType.view:
        return 'view';
      case ActionType.play:
        return 'play';
      case ActionType.download:
        return 'download';
      case ActionType.share:
        return 'share';
      case ActionType.favorite:
        return 'favorite';
      case ActionType.unfavorite:
        return 'unfavorite';
      case ActionType.seek:
        return 'seek';
      case ActionType.pause:
        return 'pause';
      case ActionType.resume:
        return 'resume';
      case ActionType.complete:
        return 'complete';
      case ActionType.unknown:
        return 'unknown';
    }
  }

  /// Get Arabic display text for the action type
  String get displayTextAr {
    switch (this) {
      case ActionType.view:
        return 'مشاهدة';
      case ActionType.play:
        return 'تشغيل';
      case ActionType.download:
        return 'تحميل';
      case ActionType.share:
        return 'مشاركة';
      case ActionType.favorite:
        return 'إضافة للمفضلة';
      case ActionType.unfavorite:
        return 'إزالة من المفضلة';
      case ActionType.seek:
        return 'تقديم/تأخير';
      case ActionType.pause:
        return 'إيقاف مؤقت';
      case ActionType.resume:
        return 'استئناف';
      case ActionType.complete:
        return 'اكتمال';
      case ActionType.unknown:
        return 'غير معروف';
    }
  }

  /// Check if this action type represents media playback
  bool get isPlaybackAction => 
      this == ActionType.play || 
      this == ActionType.pause || 
      this == ActionType.resume || 
      this == ActionType.seek;

  /// Check if this action type represents user interaction
  bool get isUserInteraction => 
      this == ActionType.view || 
      this == ActionType.favorite || 
      this == ActionType.unfavorite || 
      this == ActionType.share;

  /// Check if this action type represents content consumption
  bool get isContentConsumption => 
      this == ActionType.view || 
      this == ActionType.play || 
      this == ActionType.complete;
}
