import 'package:freezed_annotation/freezed_annotation.dart';

/// Enum representing different media types in the application
enum MediaType {
  @JsonValue('audio')
  audio,

  @JsonValue('video')
  video,

  @JsonValue('text')
  text,

  @JsonValue('pdf')
  pdf,

  @JsonValue('document')
  document,

  @JsonValue('tweet')
  tweet,

  @JsonValue('html')
  html,

  @JsonValue('unknown')
  unknown;

  /// Convert a string to MediaType enum
  static MediaType fromString(String? value) {
    if (value == null) {
      return MediaType.unknown;
    }
    final String normalizedValue = value.toLowerCase();
    for (final MediaType type in values) {
      if (type.name == normalizedValue || type.toJson() == normalizedValue) {
        return type;
      }
    }
    return MediaType.unknown;
  }

  /// Convert a MediaType enum to its JSON string representation
  String toJson() {
    switch (this) {
      case MediaType.audio:
        return 'audio';
      case MediaType.video:
        return 'video';
      case MediaType.text:
        return 'text';
      case MediaType.pdf:
        return 'pdf';
      case MediaType.document:
        return 'document';
      case MediaType.tweet:
        return 'tweet';
      case MediaType.html:
        return 'html';
      case MediaType.unknown:
        return 'unknown';
    }
  }

  /// Check if the media type is audio or video
  bool get isAudioOrVideo => this == MediaType.audio || this == MediaType.video;

  /// Check if the media type is a document (pdf or document)
  bool get isDocument => this == MediaType.pdf || this == MediaType.document;

  /// Check if the media type is a tweet
  bool get isTweetType =>
      this ==
      MediaType
          .tweet; // Renamed to avoid conflict if a category is named 'isTweet'

  /// Check if the media type is text with HTML content
  bool get isHtmlText => this == MediaType.html || this == MediaType.text;
}

/// Enum representing different media categories in the application
enum MediaCategory {
  @JsonValue('lessons')
  lessons,
  @JsonValue('sermons')
  sermons,
  @JsonValue('lectures')
  lectures,
  @JsonValue('radio')
  radio,
  @JsonValue('youtube_videos')
  youtubeVideos,
  @JsonValue('books')
  books,
  @JsonValue('html_articles') // Differentiated from MediaType.html
  htmlArticles,
  @JsonValue('recent_tweets')
  recentTweets,
  @JsonValue('photos')
  photos,
  @JsonValue('general') // A general category if applicable
  general,
  // Specific category for tweets if needed, distinct from MediaType.tweet
  @JsonValue('tweet_category')
  tweetCategory,
  @JsonValue('unknown')
  unknown;

  /// Convert a string to MediaCategory enum
  static MediaCategory fromString(String? value) {
    if (value == null) {
      return MediaCategory.unknown;
    }
    final String normalizedValue = value.toLowerCase();
    for (final MediaCategory category in values) {
      if (category.name == normalizedValue ||
          category.toJson() == normalizedValue) {
        return category;
      }
    }
    return MediaCategory.unknown;
  }

  /// Convert a MediaCategory enum to its JSON string representation
  String toJson() {
    switch (this) {
      case MediaCategory.lessons:
        return 'lessons';
      case MediaCategory.sermons:
        return 'sermons';
      case MediaCategory.lectures:
        return 'lectures';
      case MediaCategory.radio:
        return 'radio';
      case MediaCategory.youtubeVideos:
        return 'youtube_videos';
      case MediaCategory.books:
        return 'books';
      case MediaCategory.htmlArticles:
        return 'html_articles'; // Or just 'html' if context is clear
      case MediaCategory.recentTweets:
        return 'recent_tweets';
      case MediaCategory.photos:
        return 'photos';
      case MediaCategory.general:
        return 'general';
      case MediaCategory.tweetCategory:
        return 'tweet'; // If the category string from JSON is just 'tweet'
      case MediaCategory.unknown:
        return 'unknown';
    }
  }
}
