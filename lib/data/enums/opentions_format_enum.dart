import 'package:freezed_annotation/freezed_annotation.dart';

enum OptionFormat {
  @JsonValue('audio/mpeg')
  audioMpeg,

  @JsonValue('text/plain')
  textPlain,

  @JsonValue('video/youtube')
  videoYoutube,

  @JsonValue('image/jpeg')
  imageJpeg,

  @JsonValue('video/mp4')
  videoMp4,

  @JsonValue('tweet')
  tweet,

  @JsonValue(
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
  docx,

  @JsonValue('application/pdf')
  pdf,

  @JsonValue('html')
  html,

  @JsonValue('article')
  article,

  unknown;

  /// Converts a string to an [OptionFormat] enum.
  static OptionFormat fromString(String? value) {
    if (value == null) {
      return OptionFormat.unknown;
    }
    final String lowerValue = value.toLowerCase();
    for (final OptionFormat formatEnum in values) {
      if (formatEnum.toJson().toLowerCase() == lowerValue) {
        return formatEnum;
      }
    }
    // Fallback for common variations or simplified types if needed
    if (lowerValue.startsWith('audio')) {
      return OptionFormat.audioMpeg;
    }
    if (lowerValue.startsWith('video'))
      return OptionFormat.videoMp4; // Default to mp4 if just 'video'
    if (lowerValue.contains('pdf')) {
      return OptionFormat.pdf;
    }
    if (lowerValue.contains('doc') || lowerValue.contains('word'))
      return OptionFormat.docx;
    if (lowerValue == 'text' || lowerValue == 'plain')
      return OptionFormat.textPlain;

    return OptionFormat.unknown;
  }

  /// Converts an [OptionFormat] enum to its JSON string representation.
  String toJson() {
    switch (this) {
      case OptionFormat.audioMpeg:
        return 'audio/mpeg';
      case OptionFormat.textPlain:
        return 'text/plain';
      case OptionFormat.videoYoutube:
        return 'video/youtube';
      case OptionFormat.imageJpeg:
        return 'image/jpeg';
      case OptionFormat.videoMp4:
        return 'video/mp4';
      case OptionFormat.tweet:
        return 'tweet';
      case OptionFormat.docx:
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case OptionFormat.pdf:
        return 'application/pdf';
      case OptionFormat.html:
        return 'html';
      case OptionFormat.article:
        return 'article';
      case OptionFormat.unknown:
        return 'unknown';
    }
  }

  /// Check if this format is an audio format
  bool get isAudio {
    switch (this) {
      case OptionFormat.audioMpeg:
        return true;
      case OptionFormat.textPlain:
      case OptionFormat.videoYoutube:
      case OptionFormat.imageJpeg:
      case OptionFormat.videoMp4:
      case OptionFormat.tweet:
      case OptionFormat.docx:
      case OptionFormat.pdf:
      case OptionFormat.html:
      case OptionFormat.article:
      case OptionFormat.unknown:
        return false;
    }
  }

  /// Check if this format is a video format
  bool get isVideo {
    switch (this) {
      case OptionFormat.videoYoutube:
      case OptionFormat.videoMp4:
        return true;
      case OptionFormat.audioMpeg:
      case OptionFormat.textPlain:
      case OptionFormat.imageJpeg:
      case OptionFormat.tweet:
      case OptionFormat.docx:
      case OptionFormat.pdf:
      case OptionFormat.html:
      case OptionFormat.article:
      case OptionFormat.unknown:
        return false;
    }
  }

  /// Check if this format is an image format
  bool get isImage {
    switch (this) {
      case OptionFormat.imageJpeg:
        return true;
      case OptionFormat.audioMpeg:
      case OptionFormat.textPlain:
      case OptionFormat.videoYoutube:
      case OptionFormat.videoMp4:
      case OptionFormat.tweet:
      case OptionFormat.docx:
      case OptionFormat.pdf:
      case OptionFormat.html:
      case OptionFormat.article:
      case OptionFormat.unknown:
        return false;
    }
  }

  /// Check if this format is a document format
  bool get isDocument {
    switch (this) {
      case OptionFormat.docx:
      case OptionFormat.pdf:
        return true;
      case OptionFormat.audioMpeg:
      case OptionFormat.textPlain:
      case OptionFormat.videoYoutube:
      case OptionFormat.imageJpeg:
      case OptionFormat.videoMp4:
      case OptionFormat.tweet:
      case OptionFormat.html:
      case OptionFormat.article:
      case OptionFormat.unknown:
        return false;
    }
  }

  /// Check if this format is a text format
  bool get isText {
    switch (this) {
      case OptionFormat.textPlain:
      case OptionFormat.html:
      case OptionFormat.article:
        return true;
      case OptionFormat.audioMpeg:
      case OptionFormat.videoYoutube:
      case OptionFormat.imageJpeg:
      case OptionFormat.videoMp4:
      case OptionFormat.tweet:
      case OptionFormat.docx:
      case OptionFormat.pdf:
      case OptionFormat.unknown:
        return false;
    }
  }

  /// Check if this format is a social media format
  bool get isSocialMedia {
    switch (this) {
      case OptionFormat.tweet:
        return true;
      case OptionFormat.audioMpeg:
      case OptionFormat.textPlain:
      case OptionFormat.videoYoutube:
      case OptionFormat.imageJpeg:
      case OptionFormat.videoMp4:
      case OptionFormat.docx:
      case OptionFormat.pdf:
      case OptionFormat.html:
      case OptionFormat.article:
      case OptionFormat.unknown:
        return false;
    }
  }

  /// Get the MIME type category (audio, video, image, application, text)
  String get mimeCategory {
    switch (this) {
      case OptionFormat.audioMpeg:
        return 'audio';
      case OptionFormat.videoYoutube:
      case OptionFormat.videoMp4:
        return 'video';
      case OptionFormat.imageJpeg:
        return 'image';
      case OptionFormat.docx:
      case OptionFormat.pdf:
        return 'application';
      case OptionFormat.textPlain:
      case OptionFormat.html:
      case OptionFormat.article:
        return 'text';
      case OptionFormat.tweet:
        return 'social';
      case OptionFormat.unknown:
        return 'unknown';
    }
  }

  /// Get file extension for this format
  String get fileExtension {
    switch (this) {
      case OptionFormat.audioMpeg:
        return '.mp3';
      case OptionFormat.videoYoutube:
        return '.mp4'; // YouTube videos are typically downloaded as mp4
      case OptionFormat.videoMp4:
        return '.mp4';
      case OptionFormat.imageJpeg:
        return '.jpg';
      case OptionFormat.docx:
        return '.docx';
      case OptionFormat.pdf:
        return '.pdf';
      case OptionFormat.textPlain:
        return '.txt';
      case OptionFormat.html:
        return '.html';
      case OptionFormat.tweet:
        return '.json'; // Tweets might be saved as JSON
      case OptionFormat.article:
        return '.txt'; // Articles are typically saved as text
      case OptionFormat.unknown:
        return '';
    }
  }
}
