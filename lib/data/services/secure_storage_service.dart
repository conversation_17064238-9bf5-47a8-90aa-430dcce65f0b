import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Secure storage service for sensitive data like authentication tokens
/// Uses flutter_secure_storage which stores data in:
/// - iOS: Keychain
/// - Android: EncryptedSharedPreferences
class SecureStorageService {
  SecureStorageService()
      : _storage = const FlutterSecureStorage(
          aOptions: AndroidOptions(
            encryptedSharedPreferences: true,
            storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
          ),
          iOptions: IOSOptions(
            accessibility: KeychainAccessibility.first_unlock_this_device,
          ),
        );

  final FlutterSecureStorage _storage;

  // Cache for synchronous access
  String? _cachedToken;
  String? _cachedUserId;

  // Keys for secure storage
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _biometricEnabledKey = 'biometric_enabled';

  /// Save authentication token securely
  Future<void> saveAuthToken(String token) async {
    _cachedToken = token; // Update cache
    await _storage.write(key: _tokenKey, value: token);
  }

  /// Get authentication token
  Future<String?> getAuthToken() async {
    final String? token = await _storage.read(key: _tokenKey);
    _cachedToken = token; // Update cache
    return token;
  }

  /// Get authentication token synchronously (from cache)
  String? getAuthTokenSync() {
    return _cachedToken;
  }

  /// Save refresh token securely
  Future<void> saveRefreshToken(String refreshToken) async {
    await _storage.write(key: _refreshTokenKey, value: refreshToken);
  }

  /// Get refresh token
  Future<String?> getRefreshToken() async {
    return _storage.read(key: _refreshTokenKey);
  }

  /// Save user ID securely
  Future<void> saveUserId(String userId) async {
    _cachedUserId = userId; // Update cache
    await _storage.write(key: _userIdKey, value: userId);
  }

  /// Get user ID
  Future<String?> getUserId() async {
    final String? userId = await _storage.read(key: _userIdKey);
    _cachedUserId = userId; // Update cache
    return userId;
  }

  /// Get user ID synchronously (from cache)
  String? getUserIdSync() {
    return _cachedUserId;
  }

  /// Save biometric authentication preference
  Future<void> setBiometricEnabled(bool enabled) async {
    await _storage.write(key: _biometricEnabledKey, value: enabled.toString());
  }

  /// Get biometric authentication preference
  Future<bool> isBiometricEnabled() async {
    final String? value = await _storage.read(key: _biometricEnabledKey);
    return value == 'true';
  }

  /// Clear all secure data (logout)
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  /// Clear specific key
  Future<void> clearKey(String key) async {
    await _storage.delete(key: key);
  }

  /// Check if secure storage contains a key
  Future<bool> containsKey(String key) async {
    return _storage.containsKey(key: key);
  }

  /// Get all stored keys (for debugging - use carefully)
  Future<Map<String, String>> getAllSecureData() async {
    return _storage.readAll();
  }
}
