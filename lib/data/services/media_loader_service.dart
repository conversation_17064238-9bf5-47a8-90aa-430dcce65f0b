import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';

import '../../gen/assets.gen.dart';
import '../models/media_item.dart';
import '../models/media_response.dart';
import '../models/media_ui_model.dart';

@injectable
class MediaLoaderService {
  /// Loads media items from the local JSON file
  Future<List<MediaItem>> loadMediaItems() async {
    try {
      // Load the JSON file from assets
      final String jsonString =
          await rootBundle.loadString(Assets.jsons.samples);

      // Parse the JSON
      final Map<String, dynamic> jsonData =
          json.decode(jsonString) as Map<String, dynamic>;

      // Convert to MediaResponse
      final MediaResponse response = MediaResponse.fromJson(jsonData);

      return response.mediaItems;
    } catch (e) {
      debugPrint('Error loading media items: $e');
      return <MediaItem>[];
    }
  }

  /// Converts domain MediaItem objects to UI models
  List<MediaUiModel> convertToUiModels(List<MediaItem> items) {
    return items
        .map((MediaItem item) => MediaUiModel.fromDomain(item))
        .toList();
  }

  /// Loads and converts media items to UI models in one step
  Future<List<MediaUiModel>> loadMediaUiModels() async {
    final List<MediaItem> items = await loadMediaItems();
    return convertToUiModels(items);
  }
}
