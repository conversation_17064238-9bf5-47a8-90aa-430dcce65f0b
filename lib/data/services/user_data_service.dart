import 'package:injectable/injectable.dart';
import 'package:sqflite/sqflite.dart';

import '../enums/action_type_enum.dart';
import '../enums/media_type_enum.dart';
import '../models/user_data_models.dart';
import '../sqlite/sqlite_helper.dart';

@lazySingleton
class UserDataService {
  UserDataService(this._sqliteHelper);

  final SQLiteHelper _sqliteHelper;
  static const String _anonymousUserId = 'anonymous_user';

  // Favorites methods
  Future<bool> toggleFavorite({
    required String userId,
    required String itemId,
    required MediaType type,
  }) async {
    // Check if already favorited
    final bool isFavorited = await _sqliteHelper.isFavorite(
      userId: userId,
      itemId: itemId,
    );

    if (isFavorited) {
      // Remove from favorites
      await _sqliteHelper.removeFavorite(
        userId: userId,
        itemId: itemId,
      );
      return false;
    } else {
      // Add to favorites
      await _sqliteHelper.addFavorite(
        userId: userId,
        itemId: itemId,
        type: type.toJson(),
      );
      return true;
    }
  }

  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  }) async {
    return _sqliteHelper.isFavorite(
      userId: userId,
      itemId: itemId,
    );
  }

  Future<List<FavoriteItem>> getFavorites({
    required String userId,
  }) async {
    return _sqliteHelper.getUserFavorites(userId);
  }

  // Progress methods
  Future<void> saveProgress({
    required String userId,
    required String itemId,
    required double positionSeconds,
  }) async {
    await _sqliteHelper.saveProgress(
      userId: userId,
      itemId: itemId,
      positionSeconds: positionSeconds,
    );
  }

  Future<double?> getProgress({
    required String userId,
    required String itemId,
  }) async {
    final ProgressItem? progress = await _sqliteHelper.getProgress(
      userId: userId,
      itemId: itemId,
    );

    return progress?.positionSeconds;
  }

  Future<List<ProgressItem>> getAllProgress({
    required String userId,
  }) async {
    return _sqliteHelper.getUserProgress(userId);
  }

  // History methods
  Future<void> addToHistory({
    required String userId,
    required String itemId,
    required ActionType actionType,
  }) async {
    await _sqliteHelper.addHistoryItem(
      userId: userId,
      itemId: itemId,
      actionType: actionType,
    );
  }

  Future<List<HistoryItem>> getHistory({
    required String userId,
  }) async {
    return _sqliteHelper.getUserHistory(userId);
  }

  // Clear user data
  Future<void> clearUserData({
    required String userId,
    bool clearFavorites = true,
    bool clearProgress = true,
    bool clearHistory = true,
  }) async {
    final Database db = await _sqliteHelper.database;

    // Use a transaction to ensure all deletes succeed or all fail
    await db.transaction((Transaction txn) async {
      if (clearFavorites) {
        await txn.delete(
          'favorites',
          where: 'user_id = ?',
          whereArgs: <Object?>[userId],
        );
      }

      if (clearProgress) {
        await txn.delete(
          'progress',
          where: 'user_id = ?',
          whereArgs: <Object?>[userId],
        );
      }

      if (clearHistory) {
        await txn.delete(
          'history',
          where: 'user_id = ?',
          whereArgs: <Object?>[userId],
        );
      }
    });
  }

  // Transfer anonymous data to user account
  Future<void> transferAnonymousData({
    required String userId,
  }) async {
    final Database db = await _sqliteHelper.database;

    // Use a transaction to ensure all updates succeed or all fail
    await db.transaction((Transaction txn) async {
      // Update favorites
      await txn.update(
        'favorites',
        <String, Object?>{'user_id': userId},
        where: 'user_id = ?',
        whereArgs: <Object?>[_anonymousUserId],
      );

      // Update progress
      await txn.update(
        'progress',
        <String, Object?>{'user_id': userId},
        where: 'user_id = ?',
        whereArgs: <Object?>[_anonymousUserId],
      );

      // Update history
      await txn.update(
        'history',
        <String, Object?>{'user_id': userId},
        where: 'user_id = ?',
        whereArgs: <Object?>[_anonymousUserId],
      );
    });
  }
}
