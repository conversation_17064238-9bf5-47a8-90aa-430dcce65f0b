import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

/// A JsonConverter for IconData
class IconDataConverter implements JsonConverter<IconData, int> {
  const IconDataConverter();

  @override
  IconData fromJson(int json) {
    return IconData(json, fontFamily: 'MaterialIcons');
  }

  @override
  int toJson(IconData object) {
    return object.codePoint;
  }
}
