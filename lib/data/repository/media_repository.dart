import 'package:injectable/injectable.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../di/components/service_locator.dart';
import '../../domain/repositories/media_repository_interface.dart';
import '../enums/media_type_enum.dart';
import '../models/media_item.dart';
import '../models/media_ui_model.dart';
import '../services/media_loader_service.dart';

part 'media_repository.g.dart';

// Provider for MediaRepository
@riverpod
MediaRepositoryInterface mediaRepository(Ref ref) {
  return MediaRepository(getIt<MediaLoaderService>());
}

@injectable
class MediaRepository implements MediaRepositoryInterface {
  MediaRepository(this._mediaLoaderService);
  final MediaLoaderService _mediaLoaderService;

  /// Get all media items
  @override
  Future<List<MediaItem>> getAllMediaItems() async {
    return _mediaLoaderService.loadMediaItems();
  }

  /// Get all media items as UI models
  @override
  Future<List<MediaUiModel>> getAllMediaUiModels() async {
    return _mediaLoaderService.loadMediaUiModels();
  }

  /// Get media items by category
  @override
  Future<List<MediaUiModel>> getMediaByCategory(MediaCategory category) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final List<MediaItem> filteredItems =
        allItems.where((MediaItem item) => item.category == category).toList();
    return _mediaLoaderService.convertToUiModels(filteredItems);
  }

  /// Get media items by type
  @override
  Future<List<MediaUiModel>> getMediaByType(MediaType type) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final MediaType requestedType = type;
    final List<MediaItem> filteredItems =
        allItems.where((MediaItem item) => item.type == requestedType).toList();
    return _mediaLoaderService.convertToUiModels(filteredItems);
  }

  /// Get a media item by ID
  @override
  Future<MediaUiModel?> getMediaById(String id) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final MediaItem? item =
        allItems.where((MediaItem item) => item.id == id).firstOrNull;

    if (item == null) {
      return null;
    }

    return MediaUiModel.fromDomain(item);
  }

  /// Search media items
  @override
  Future<List<MediaUiModel>> searchMedia(String query) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final String lowercaseQuery = query.toLowerCase();

    final List<MediaItem> filteredItems = allItems
        .where((MediaItem item) =>
            item.title.toLowerCase().contains(lowercaseQuery) ||
            (item.description?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            item.searchableText.toLowerCase().contains(lowercaseQuery))
        .toList();

    return _mediaLoaderService.convertToUiModels(filteredItems);
  }
}
