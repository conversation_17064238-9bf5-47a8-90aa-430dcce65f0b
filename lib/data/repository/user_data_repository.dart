import 'package:injectable/injectable.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../di/components/service_locator.dart';
import '../../domain/repositories/user_data_repository_interface.dart';
import '../enums/action_type_enum.dart';
import '../enums/media_type_enum.dart';
import '../models/user_data_models.dart';
import '../services/user_data_service.dart';

part 'user_data_repository.g.dart';

// Provider for UserDataRepository
@riverpod
UserDataRepositoryInterface userDataRepository(Ref ref) {
  return UserDataRepository(getIt<UserDataService>());
}

@injectable
class UserDataRepository implements UserDataRepositoryInterface {
  UserDataRepository(this._userDataService);

  final UserDataService _userDataService;

  @override
  Future<bool> toggleFavorite({
    required String userId,
    required String itemId,
    required MediaType type,
  }) async {
    return _userDataService.toggleFavorite(
      userId: userId,
      itemId: itemId,
      type: type,
    );
  }

  @override
  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  }) async {
    return _userDataService.isFavorite(
      userId: userId,
      itemId: itemId,
    );
  }

  @override
  Future<List<FavoriteItem>> getFavorites({
    required String userId,
  }) async {
    return _userDataService.getFavorites(userId: userId);
  }

  @override
  Future<void> saveProgress({
    required String userId,
    required String itemId,
    required double positionSeconds,
  }) async {
    return _userDataService.saveProgress(
      userId: userId,
      itemId: itemId,
      positionSeconds: positionSeconds,
    );
  }

  @override
  Future<double?> getProgress({
    required String userId,
    required String itemId,
  }) async {
    return _userDataService.getProgress(
      userId: userId,
      itemId: itemId,
    );
  }

  @override
  Future<List<ProgressItem>> getAllProgress({
    required String userId,
  }) async {
    return _userDataService.getAllProgress(userId: userId);
  }

  @override
  Future<void> addToHistory({
    required String userId,
    required String itemId,
    required ActionType actionType,
  }) async {
    return _userDataService.addToHistory(
      userId: userId,
      itemId: itemId,
      actionType: actionType,
    );
  }

  @override
  Future<List<HistoryItem>> getHistory({
    required String userId,
  }) async {
    return _userDataService.getHistory(userId: userId);
  }

  @override
  Future<void> clearUserData({
    required String userId,
    bool clearFavorites = true,
    bool clearProgress = true,
    bool clearHistory = true,
  }) async {
    return _userDataService.clearUserData(
      userId: userId,
      clearFavorites: clearFavorites,
      clearProgress: clearProgress,
      clearHistory: clearHistory,
    );
  }

  @override
  Future<void> transferAnonymousData({
    required String userId,
  }) async {
    return _userDataService.transferAnonymousData(userId: userId);
  }
}
