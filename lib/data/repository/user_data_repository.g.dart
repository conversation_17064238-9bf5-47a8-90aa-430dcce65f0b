// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(userDataRepository)
const userDataRepositoryProvider = UserDataRepositoryProvider._();

final class UserDataRepositoryProvider extends $FunctionalProvider<
    UserDataRepositoryInterface,
    UserDataRepositoryInterface> with $Provider<UserDataRepositoryInterface> {
  const UserDataRepositoryProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'userDataRepositoryProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$userDataRepositoryHash();

  @$internal
  @override
  $ProviderElement<UserDataRepositoryInterface> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  UserDataRepositoryInterface create(Ref ref) {
    return userDataRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserDataRepositoryInterface value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<UserDataRepositoryInterface>(value),
    );
  }
}

String _$userDataRepositoryHash() =>
    r'474f0b3d275ba2e3881c2da8cc31299b481aa34c';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
