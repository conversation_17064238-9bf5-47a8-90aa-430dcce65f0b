// ignore_for_file: avoid_classes_with_only_static_members

import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';

/// Custom color scheme for the app
class AppColors {
  // Primary colors
  static const Color primaryLight = Color(0xFF9E7450); // Green shade
  static const Color primaryDark =
      Color(0xFF29BB89); // Lighter green for dark mode

  // Secondary colors
  static const Color secondaryLight = Color(0xFF289672); // Another green shade
  static const Color secondaryDark =
      Color(0xFF4ADEA9); // Lighter green for dark mode

  // Tertiary colors
  static const Color tertiaryLight = Color(0xFF3EDBAD); // Accent green
  static const Color tertiaryDark =
      Color(0xFF6EDFC6); // Lighter accent for dark mode

  // Background colors
  static const Color backgroundLight = Color(0xFFF5F5F5); // Light background
  static const Color backgroundDark = Color(0xFF121212); // Dark background

  // Surface colors
  static const Color surfaceLight = Color(0xFFFFFFFF); // Light surface
  static const Color surfaceDark = Color(0xFF1E1E1E); // Dark surface

  // Error colors
  static const Color errorLight = Color(0xFFB00020); // Standard error color
  static const Color errorDark = Color(0xFFCF6679); // Error color for dark mode

  // Additional colors
  static const Color accentLight = Color(0xFFFFC857); // Yellow accent
  static const Color accentDark =
      Color(0xFFFFD685); // Lighter yellow for dark mode

  // Text colors
  static const Color textPrimaryLight =
      Color(0xFF212121); // Dark text for light mode
  static const Color textSecondaryLight =
      Color(0xFF757575); // Secondary text for light mode
  static const Color textPrimaryDark =
      Color(0xFFE1E1E1); // Light text for dark mode
  static const Color textSecondaryDark =
      Color(0xFFB0B0B0); // Secondary text for dark mode

  /// Create a custom FlexSchemeColor for light theme
  static FlexSchemeColor get lightScheme => FlexSchemeColor(
        primary: primaryLight,
        primaryContainer: primaryLight.withValues(alpha: 0.2),
        secondary: secondaryLight,
        secondaryContainer: secondaryLight.withValues(alpha: 0.2),
        tertiary: tertiaryLight,
        tertiaryContainer: tertiaryLight.withValues(alpha: 0.2),
        appBarColor: primaryLight,
        error: errorLight,
      );

  /// Create a custom FlexSchemeColor for dark theme
  static FlexSchemeColor get darkScheme => FlexSchemeColor(
        primary: primaryDark,
        primaryContainer: primaryDark.withValues(alpha: 0.2),
        secondary: secondaryDark,
        secondaryContainer: secondaryDark.withValues(alpha: 0.2),
        tertiary: tertiaryDark,
        tertiaryContainer: tertiaryDark.withValues(alpha: 0.2),
        appBarColor: primaryDark,
        error: errorDark,
      );
}
