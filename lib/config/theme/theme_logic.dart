// ignore_for_file: cast_nullable_to_non_nullable

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../data/sqlite/sqlite_helper.dart';
import 'theme_ui_model.dart';

part 'theme_logic.g.dart';

@riverpod
class ThemeLogic extends _$ThemeLogic {
  late SQLiteHelper _sqliteHelper;

  @override
  ThemeUiModel build() {
    _sqliteHelper = SQLiteHelper();
    return _getThemeMode();
  }

  ThemeUiModel _getThemeMode() {
    // Default theme mode
    ThemeMode themeMode = ThemeMode.light;

    // Use FutureBuilder in the UI to handle the async nature of this
    _sqliteHelper
        .getPreference('themeMode', defaultValue: ThemeMode.light.toString())
        .then((String? mode) {
      if (mode != null) {
        switch (mode) {
          case 'ThemeMode.dark':
            themeMode = ThemeMode.dark;
            break;
          case 'ThemeMode.light':
            themeMode = ThemeMode.light;
            break;
          case 'ThemeMode.system':
            themeMode = ThemeMode.system;
            break;
        }
        state = ThemeUiModel(themeMode: themeMode);
      }
    });

    return ThemeUiModel(themeMode: themeMode);
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    await _sqliteHelper.savePreference('themeMode', mode.toString());
    state = state.copyWith(themeMode: mode);
  }

  void toggleTheme() {
    if (state.themeMode == ThemeMode.dark) {
      setThemeMode(ThemeMode.light);
    } else {
      setThemeMode(ThemeMode.dark);
    }
  }
}
