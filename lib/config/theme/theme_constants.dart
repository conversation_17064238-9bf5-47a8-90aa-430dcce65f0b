import 'package:flex_color_scheme/flex_color_scheme.dart';

/// Constants for theme configuration
class ThemeConstants {
  // Border radius values
  static const double smallRadius = 8.0;
  static const double mediumRadius = 12.0;
  static const double largeRadius = 16.0;

  // Padding values
  static const double smallPadding = 8.0;
  static const double mediumPadding = 16.0;
  static const double largePadding = 24.0;

  // Elevation values
  static const double lowElevation = 1.0;
  static const double mediumElevation = 3.0;
  static const double highElevation = 6.0;

  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 350);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Common FlexSubThemesData for both light and dark themes
  static const FlexSubThemesData commonSubThemes = FlexSubThemesData(
    interactionEffects: true,
    tintedDisabledControls: true,
    useM2StyleDividerInM3: true,
    inputDecoratorIsFilled: true,
    inputDecoratorBorderType: FlexInputBorderType.outline,
    alignedDropdown: true,
    navigationRailUseIndicator: true,
    textButtonRadius: smallRadius,
    elevatedButtonRadius: smallRadius,
    outlinedButtonRadius: smallRadius,
    chipRadius: smallRadius,
    cardRadius: mediumRadius,
    popupMenuRadius: smallRadius,
    dialogRadius: mediumRadius,
    // Additional customizations
    appBarBackgroundSchemeColor: SchemeColor.primary,
    bottomNavigationBarSelectedLabelSchemeColor: SchemeColor.primary,
    bottomNavigationBarSelectedIconSchemeColor: SchemeColor.primary,
    navigationBarSelectedLabelSchemeColor: SchemeColor.primary,
    navigationBarSelectedIconSchemeColor: SchemeColor.primary,
    navigationRailSelectedLabelSchemeColor: SchemeColor.primary,
    navigationRailSelectedIconSchemeColor: SchemeColor.primary,
    tabBarItemSchemeColor: SchemeColor.primary,
    tabBarIndicatorSchemeColor: SchemeColor.primary,
  );

  // Dark mode specific sub themes
  static const FlexSubThemesData darkSubThemes = FlexSubThemesData(
    interactionEffects: true,
    tintedDisabledControls: true,
    blendOnColors: true,
    useM2StyleDividerInM3: true,
    inputDecoratorIsFilled: true,
    inputDecoratorBorderType: FlexInputBorderType.outline,
    alignedDropdown: true,
    navigationRailUseIndicator: true,
    textButtonRadius: smallRadius,
    elevatedButtonRadius: smallRadius,
    outlinedButtonRadius: smallRadius,
    chipRadius: smallRadius,
    cardRadius: mediumRadius,
    popupMenuRadius: smallRadius,
    dialogRadius: mediumRadius,
    // Additional customizations
    appBarBackgroundSchemeColor: SchemeColor.primary,
    bottomNavigationBarSelectedLabelSchemeColor: SchemeColor.primary,
    bottomNavigationBarSelectedIconSchemeColor: SchemeColor.primary,
    navigationBarSelectedLabelSchemeColor: SchemeColor.primary,
    navigationBarSelectedIconSchemeColor: SchemeColor.primary,
    navigationRailSelectedLabelSchemeColor: SchemeColor.primary,
    navigationRailSelectedIconSchemeColor: SchemeColor.primary,
    tabBarItemSchemeColor: SchemeColor.primary,
    tabBarIndicatorSchemeColor: SchemeColor.primary,
  );
}
