import '../../data/enums/action_type_enum.dart';
import '../../data/enums/media_type_enum.dart';
import '../../data/models/user_data_models.dart';

/// Abstract interface for user data repository operations
/// This interface defines the contract for user-specific data access
/// including favorites, progress tracking, and history
abstract class UserDataRepositoryInterface {
  // Favorites operations
  
  /// Toggle favorite status for a media item
  /// Returns true if item is now favorited, false if unfavorited
  Future<bool> toggleFavorite({
    required String userId,
    required String itemId,
    required MediaType type,
  });

  /// Check if a media item is favorited by the user
  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  });

  /// Get all favorite items for a user
  Future<List<FavoriteItem>> getFavorites({
    required String userId,
  });

  // Progress tracking operations
  
  /// Save playback progress for a media item
  Future<void> saveProgress({
    required String userId,
    required String itemId,
    required double positionSeconds,
  });

  /// Get saved progress for a media item
  Future<double?> getProgress({
    required String userId,
    required String itemId,
  });

  /// Get all progress records for a user
  Future<List<ProgressItem>> getAllProgress({
    required String userId,
  });

  // History operations
  
  /// Add an action to user's history
  Future<void> addToHistory({
    required String userId,
    required String itemId,
    required ActionType actionType,
  });

  /// Get user's action history
  Future<List<HistoryItem>> getHistory({
    required String userId,
  });

  // Data management operations
  
  /// Clear user data with selective options
  Future<void> clearUserData({
    required String userId,
    bool clearFavorites = true,
    bool clearProgress = true,
    bool clearHistory = true,
  });

  /// Transfer anonymous user data to authenticated user account
  Future<void> transferAnonymousData({
    required String userId,
  });
}
