import '../../data/enums/media_type_enum.dart';
import '../../data/models/media_item.dart';
import '../../data/models/media_ui_model.dart';

/// Abstract interface for media repository operations
/// This interface defines the contract for media data access
/// allowing for different implementations (API, local storage, etc.)
abstract class MediaRepositoryInterface {
  /// Get all media items from the data source
  Future<List<MediaItem>> getAllMediaItems();

  /// Get all media items as UI models for presentation layer
  Future<List<MediaUiModel>> getAllMediaUiModels();

  /// Get media items filtered by category
  Future<List<MediaUiModel>> getMediaByCategory(MediaCategory category);

  /// Get media items filtered by type
  Future<List<MediaUiModel>> getMediaByType(MediaType type);

  /// Get a specific media item by its ID
  Future<MediaUiModel?> getMediaById(String id);

  /// Search media items based on query string
  Future<List<MediaUiModel>> searchMedia(String query);
}
