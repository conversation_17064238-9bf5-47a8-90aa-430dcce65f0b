/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Cairo-Black.ttf
  String get cairoBlack => 'assets/fonts/Cairo-Black.ttf';

  /// File path: assets/fonts/Cairo-Bold.ttf
  String get cairoBold => 'assets/fonts/Cairo-Bold.ttf';

  /// File path: assets/fonts/Cairo-ExtraBold.ttf
  String get cairoExtraBold => 'assets/fonts/Cairo-ExtraBold.ttf';

  /// File path: assets/fonts/Cairo-ExtraLight.ttf
  String get cairoExtraLight => 'assets/fonts/Cairo-ExtraLight.ttf';

  /// File path: assets/fonts/Cairo-Light.ttf
  String get cairoLight => 'assets/fonts/Cairo-Light.ttf';

  /// File path: assets/fonts/Cairo-Medium.ttf
  String get cairoMedium => 'assets/fonts/Cairo-Medium.ttf';

  /// File path: assets/fonts/Cairo-Regular.ttf
  String get cairoRegular => 'assets/fonts/Cairo-Regular.ttf';

  /// File path: assets/fonts/Cairo-SemiBold.ttf
  String get cairoSemiBold => 'assets/fonts/Cairo-SemiBold.ttf';

  /// File path: assets/fonts/Nunito-Bold.ttf
  String get nunitoBold => 'assets/fonts/Nunito-Bold.ttf';

  /// File path: assets/fonts/Nunito-Light.ttf
  String get nunitoLight => 'assets/fonts/Nunito-Light.ttf';

  /// File path: assets/fonts/Nunito-Medium.ttf
  String get nunitoMedium => 'assets/fonts/Nunito-Medium.ttf';

  /// File path: assets/fonts/Nunito-Regular.ttf
  String get nunitoRegular => 'assets/fonts/Nunito-Regular.ttf';

  /// List of all assets
  List<String> get values => [
    cairoBlack,
    cairoBold,
    cairoExtraBold,
    cairoExtraLight,
    cairoLight,
    cairoMedium,
    cairoRegular,
    cairoSemiBold,
    nunitoBold,
    nunitoLight,
    nunitoMedium,
    nunitoRegular,
  ];
}

class $AssetsImgGen {
  const $AssetsImgGen();

  /// File path: assets/img/Group 52.png
  AssetGenImage get group52 => const AssetGenImage('assets/img/Group 52.png');

  /// File path: assets/img/Group 58.png
  AssetGenImage get group58 => const AssetGenImage('assets/img/Group 58.png');

  /// File path: assets/img/dr_diwanifont.png
  AssetGenImage get drDiwanifont =>
      const AssetGenImage('assets/img/dr_diwanifont.png');

  /// File path: assets/img/dr_image.png
  AssetGenImage get drImage => const AssetGenImage('assets/img/dr_image.png');

  /// File path: assets/img/home_light_web.jpg
  AssetGenImage get homeLightWeb =>
      const AssetGenImage('assets/img/home_light_web.jpg');

  /// File path: assets/img/info_light_web.jpg
  AssetGenImage get infoLightWeb =>
      const AssetGenImage('assets/img/info_light_web.jpg');

  /// File path: assets/img/programing.png
  AssetGenImage get programing =>
      const AssetGenImage('assets/img/programing.png');

  /// File path: assets/img/splash_screen_background.png
  AssetGenImage get splashScreenBackground =>
      const AssetGenImage('assets/img/splash_screen_background.png');

  /// File path: assets/img/splash_screen_center.png
  AssetGenImage get splashScreenCenter =>
      const AssetGenImage('assets/img/splash_screen_center.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    group52,
    group58,
    drDiwanifont,
    drImage,
    homeLightWeb,
    infoLightWeb,
    programing,
    splashScreenBackground,
    splashScreenCenter,
  ];
}

class $AssetsJsonsGen {
  const $AssetsJsonsGen();

  /// File path: assets/jsons/samples.json
  String get samples => 'assets/jsons/samples.json';

  /// List of all assets
  List<String> get values => [samples];
}

class $AssetsSvgGen {
  const $AssetsSvgGen();

  /// File path: assets/svg/arrow_next.svg
  String get arrowNext => 'assets/svg/arrow_next.svg';

  /// File path: assets/svg/calender.svg
  String get calender => 'assets/svg/calender.svg';

  /// File path: assets/svg/facebook.svg
  String get facebook => 'assets/svg/facebook.svg';

  /// File path: assets/svg/headphone.svg
  String get headphone => 'assets/svg/headphone.svg';

  /// File path: assets/svg/insta.svg
  String get insta => 'assets/svg/insta.svg';

  /// File path: assets/svg/menu.svg
  String get menu => 'assets/svg/menu.svg';

  /// File path: assets/svg/replay.svg
  String get replay => 'assets/svg/replay.svg';

  /// File path: assets/svg/search.svg
  String get search => 'assets/svg/search.svg';

  /// File path: assets/svg/settings.svg
  String get settings => 'assets/svg/settings.svg';

  /// File path: assets/svg/ticktok.svg
  String get ticktok => 'assets/svg/ticktok.svg';

  /// File path: assets/svg/youtube.svg
  String get youtube => 'assets/svg/youtube.svg';

  /// List of all assets
  List<String> get values => [
    arrowNext,
    calender,
    facebook,
    headphone,
    insta,
    menu,
    replay,
    search,
    settings,
    ticktok,
    youtube,
  ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en-US.json
  String get enUS => 'assets/translations/en-US.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, enUS, en];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsImgGen img = $AssetsImgGen();
  static const $AssetsJsonsGen jsons = $AssetsJsonsGen();
  static const $AssetsSvgGen svg = $AssetsSvgGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
