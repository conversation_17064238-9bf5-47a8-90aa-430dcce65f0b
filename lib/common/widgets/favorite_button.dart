import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/enums/media_type_enum.dart';
import '../../features/user_data/application/user_data_providers.dart';

class FavoriteButton extends ConsumerWidget {
  const FavoriteButton({
    required this.itemId,
    required this.itemType,
    this.size = 24.0,
    this.color,
    this.activeColor = Colors.red,
    super.key,
  });

  final String itemId;
  final MediaType itemType;
  final double size;
  final Color? color;
  final Color activeColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<bool> isFavoriteAsync =
        ref.watch(isFavoriteProvider(itemId));

    return isFavoriteAsync.when(
      data: (bool isFavorite) {
        return IconButton(
          icon: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            size: size,
            color: isFavorite ? activeColor : color,
          ),
          onPressed: () async {
            // Toggle the favorite status
            await ref.read(favoritesNotifierProvider.notifier).toggleFavorite(
                  itemId: itemId,
                  type: itemType,
                );

            // Invalidate the providers to force UI refresh
            ref.invalidate(isFavoriteProvider(itemId));
            ref.invalidate(favoritesNotifierProvider);
          },
        );
      },
      loading: () => SizedBox(
        width: size,
        height: size,
        child: const CircularProgressIndicator.adaptive(
          strokeWidth: 2,
        ),
      ),
      error: (Object error, StackTrace stackTrace) => IconButton(
        icon: Icon(
          Icons.favorite_border,
          size: size,
          color: color,
        ),
        onPressed: () async {
          // Toggle the favorite status
          await ref.read(favoritesNotifierProvider.notifier).toggleFavorite(
                itemId: itemId,
                type: itemType,
              );

          // Invalidate the providers to force UI refresh
          ref.invalidate(isFavoriteProvider(itemId));
          ref.invalidate(favoritesNotifierProvider);
        },
      ),
    );
  }
}
