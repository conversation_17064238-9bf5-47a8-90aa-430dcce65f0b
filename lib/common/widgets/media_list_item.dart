import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/enums/media_type_enum.dart';
import '../../data/models/media_ui_model.dart';
import '../../utils/icons_changer.dart';
import '../universal_image.dart';

/// A reusable widget for displaying media items in lists across the app
class MediaListItem extends ConsumerWidget {
  const MediaListItem({
    required this.title,
    required this.subtitle,
    this.secondarySubtitle,
    this.thumbnailUrl,
    this.mediaType,
    this.trailing,
    this.onTap,
    this.isUnavailable = false,
    super.key,
  });

  /// Creates a MediaListItem from a MediaUiModel
  factory MediaListItem.fromMediaModel({
    required MediaUiModel mediaItem,
    required String subtitle,
    String? secondarySubtitle,
    Widget? trailing,
    VoidCallback? onTap,
    Key? key,
  }) {
    return MediaListItem(
      title: mediaItem.title,
      subtitle: subtitle,
      secondarySubtitle: secondarySubtitle,
      thumbnailUrl: mediaItem.thumbnailUrl,
      mediaType: mediaItem.type,
      trailing: trailing,
      onTap: onTap,
      key: key,
    );
  }

  /// Creates a MediaListItem for an unavailable item
  factory MediaListItem.unavailable({
    required String itemId,
    required String subtitle,
    String? secondarySubtitle,
    Widget? trailing,
    VoidCallback? onTap,
    Key? key,
  }) {
    return MediaListItem(
      title: 'عنصر غير متوفر ($itemId)',
      subtitle: subtitle,
      secondarySubtitle: secondarySubtitle,
      trailing: trailing,
      onTap: onTap,
      isUnavailable: true,
      key: key,
    );
  }

  final String title;
  final String subtitle;
  final String? secondarySubtitle;
  final String? thumbnailUrl;
  final MediaType? mediaType;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool isUnavailable;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: buildListTile(context),
    );
  }

  /// Builds just the ListTile without the Card wrapper
  /// Useful when you need to embed the ListTile in a custom Card layout
  Widget buildListTile(BuildContext context) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(subtitle),
          if (secondarySubtitle != null)
            Text(
              secondarySubtitle!,
              style: Theme.of(context).textTheme.bodySmall,
            ),
        ],
      ),
      isThreeLine: secondarySubtitle != null,
      leading: _buildLeadingWidget(),
      trailing: trailing,
      onTap: onTap,
    );
  }

  Widget _buildLeadingWidget() {
    if (isUnavailable) {
      return const Icon(Icons.help_outline, size: 56);
    }

    if (thumbnailUrl != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: UniversalImage(
          path: thumbnailUrl!,
          width: 56,
          height: 56,
        ),
      );
    }

    return Icon(
      mediaType != null ? getIconForType(mediaType!) : Icons.help_outline,
      size: 56,
    );
  }
}
