import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../data/enums/action_type_enum.dart';
import '../../data/models/media_ui_model.dart';
import '../../features/user_data/application/media_providers.dart';
import '../../routing/app_router.dart';
import '../../utils/format_utils.dart';
import 'media_list_item.dart';

enum UserDataItemType {
  favorite,
  history,
  progress,
}

/// A reusable widget for displaying user data items (favorites, history, progress)
/// that automatically fetches the associated media item.
class UserDataListItem extends ConsumerWidget {
  /// Creates a UserDataListItem for a favorite item
  const UserDataListItem.favorite({
    required this.itemId,
    required this.dateTime,
    this.onRemove,
    this.onTap,
    super.key,
  })  : itemType = UserDataItemType.favorite,
        actionType = null,
        positionSeconds = null,
        customSubtitle = null;

  /// Creates a UserDataListItem for a history item
  const UserDataListItem.history({
    required this.itemId,
    required this.dateTime,
    required this.actionType,
    this.onTap,
    super.key,
  })  : itemType = UserDataItemType.history,
        onRemove = null,
        positionSeconds = null,
        customSubtitle = null;

  /// Creates a UserDataListItem for a progress item
  const UserDataListItem.progress({
    required this.itemId,
    required this.dateTime,
    required this.positionSeconds,
    this.onTap,
    this.customSubtitle,
    super.key,
  })  : itemType = UserDataItemType.progress,
        onRemove = null,
        actionType = null;

  final String itemId;
  final DateTime dateTime;
  final UserDataItemType itemType;
  final ActionType? actionType;
  final double? positionSeconds;
  final VoidCallback? onRemove;
  final VoidCallback? onTap;
  final String? customSubtitle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the media item details using the itemId
    final AsyncValue<MediaUiModel?> mediaItemAsync =
        ref.watch(mediaItemProvider(itemId));

    return mediaItemAsync.when(
      data: (MediaUiModel? mediaItem) {
        if (mediaItem == null) {
          return _buildUnavailableItem(context);
        }
        return _buildDataItem(context, mediaItem);
      },
      loading: () => const Card(
        margin: EdgeInsets.only(bottom: 16),
        child: ListTile(
          title: Text('جاري التحميل...'),
          leading: CircularProgressIndicator(),
        ),
      ),
      error: (Object error, StackTrace stackTrace) => const Card(
        margin: EdgeInsets.only(bottom: 16),
        child: ListTile(
          title: Text('عنصر غير متوفر'),
          subtitle: Text('تعذر تحميل بيانات العنصر'),
          leading: Icon(Icons.error_outline),
        ),
      ),
    );
  }

  Widget _buildUnavailableItem(BuildContext context) {
    String subtitle;
    String secondarySubtitle;

    switch (itemType) {
      case UserDataItemType.favorite:
        subtitle = 'تاريخ الإضافة: ${_formatDate(dateTime)}';
        secondarySubtitle = '';
        break;
      case UserDataItemType.history:
        subtitle = 'نوع الإجراء: ${actionType?.displayTextAr ?? ''}';
        secondarySubtitle = 'تاريخ المشاهدة: ${_formatDateTime(dateTime)}';
        break;
      case UserDataItemType.progress:
        subtitle =
            'الموضع: ${formatDurationFromDoubleSeconds(positionSeconds ?? 0)}';
        secondarySubtitle = 'آخر تحديث: ${formatDateTime(dateTime)}';
        break;
    }

    return MediaListItem.unavailable(
      itemId: itemId,
      subtitle: subtitle,
      secondarySubtitle:
          secondarySubtitle.isNotEmpty ? secondarySubtitle : null,
      trailing: itemType == UserDataItemType.favorite && onRemove != null
          ? IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: onRemove,
              tooltip: 'إزالة من المفضلة',
            )
          : null,
      onTap: onTap ?? () => _navigateToItemDetails(context, itemId),
    );
  }

  Widget _buildDataItem(BuildContext context, MediaUiModel mediaItem) {
    switch (itemType) {
      case UserDataItemType.favorite:
        return _buildFavoriteItem(context, mediaItem);
      case UserDataItemType.history:
        return _buildHistoryItem(context, mediaItem);
      case UserDataItemType.progress:
        return _buildProgressItem(context, mediaItem);
    }
  }

  Widget _buildFavoriteItem(BuildContext context, MediaUiModel mediaItem) {
    return MediaListItem.fromMediaModel(
      mediaItem: mediaItem,
      subtitle: mediaItem.category.name,
      secondarySubtitle: 'تاريخ الإضافة: ${_formatDate(dateTime)}',
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: () => _playMedia(context, mediaItem),
            tooltip: 'تشغيل',
          ),
          if (onRemove != null)
            IconButton(
              icon: const Icon(Icons.favorite, color: Colors.red),
              onPressed: onRemove,
              tooltip: 'إزالة من المفضلة',
            ),
        ],
      ),
      onTap: onTap ?? () => _playMedia(context, mediaItem),
    );
  }

  Widget _buildHistoryItem(BuildContext context, MediaUiModel mediaItem) {
    return MediaListItem.fromMediaModel(
      mediaItem: mediaItem,
      subtitle: '${mediaItem.category} - ${actionType?.displayTextAr ?? ''}',
      secondarySubtitle: 'تاريخ المشاهدة: ${_formatDateTime(dateTime)}',
      trailing: IconButton(
        icon: const Icon(Icons.play_arrow),
        onPressed: () => _playMedia(context, mediaItem),
        tooltip: 'تشغيل',
      ),
      onTap: onTap ?? () => _playMedia(context, mediaItem),
    );
  }

  Widget _buildProgressItem(BuildContext context, MediaUiModel mediaItem) {
    // Calculate progress percentage if duration is available
    double? progressPercentage;
    double? duration;

    // Try to get duration from metadata
    if (mediaItem.metadata != null &&
        mediaItem.metadata!.additionalInfo != null &&
        mediaItem.metadata!.additionalInfo!.containsKey('duration')) {
      duration = mediaItem.metadata!.additionalInfo!['duration'] as double?;
    }

    if (duration != null && duration > 0 && positionSeconds != null) {
      progressPercentage = positionSeconds! / duration;
      // Clamp to valid range
      progressPercentage = progressPercentage.clamp(0.0, 1.0);
    }

    final String subtitle = customSubtitle ??
        '${mediaItem.category} - ${formatDurationFromDoubleSeconds(positionSeconds ?? 0)}';

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          MediaListItem.fromMediaModel(
            mediaItem: mediaItem,
            subtitle: subtitle,
            secondarySubtitle: 'آخر تحديث: ${formatDateTime(dateTime)}',
            trailing: IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () => _playMedia(context, mediaItem),
              tooltip: 'استئناف',
            ),
            onTap: onTap ?? () => _playMedia(context, mediaItem),
          ).buildListTile(context),
          if (progressPercentage != null) ...<Widget>[
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  LinearProgressIndicator(
                    value: progressPercentage,
                    backgroundColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Text(
                        _formatDuration(positionSeconds ?? 0),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        duration != null ? _formatDuration(duration) : '--:--',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _playMedia(BuildContext context, MediaUiModel mediaItem) {
    // Always navigate to the MediaPlayerScreen regardless of media type
    context.push('${SGRoute.mediaPlayer.route}/${mediaItem.id}');
  }

  // Navigate to item details page
  void _navigateToItemDetails(BuildContext context, String itemId) {
    // Always navigate to the MediaPlayerScreen
    context.push('${SGRoute.mediaPlayer.route}/$itemId');
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(double seconds) {
    final Duration duration = Duration(seconds: seconds.round());
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes.remainder(60);
    final int secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
