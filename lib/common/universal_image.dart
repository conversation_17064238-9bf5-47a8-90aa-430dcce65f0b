import 'dart:io'; // For Platform.isWindows and File operations

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart'; // For kIsWeb
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../data/enums/image_source.dart';

class UniversalImage extends StatelessWidget {
  const UniversalImage({
    super.key,
    required this.path,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
    this.cacheKey,
    this.headers,
    this.color,
    this.colorBlendMode,
    this.semanticLabel,
  });
  final String path;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadiusGeometry? borderRadius;
  final String? cacheKey;
  final Map<String, String>? headers;
  final Color? color;
  final BlendMode? colorBlendMode;
  final String? semanticLabel;

  ImageSourceType _inferSourceType() {
    if (path.isEmpty) {
      return ImageSourceType.unknown;
    }

    final String lcPath = path.toLowerCase();

    if (lcPath.startsWith('http://') || lcPath.startsWith('https://')) {
      return lcPath.endsWith('.svg')
          ? ImageSourceType.networkSvg
          : ImageSourceType.network;
    } else if (!kIsWeb &&
        (lcPath.startsWith('/') ||
            (Platform.isWindows && RegExp(r'^[a-zA-Z]:\\').hasMatch(path)))) {
      // Basic check for absolute file paths (non-web platforms)
      return lcPath.endsWith('.svg')
          ? ImageSourceType.fileSvg
          : ImageSourceType.file;
    } else if (lcPath.startsWith('assets/')) {
      // Common convention for assets
      return lcPath.endsWith('.svg')
          ? ImageSourceType.assetSvg
          : ImageSourceType.asset;
    } else {
      // If it's not a network URL, not an obvious absolute file path (on non-web),
      // and doesn't start with 'assets/', it's ambiguous.
      // Defaulting to asset, but this might need refinement based on your project's path conventions.
      // For robustness, you might require asset paths to have a specific prefix or be listed in pubspec.
      // If a path like "my_image.png" is given, it's assumed to be an asset.
      return lcPath.endsWith('.svg')
          ? ImageSourceType.assetSvg
          : ImageSourceType.asset;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (path.isEmpty) {
      return _buildErrorWidget(context, 'Image path is empty');
    }

    final ImageSourceType inferredSourceType = _inferSourceType();
    Widget imageWidget;

    switch (inferredSourceType) {
      case ImageSourceType.network:
        imageWidget = CachedNetworkImage(
          imageUrl: path,
          width: width,
          height: height,
          fit: fit,
          httpHeaders: headers,
          cacheKey: cacheKey,
          placeholder: placeholder != null
              ? (BuildContext context, String url) => placeholder!
              : (BuildContext context, String url) =>
                  _defaultPlaceholder(context),
          errorWidget: errorWidget != null
              ? (BuildContext context, String url, Object error) => errorWidget!
              : (BuildContext context, String url, Object error) =>
                  _defaultErrorWidget(context, error),
          color: color,
          colorBlendMode: colorBlendMode,
          useOldImageOnUrlChange: true,
          fadeInDuration: const Duration(milliseconds: 300),
          fadeOutDuration: const Duration(milliseconds: 300),
          imageBuilder: borderRadius != null
              ? (BuildContext context, ImageProvider<Object> imageProvider) =>
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: borderRadius,
                      image: DecorationImage(
                        image: imageProvider,
                        fit: fit,
                        colorFilter: color != null && colorBlendMode != null
                            ? ColorFilter.mode(color!, colorBlendMode!)
                            : null,
                      ),
                    ),
                  )
              : null,
        );
        // No explicit ClipRRect wrapper needed here if imageBuilder is used for borderRadius
        break;
      case ImageSourceType.asset:
        imageWidget = Image.asset(
          path,
          width: width,
          height: height,
          fit: fit,
          color: color,
          colorBlendMode: colorBlendMode,
          semanticLabel: semanticLabel,
        );
        break;
      case ImageSourceType.networkSvg:
        imageWidget = SvgPicture.network(
          path,
          width: width,
          height: height,
          fit: fit,
          headers: headers,
          colorFilter:
              color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
          placeholderBuilder: (BuildContext context) =>
              placeholder ?? _defaultPlaceholder(context),
          semanticsLabel: semanticLabel,
        );
        break;
      case ImageSourceType.assetSvg:
        imageWidget = SvgPicture.asset(
          path,
          width: width,
          height: height,
          fit: fit,
          colorFilter:
              color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
          placeholderBuilder: (BuildContext context) =>
              placeholder ?? _defaultPlaceholder(context),
          semanticsLabel: semanticLabel,
        );
        break;
      case ImageSourceType.file:
        if (kIsWeb) {
          imageWidget = _buildErrorWidget(context,
              'File system images not supported on web directly via path.');
        } else {
          imageWidget = Image.file(
            File(path), // dart:io File
            width: width,
            height: height,
            fit: fit,
            color: color,
            colorBlendMode: colorBlendMode,
            semanticLabel: semanticLabel,
            errorBuilder:
                (BuildContext context, Object error, StackTrace? stackTrace) {
              return errorWidget ?? _defaultErrorWidget(context, error);
            },
          );
        }
        break;
      case ImageSourceType.fileSvg:
        if (kIsWeb) {
          imageWidget = _buildErrorWidget(context,
              'File system SVGs not supported on web directly via path.');
        } else {
          imageWidget = SvgPicture.file(
            File(path), // dart:io File
            width: width,
            height: height,
            fit: fit,
            colorFilter: color != null
                ? ColorFilter.mode(color!, BlendMode.srcIn)
                : null,
            placeholderBuilder: (BuildContext context) =>
                placeholder ?? _defaultPlaceholder(context),
            semanticsLabel: semanticLabel,
          );
        }
        break;
      case ImageSourceType.unknown:
        imageWidget =
            _buildErrorWidget(context, 'Unknown or invalid image path');
    }

    // Apply borderRadius universally if not handled by CachedNetworkImage's imageBuilder
    if (borderRadius != null &&
        !(inferredSourceType == ImageSourceType.network &&
            imageWidget is Container)) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }
    return imageWidget;
  }

  Widget _defaultPlaceholder(BuildContext context) {
    return Container(
      width: width,
      height: height,
      color: Theme.of(context)
          .colorScheme
          .surfaceContainerHighest
          .withValues(alpha: 0.5),
      child: const Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2.0),
        ),
      ),
    );
  }

  Widget _defaultErrorWidget(BuildContext context, Object? error) {
    // Log error if needed: debugPrint("UniversalImage Error: $error | Path: $path");
    return Container(
      width: width,
      height: height,
      color:
          Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.3),
      child: Center(
        child: Icon(
          Icons.broken_image_outlined,
          color: Theme.of(context)
              .colorScheme
              .onErrorContainer
              .withValues(alpha: 0.7),
          size: (width != null && height != null)
              ? (width! < height! ? width! * 0.5 : height! * 0.5)
              : 24.0,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Container(
      width: width,
      height: height,
      color:
          Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.error_outline,
              color: Theme.of(context)
                  .colorScheme
                  .onErrorContainer
                  .withValues(alpha: 0.7),
              size: (width != null && height != null)
                  ? (width! < height! ? width! * 0.5 : height! * 0.5)
                  : 24.0,
            ),
            if (message.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(4.0),
                child: Text(
                  message,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 10,
                    color: Theme.of(context)
                        .colorScheme
                        .onErrorContainer
                        .withValues(alpha: 0.7),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
