import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// A custom hook that debounces a value change
///
/// This hook is useful for search functionality where you want to delay
/// the actual search operation until the user stops typing.
///
/// Example usage:
/// ```dart
/// final debouncedSearchTerm = useDebounce(searchTerm, const Duration(milliseconds: 300));
/// ```
T useDebounce<T>(T value, Duration delay) {
  // Store the latest value
  final ValueNotifier<T> debouncedValue = useState<T>(value);

  // Store the timer
  final ObjectRef<Timer?> timer = useRef<Timer?>(null);

  // Update the debounced value when the input value changes
  useEffect(() {
    // Cancel the previous timer if it exists
    timer.value?.cancel();

    // Set a new timer
    timer.value = Timer(delay, () {
      debouncedValue.value = value;
    });

    // Cleanup function to cancel the timer when the hook is disposed
    return () {
      timer.value?.cancel();
      timer.value = null;
    };
  }, <Object?>[value]);

  // Return the debounced value
  return debouncedValue.value;
}

/// A custom hook that provides a debounced callback function
///
/// This hook is useful when you want to debounce a function call,
/// such as an API request or a search operation.
///
/// Example usage:
/// ```dart
/// final debouncedSearch = useDebouncedCallback(
///   (String query) => performSearch(query),
///   const Duration(milliseconds: 300),
/// );
/// ```
Function useDebouncedCallback(Function callback, Duration delay) {
  // Store the timer
  final ObjectRef<Timer?> timer = useRef<Timer?>(null);

  // Return a debounced version of the callback
  return useCallback(
    ([dynamic args]) {
      // Cancel the previous timer if it exists
      timer.value?.cancel();

      // Set a new timer
      timer.value = Timer(delay, () {
        if (args != null) {
          callback(args);
        } else {
          callback();
        }
      });
    },
    <Object?>[callback, delay],
  );
}
