import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

import '../constants/colors.dart';

class SvgIcon extends ConsumerWidget {
  const SvgIcon({
    super.key,
    required this.image,
    this.width = 24,
    this.height = 24,
    this.color,
    this.orignalColor = false,
  });
  final String image;

  final double width;
  final double height;
  final Color? color;
  final bool orignalColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SvgPicture.asset(
      image,
      colorFilter: !orignalColor
          ? ColorFilter.mode(color ?? kWhiteColor, BlendMode.srcIn)
          : null,
      width: width,
      height: height,
      semanticsLabel: 'Red dash paths',
    );
  }
}
