// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:ionicons/ionicons.dart';
// import 'package:water_drop_nav_bar/water_drop_nav_bar.dart';

// import '../../routing/app_router.dart';
// import '../../utils/context_extensions.dart';
// import 'nav_bar_logic.dart';

// class BottomNavBar extends ConsumerWidget {
//   const BottomNavBar({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     // ignore: prefer_final_locals, always_specify_types
//     var nav = ref.watch(bottomNavBarLogicProvider);

//     return Padding(
//       /// Padding
//       padding: const EdgeInsets.only(bottom: 8),
//       child: WaterDropNavBar(
//         key: const Key('bottom_nav_bar'),
//         backgroundColor: context.colorScheme.surface,
//         waterDropColor: context.colorScheme.primary,
//         selectedIndex: nav.navIndex,
//         onItemSelected: (int index) {
//           ref.read(bottomNavBarLogicProvider.notifier).setNavIndex(index);

//           switch (nav.navIndex) {
//             case 0:
//               context.go(SGRoute.firstScreen.route);
//               break;
//             case 1:
//               context.go(SGRoute.secondScreen.route);
//               break;
//             case 2:
//               context.go(SGRoute.secondScreen.route);
//               break;
//             case 3:
//               context.go(SGRoute.secondScreen.route);
//               break;
//             default:
//               context.go(SGRoute.firstScreen.route);
//           }
//           // context.go(nav.navIndex == 1
//           //     ? SGRoute.firstScreen.route
//           //     : SGRoute.secondScreen.route);
//         },
//         barItems: <BarItem>[
//           BarItem(
//             filledIcon: Ionicons.home,
//             outlinedIcon: Ionicons.home_outline,
//           ),
//           BarItem(
//             filledIcon: Ionicons.videocam,
//             outlinedIcon: Ionicons.videocam_off_outline,
//           ),
//           BarItem(
//             filledIcon: Ionicons.headset,
//             outlinedIcon: Ionicons.headset_outline,
//           ),
//           BarItem(
//             filledIcon: Ionicons.book,
//             outlinedIcon: Ionicons.book_outline,
//           ),
//         ],
//       ),
//     );
//   }
// }
