// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'nav_bar_ui_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BottomNavBarUiModel {
  int get navIndex;

  /// Create a copy of BottomNavBarUiModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BottomNavBarUiModelCopyWith<BottomNavBarUiModel> get copyWith =>
      _$BottomNavBarUiModelCopyWithImpl<BottomNavBarUiModel>(
          this as BottomNavBarUiModel, _$identity);

  /// Serializes this BottomNavBarUiModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BottomNavBarUiModel &&
            (identical(other.navIndex, navIndex) ||
                other.navIndex == navIndex));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, navIndex);

  @override
  String toString() {
    return 'BottomNavBarUiModel(navIndex: $navIndex)';
  }
}

/// @nodoc
abstract mixin class $BottomNavBarUiModelCopyWith<$Res> {
  factory $BottomNavBarUiModelCopyWith(
          BottomNavBarUiModel value, $Res Function(BottomNavBarUiModel) _then) =
      _$BottomNavBarUiModelCopyWithImpl;
  @useResult
  $Res call({int navIndex});
}

/// @nodoc
class _$BottomNavBarUiModelCopyWithImpl<$Res>
    implements $BottomNavBarUiModelCopyWith<$Res> {
  _$BottomNavBarUiModelCopyWithImpl(this._self, this._then);

  final BottomNavBarUiModel _self;
  final $Res Function(BottomNavBarUiModel) _then;

  /// Create a copy of BottomNavBarUiModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? navIndex = null,
  }) {
    return _then(_self.copyWith(
      navIndex: null == navIndex
          ? _self.navIndex
          : navIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _BottomNavBarUiModel implements BottomNavBarUiModel {
  const _BottomNavBarUiModel({this.navIndex = 0});
  factory _BottomNavBarUiModel.fromJson(Map<String, dynamic> json) =>
      _$BottomNavBarUiModelFromJson(json);

  @override
  @JsonKey()
  final int navIndex;

  /// Create a copy of BottomNavBarUiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BottomNavBarUiModelCopyWith<_BottomNavBarUiModel> get copyWith =>
      __$BottomNavBarUiModelCopyWithImpl<_BottomNavBarUiModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BottomNavBarUiModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BottomNavBarUiModel &&
            (identical(other.navIndex, navIndex) ||
                other.navIndex == navIndex));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, navIndex);

  @override
  String toString() {
    return 'BottomNavBarUiModel(navIndex: $navIndex)';
  }
}

/// @nodoc
abstract mixin class _$BottomNavBarUiModelCopyWith<$Res>
    implements $BottomNavBarUiModelCopyWith<$Res> {
  factory _$BottomNavBarUiModelCopyWith(_BottomNavBarUiModel value,
          $Res Function(_BottomNavBarUiModel) _then) =
      __$BottomNavBarUiModelCopyWithImpl;
  @override
  @useResult
  $Res call({int navIndex});
}

/// @nodoc
class __$BottomNavBarUiModelCopyWithImpl<$Res>
    implements _$BottomNavBarUiModelCopyWith<$Res> {
  __$BottomNavBarUiModelCopyWithImpl(this._self, this._then);

  final _BottomNavBarUiModel _self;
  final $Res Function(_BottomNavBarUiModel) _then;

  /// Create a copy of BottomNavBarUiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? navIndex = null,
  }) {
    return _then(_BottomNavBarUiModel(
      navIndex: null == navIndex
          ? _self.navIndex
          : navIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
