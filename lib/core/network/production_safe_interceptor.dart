import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../error/app_error.dart';
import '../logging/app_logger.dart';

/// Production-safe network interceptor with comprehensive logging and error handling
class ProductionSafeInterceptor extends Interceptor {
  ProductionSafeInterceptor({
    this.enableRequestLogging = kDebugMode,
    this.enableResponseLogging = kDebugMode,
    this.enableErrorLogging = true,
    this.logHeaders = kDebugMode,
    this.logRequestBody = kDebugMode,
    this.logResponseBody = kDebugMode,
  });

  final bool enableRequestLogging;
  final bool enableResponseLogging;
  final bool enableErrorLogging;
  final bool logHeaders;
  final bool logRequestBody;
  final bool logResponseBody;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (enableRequestLogging) {
      _logRequest(options);
    }

    // Add request timestamp for performance monitoring
    options.extra['request_start_time'] = DateTime.now().millisecondsSinceEpoch;

    super.onRequest(options, handler);
  }

  @override
  void onResponse(
      Response<dynamic> response, ResponseInterceptorHandler handler) {
    if (enableResponseLogging) {
      _logResponse(response);
    }

    _logPerformance(response.requestOptions);

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (enableErrorLogging) {
      _logError(err);
    }

    _logPerformance(err.requestOptions);

    // Convert DioException to AppError
    final AppError appError = _convertDioExceptionToAppError(err);

    // Create a new DioException with our AppError
    final DioException enhancedError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: appError,
      message: appError.userMessage,
      stackTrace: err.stackTrace,
    );

    super.onError(enhancedError, handler);
  }

  void _logRequest(RequestOptions options) {
    final StringBuffer logBuffer = StringBuffer();
    logBuffer.writeln('🌐 HTTP Request');
    logBuffer.writeln('Method: ${options.method}');
    logBuffer.writeln('URL: ${options.uri}');

    if (logHeaders && options.headers.isNotEmpty) {
      logBuffer.writeln('Headers:');
      options.headers.forEach((String key, dynamic value) {
        // Don't log sensitive headers in production
        if (_isSensitiveHeader(key)) {
          logBuffer.writeln('  $key: [REDACTED]');
        } else {
          logBuffer.writeln('  $key: $value');
        }
      });
    }

    if (logRequestBody && options.data != null) {
      logBuffer.writeln('Body: ${_sanitizeBody(options.data)}');
    }

    AppLogger.debug(logBuffer.toString());
  }

  void _logResponse(Response<dynamic> response) {
    final StringBuffer logBuffer = StringBuffer();
    final String emoji = _getStatusEmoji(response.statusCode ?? 0);

    logBuffer.writeln('$emoji HTTP Response');
    logBuffer
        .writeln('Status: ${response.statusCode} ${response.statusMessage}');
    logBuffer.writeln('URL: ${response.requestOptions.uri}');

    if (logHeaders && response.headers.map.isNotEmpty) {
      logBuffer.writeln('Headers:');
      response.headers.map.forEach((String key, List<String> value) {
        logBuffer.writeln('  $key: ${value.join(', ')}');
      });
    }

    if (logResponseBody && response.data != null) {
      final String bodyPreview = _getBodyPreview(response.data);
      logBuffer.writeln('Body: $bodyPreview');
    }

    AppLogger.debug(logBuffer.toString());
  }

  void _logError(DioException err) {
    final StringBuffer logBuffer = StringBuffer();
    logBuffer.writeln('❌ HTTP Error');
    logBuffer.writeln('Type: ${err.type}');
    logBuffer.writeln('Method: ${err.requestOptions.method}');
    logBuffer.writeln('URL: ${err.requestOptions.uri}');

    if (err.response != null) {
      logBuffer.writeln(
          'Status: ${err.response!.statusCode} ${err.response!.statusMessage}');
      if (err.response!.data != null) {
        logBuffer.writeln('Error Body: ${_getBodyPreview(err.response!.data)}');
      }
    }

    if (err.message != null) {
      logBuffer.writeln('Message: ${err.message}');
    }

    AppLogger.error(logBuffer.toString(),
        error: err, stackTrace: err.stackTrace);
  }

  void _logPerformance(RequestOptions options) {
    final int? startTime = options.extra['request_start_time'] as int?;
    if (startTime != null) {
      final int duration = DateTime.now().millisecondsSinceEpoch - startTime;
      AppLogger.performance(
        '${options.method} ${options.path}',
        Duration(milliseconds: duration),
        data: <String, dynamic>{
          'url': options.uri.toString(),
          'method': options.method,
        },
      );
    }
  }

  AppError _convertDioExceptionToAppError(DioException err) {
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppError.timeout(
          message: 'Request timeout: ${err.message ?? 'Unknown timeout error'}',
          duration: const Duration(
            milliseconds: 3000,
            // milliseconds: err.requestOptions.connectTimeout ??
            //     err.requestOptions.sendTimeout ??
            //     err.requestOptions.receiveTimeout ??
            //     30000,
          ),
        );

      case DioExceptionType.badResponse:
        return AppError.network(
          message: err.message ?? 'Bad response from server',
          statusCode: err.response?.statusCode,
          details: err.response?.data?.toString(),
          isRetryable: _isRetryableStatusCode(err.response?.statusCode),
        );

      case DioExceptionType.cancel:
        return const AppError.network(
          message: 'Request was cancelled',
        );

      case DioExceptionType.connectionError:
        return AppError.network(
          message:
              'Connection error: ${err.message ?? 'Unable to connect to server'}',
          isRetryable: true,
        );

      case DioExceptionType.badCertificate:
        return AppError.network(
          message:
              'SSL certificate error: ${err.message ?? 'Invalid certificate'}',
        );

      case DioExceptionType.unknown:
        return AppError.network(
          message: err.message ?? 'Unknown network error occurred',
          isRetryable: true,
        );
    }
  }

  bool _isRetryableStatusCode(int? statusCode) {
    if (statusCode == null) {
      return true;
    }

    // Retry on server errors (5xx) and specific client errors
    return statusCode >= 500 ||
        statusCode == 408 || // Request Timeout
        statusCode == 429; // Too Many Requests
  }

  bool _isSensitiveHeader(String headerName) {
    final String lowerName = headerName.toLowerCase();
    return lowerName.contains('authorization') ||
        lowerName.contains('token') ||
        lowerName.contains('key') ||
        lowerName.contains('secret') ||
        lowerName.contains('password') ||
        lowerName.contains('cookie');
  }

  String _sanitizeBody(dynamic body) {
    if (body == null) {
      return 'null';
    }

    final String bodyString = body.toString();

    // In production, limit body size and sanitize sensitive data
    if (kReleaseMode) {
      // Truncate large bodies
      if (bodyString.length > 500) {
        return '${bodyString.substring(0, 500)}... [TRUNCATED]';
      }

      // TODO: Add regex patterns to redact sensitive data like emails, phones, etc.
      return bodyString;
    }

    return bodyString;
  }

  String _getBodyPreview(dynamic body) {
    if (body == null) {
      return 'null';
    }

    final String bodyString = body.toString();

    // Always limit preview size
    if (bodyString.length > 1000) {
      return '${bodyString.substring(0, 1000)}... [TRUNCATED]';
    }

    return bodyString;
  }

  String _getStatusEmoji(int statusCode) {
    if (statusCode >= 200 && statusCode < 300) {
      return '✅'; // Success
    } else if (statusCode >= 300 && statusCode < 400) {
      return '🔄'; // Redirect
    } else if (statusCode >= 400 && statusCode < 500) {
      return '⚠️'; // Client Error
    } else if (statusCode >= 500) {
      return '❌'; // Server Error
    } else {
      return '❓'; // Unknown
    }
  }
}

/// Retry interceptor for handling retryable requests
class RetryInterceptor extends Interceptor {
  RetryInterceptor({
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.retryDelayMultiplier = 2.0,
  });

  final int maxRetries;
  final Duration retryDelay;
  final double retryDelayMultiplier;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final int retryCount = err.requestOptions.extra['retry_count'] as int? ?? 0;

    if (_shouldRetry(err) && retryCount < maxRetries) {
      _retry(err, handler, retryCount);
    } else {
      super.onError(err, handler);
    }
  }

  bool _shouldRetry(DioException err) {
    // Only retry on specific error types
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return true;
      case DioExceptionType.badResponse:
        // Retry on server errors and specific client errors
        final int? statusCode = err.response?.statusCode;
        return statusCode != null &&
            (statusCode >= 500 || statusCode == 408 || statusCode == 429);
      case DioExceptionType.badCertificate:
        // Don't retry on certificate errors as they're unlikely to be resolved with retries
        return false;
      case DioExceptionType.cancel:
        // Don't retry on canceled requests as they were intentionally canceled
        return false;
      case DioExceptionType.unknown:
        // Retry on unknown errors as they might be temporary network issues
        return true;
    }
  }

  void _retry(
      DioException err, ErrorInterceptorHandler handler, int retryCount) {
    final int newRetryCount = retryCount + 1;
    final Duration delay = Duration(
      milliseconds:
          (retryDelay.inMilliseconds * (retryDelayMultiplier * newRetryCount))
              .round(),
    );

    AppLogger.warning(
      'Retrying request (attempt $newRetryCount/$maxRetries) after ${delay.inMilliseconds}ms',
      data: <String, dynamic>{
        'url': err.requestOptions.uri.toString(),
        'method': err.requestOptions.method,
        'error_type': err.type.toString(),
      },
    );

    Future<void>.delayed(delay, () {
      err.requestOptions.extra['retry_count'] = newRetryCount;

      final Dio dio = Dio();
      dio.fetch<dynamic>(err.requestOptions).then(
        (Response<dynamic> response) => handler.resolve(response),
        onError: (Object error) {
          if (error is DioException) {
            handler.reject(error);
          } else {
            // Create a new DioException if the error is not already one
            handler.reject(
              DioException(
                requestOptions: err.requestOptions,
                error: error,
                message: error.toString(),
              ),
            );
          }
        },
      );
    });
  }
}
