import 'dart:async';
import 'dart:collection';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../logging/app_logger.dart';

/// Performance monitoring system for the application
class PerformanceMonitor {
  PerformanceMonitor._();

  static final PerformanceMonitor _instance = PerformanceMonitor._();
  static PerformanceMonitor get instance => _instance;

  final Map<String, _PerformanceMetric> _metrics =
      <String, _PerformanceMetric>{};
  final Queue<_FrameMetric> _frameMetrics = Queue<_FrameMetric>();
  final int _maxFrameMetrics = 100;

  Timer? _reportingTimer;
  bool _isInitialized = false;
  Duration? _lastFrameTime;

  /// Initialize performance monitoring
  void initialize() {
    if (_isInitialized) {
      return;
    }

    // Monitor frame rendering performance
    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);

    // Start periodic reporting in debug mode
    if (kDebugMode) {
      _reportingTimer = Timer.periodic(
        const Duration(minutes: 1),
        (_) => _reportPerformanceMetrics(),
      );
    }

    _isInitialized = true;
    AppLogger.info('🔍 Performance monitoring initialized');
  }

  /// Dispose performance monitoring
  void dispose() {
    _reportingTimer?.cancel();
    _reportingTimer = null;
    _isInitialized = false;
  }

  /// Start measuring a performance metric
  void startMeasurement(String name, {Map<String, dynamic>? metadata}) {
    _metrics[name] = _PerformanceMetric(
      name: name,
      startTime: DateTime.now(),
      metadata: metadata ?? <String, dynamic>{},
    );
  }

  /// End measuring a performance metric
  Duration? endMeasurement(String name,
      {Map<String, dynamic>? additionalMetadata}) {
    final _PerformanceMetric? metric = _metrics.remove(name);
    if (metric == null) {
      AppLogger.warning('Performance metric "$name" was not started');
      return null;
    }

    final DateTime endTime = DateTime.now();
    final Duration duration = endTime.difference(metric.startTime);

    // Combine metadata
    final Map<String, dynamic> allMetadata = <String, dynamic>{
      ...metric.metadata,
      if (additionalMetadata != null) ...additionalMetadata,
    };

    // Log performance metric
    AppLogger.performance(name, duration, data: allMetadata);

    // Report slow operations
    if (duration.inMilliseconds > 1000) {
      AppLogger.warning(
        '🐌 Slow operation detected: $name took ${duration.inMilliseconds}ms',
        data: allMetadata,
      );
    }

    return duration;
  }

  /// Measure a synchronous operation
  T measureSync<T>(String name, T Function() operation,
      {Map<String, dynamic>? metadata}) {
    startMeasurement(name, metadata: metadata);
    try {
      final T result = operation();
      endMeasurement(name);
      return result;
    } catch (e) {
      endMeasurement(name,
          additionalMetadata: <String, dynamic>{'error': e.toString()});
      rethrow;
    }
  }

  /// Measure an asynchronous operation
  Future<T> measureAsync<T>(
    String name,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    startMeasurement(name, metadata: metadata);
    try {
      final T result = await operation();
      endMeasurement(name);
      return result;
    } catch (e) {
      endMeasurement(name,
          additionalMetadata: <String, dynamic>{'error': e.toString()});
      rethrow;
    }
  }

  /// Record a custom metric
  void recordMetric(String name, double value,
      {String? unit, Map<String, dynamic>? metadata}) {
    AppLogger.info(
      '📊 Custom metric: $name = $value${unit != null ? ' $unit' : ''}',
      data: metadata,
    );
  }

  /// Record memory usage
  void recordMemoryUsage() {
    // This would typically integrate with platform-specific memory APIs
    AppLogger.debug('💾 Memory usage recorded');
  }

  /// Get frame rate statistics
  FrameRateStats getFrameRateStats() {
    if (_frameMetrics.isEmpty) {
      return FrameRateStats(
        averageFps: 0,
        minFps: 0,
        maxFps: 0,
        droppedFrames: 0,
        totalFrames: 0,
      );
    }

    final List<double> fpsList =
        _frameMetrics.map((_FrameMetric metric) => metric.fps).toList();
    final double averageFps =
        fpsList.reduce((double a, double b) => a + b) / fpsList.length;
    final double minFps = fpsList.reduce((double a, double b) => a < b ? a : b);
    final double maxFps = fpsList.reduce((double a, double b) => a > b ? a : b);
    final int droppedFrames =
        _frameMetrics.where((_FrameMetric metric) => metric.fps < 55).length;

    return FrameRateStats(
      averageFps: averageFps,
      minFps: minFps,
      maxFps: maxFps,
      droppedFrames: droppedFrames,
      totalFrames: _frameMetrics.length,
    );
  }

  /// Clear all metrics
  void clearMetrics() {
    _metrics.clear();
    _frameMetrics.clear();
    AppLogger.debug('🧹 Performance metrics cleared');
  }

  void _onFrame(Duration timestamp) {
    // Calculate FPS based on frame duration
    if (_lastFrameTime != null) {
      final Duration frameDuration = timestamp - _lastFrameTime!;
      final double fps = frameDuration.inMicroseconds > 0
          ? 1000000.0 / frameDuration.inMicroseconds
          : 0.0;

      _frameMetrics.add(_FrameMetric(timestamp: timestamp, fps: fps));

      // Keep only recent frame metrics
      while (_frameMetrics.length > _maxFrameMetrics) {
        _frameMetrics.removeFirst();
      }

      // Log frame drops in debug mode
      if (kDebugMode && fps < 55) {
        AppLogger.debug(
            '📉 Frame drop detected: ${fps.toStringAsFixed(1)} FPS');
      }
    }

    _lastFrameTime = timestamp;
  }

  void _reportPerformanceMetrics() {
    final FrameRateStats frameStats = getFrameRateStats();

    AppLogger.info(
      '📊 Performance Report',
      data: <String, dynamic>{
        'average_fps': frameStats.averageFps.toStringAsFixed(1),
        'min_fps': frameStats.minFps.toStringAsFixed(1),
        'max_fps': frameStats.maxFps.toStringAsFixed(1),
        'dropped_frames': frameStats.droppedFrames,
        'total_frames': frameStats.totalFrames,
        'frame_drop_rate': frameStats.totalFrames > 0
            ? '${(frameStats.droppedFrames / frameStats.totalFrames * 100).toStringAsFixed(1)}%'
            : '0%',
      },
    );
  }
}

/// Performance metric data class
class _PerformanceMetric {
  _PerformanceMetric({
    required this.name,
    required this.startTime,
    required this.metadata,
  });

  final String name;
  final DateTime startTime;
  final Map<String, dynamic> metadata;
}

/// Frame metric data class
class _FrameMetric {
  _FrameMetric({
    required this.timestamp,
    required this.fps,
  });

  final Duration timestamp;
  final double fps;
}

/// Frame rate statistics
class FrameRateStats {
  FrameRateStats({
    required this.averageFps,
    required this.minFps,
    required this.maxFps,
    required this.droppedFrames,
    required this.totalFrames,
  });

  final double averageFps;
  final double minFps;
  final double maxFps;
  final int droppedFrames;
  final int totalFrames;

  double get frameDropRate =>
      totalFrames > 0 ? droppedFrames / totalFrames : 0.0;

  bool get isPerformanceGood => averageFps >= 55 && frameDropRate < 0.05;
}

/// Mixin for widgets that need performance monitoring
mixin PerformanceMonitorMixin {
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor.instance;

  void startPerformanceMeasurement(String name,
      {Map<String, dynamic>? metadata}) {
    _performanceMonitor.startMeasurement(name, metadata: metadata);
  }

  Duration? endPerformanceMeasurement(String name,
      {Map<String, dynamic>? additionalMetadata}) {
    return _performanceMonitor.endMeasurement(name,
        additionalMetadata: additionalMetadata);
  }

  T measurePerformanceSync<T>(String name, T Function() operation,
      {Map<String, dynamic>? metadata}) {
    return _performanceMonitor.measureSync(name, operation, metadata: metadata);
  }

  Future<T> measurePerformanceAsync<T>(
    String name,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) {
    return _performanceMonitor.measureAsync(name, operation,
        metadata: metadata);
  }
}

/// Widget performance tracker
class PerformanceTracker extends ConsumerStatefulWidget {
  const PerformanceTracker({
    super.key,
    required this.name,
    required this.child,
    this.metadata,
  });

  final String name;
  final Widget child;
  final Map<String, dynamic>? metadata;

  @override
  ConsumerState<PerformanceTracker> createState() => _PerformanceTrackerState();
}

class _PerformanceTrackerState extends ConsumerState<PerformanceTracker> {
  @override
  void initState() {
    super.initState();
    PerformanceMonitor.instance.startMeasurement(
      'widget_${widget.name}_build',
      metadata: widget.metadata,
    );
  }

  @override
  void dispose() {
    PerformanceMonitor.instance.endMeasurement('widget_${widget.name}_build');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
