import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

/// Comprehensive error handling for the application
@freezed
class AppError with _$AppError implements Exception {
  const AppError._();

  /// Network-related errors
  const factory AppError.network({
    required String message,
    int? statusCode,
    String? details,
    @Default(false) bool isRetryable,
  }) = NetworkError;

  /// Storage/Database errors
  const factory AppError.storage({
    required String message,
    String? details,
    String? operation,
  }) = StorageError;

  /// Validation errors
  const factory AppError.validation({
    required String message,
    Map<String, String>? fieldErrors,
  }) = ValidationError;

  /// Authentication/Authorization errors
  const factory AppError.auth({
    required String message,
    @Default(false) bool requiresReauth,
  }) = AuthError;

  /// Media playback errors
  const factory AppError.media({
    required String message,
    String? mediaType,
    String? mediaId,
  }) = MediaError;

  /// File system errors
  const factory AppError.file({
    required String message,
    String? filePath,
    String? operation,
  }) = FileError;

  /// Permission errors
  const factory AppError.permission({
    required String message,
    String? permission,
  }) = PermissionError;

  /// Unknown/Unexpected errors
  const factory AppError.unknown({
    required String message,
    Object? originalError,
    StackTrace? stackTrace,
  }) = UnknownError;

  /// Timeout errors
  const factory AppError.timeout({
    required String message,
    Duration? duration,
  }) = TimeoutError;

  /// Cache errors
  const factory AppError.cache({
    required String message,
    String? cacheKey,
  }) = CacheError;

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);

  /// Manual implementation of when method since Freezed isn't generating it
  T when<T>({
    required T Function(
            String message, int? statusCode, String? details, bool isRetryable)
        network,
    required T Function(String message, String? details, String? operation)
        storage,
    required T Function(String message, Map<String, String>? fieldErrors)
        validation,
    required T Function(String message, bool requiresReauth) auth,
    required T Function(String message, String? mediaType, String? mediaId)
        media,
    required T Function(String message, String? filePath, String? operation)
        file,
    required T Function(String message, String? permission) permission,
    required T Function(String message, Duration? duration) timeout,
    required T Function(String message, String? cacheKey) cache,
    required T Function(
            String message, Object? originalError, StackTrace? stackTrace)
        unknown,
  }) {
    if (this is NetworkError) {
      final NetworkError error = this as NetworkError;
      return network(
          error.message, error.statusCode, error.details, error.isRetryable);
    } else if (this is StorageError) {
      final StorageError error = this as StorageError;
      return storage(error.message, error.details, error.operation);
    } else if (this is ValidationError) {
      final ValidationError error = this as ValidationError;
      return validation(error.message, error.fieldErrors);
    } else if (this is AuthError) {
      final AuthError error = this as AuthError;
      return auth(error.message, error.requiresReauth);
    } else if (this is MediaError) {
      final MediaError error = this as MediaError;
      return media(error.message, error.mediaType, error.mediaId);
    } else if (this is FileError) {
      final FileError error = this as FileError;
      return file(error.message, error.filePath, error.operation);
    } else if (this is PermissionError) {
      final PermissionError error = this as PermissionError;
      return permission(error.message, error.permission);
    } else if (this is TimeoutError) {
      final TimeoutError error = this as TimeoutError;
      return timeout(error.message, error.duration);
    } else if (this is CacheError) {
      final CacheError error = this as CacheError;
      return cache(error.message, error.cacheKey);
    } else if (this is UnknownError) {
      final UnknownError error = this as UnknownError;
      return unknown(error.message, error.originalError, error.stackTrace);
    } else {
      throw StateError('Unknown AppError type: $runtimeType');
    }
  }
}

/// Extension to provide user-friendly error messages
extension AppErrorExtension on AppError {
  /// Get user-friendly error message in Arabic
  String get userMessage {
    return when(
      network:
          (String message, int? statusCode, String? details, bool isRetryable) {
        if (statusCode != null) {
          switch (statusCode) {
            case 400:
              return 'طلب غير صحيح. يرجى المحاولة مرة أخرى.';
            case 401:
              return 'يرجى تسجيل الدخول مرة أخرى.';
            case 403:
              return 'ليس لديك صلاحية للوصول إلى هذا المحتوى.';
            case 404:
              return 'المحتوى المطلوب غير موجود.';
            case 500:
              return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
            default:
              return 'خطأ في الشبكة. يرجى التحقق من اتصالك بالإنترنت.';
          }
        }
        return 'خطأ في الشبكة. يرجى التحقق من اتصالك بالإنترنت.';
      },
      storage: (String message, String? details, String? operation) =>
          'خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.',
      validation: (String message, Map<String, String>? fieldErrors) =>
          'يرجى التحقق من البيانات المدخلة.',
      auth: (String message, bool requiresReauth) =>
          'خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.',
      media: (String message, String? mediaType, String? mediaId) =>
          'خطأ في تشغيل الوسائط. يرجى المحاولة مرة أخرى.',
      file: (String message, String? filePath, String? operation) =>
          'خطأ في الوصول إلى الملف. يرجى المحاولة مرة أخرى.',
      permission: (String message, String? permission) =>
          'يرجى منح الصلاحيات المطلوبة للتطبيق.',
      timeout: (String message, Duration? duration) =>
          'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.',
      cache: (String message, String? cacheKey) =>
          'خطأ في التخزين المؤقت. يرجى المحاولة مرة أخرى.',
      unknown:
          (String message, Object? originalError, StackTrace? stackTrace) =>
              'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
    );
  }

  /// Check if the error is retryable
  bool get isRetryable {
    return when(
      network: (String message, int? statusCode, String? details,
              bool isRetryable) =>
          isRetryable,
      storage: (String message, String? details, String? operation) => true,
      validation: (String message, Map<String, String>? fieldErrors) => false,
      auth: (String message, bool requiresReauth) => false,
      media: (String message, String? mediaType, String? mediaId) => true,
      file: (String message, String? filePath, String? operation) => true,
      permission: (String message, String? permission) => false,
      timeout: (String message, Duration? duration) => true,
      cache: (String message, String? cacheKey) => true,
      unknown:
          (String message, Object? originalError, StackTrace? stackTrace) =>
              true,
    );
  }

  /// Get error severity level
  ErrorSeverity get severity {
    return when(
      network:
          (String message, int? statusCode, String? details, bool isRetryable) {
        if (statusCode != null && statusCode >= 500) {
          return ErrorSeverity.high;
        }
        return ErrorSeverity.medium;
      },
      storage: (String message, String? details, String? operation) =>
          ErrorSeverity.high,
      validation: (String message, Map<String, String>? fieldErrors) =>
          ErrorSeverity.low,
      auth: (String message, bool requiresReauth) => ErrorSeverity.medium,
      media: (String message, String? mediaType, String? mediaId) =>
          ErrorSeverity.medium,
      file: (String message, String? filePath, String? operation) =>
          ErrorSeverity.medium,
      permission: (String message, String? permission) => ErrorSeverity.medium,
      timeout: (String message, Duration? duration) => ErrorSeverity.medium,
      cache: (String message, String? cacheKey) => ErrorSeverity.low,
      unknown:
          (String message, Object? originalError, StackTrace? stackTrace) =>
              ErrorSeverity.high,
    );
  }
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Helper functions to create common errors

/// Create network error from DioException
AppError fromDioException(Object dioException) {
  String message = 'Network error occurred';
  int? statusCode;
  String? details;

  if (dioException is DioException) {
    // Handle DioException properly with type safety
    message = dioException.message ?? 'Network error occurred';
    statusCode = dioException.response?.statusCode;
    details = dioException.response?.data?.toString();
  } else {
    // Fallback for when the object is not a DioException
    try {
      // Convert to string for safe extraction
      final String exceptionString = dioException.toString();

      // Extract message if available
      final RegExp messageRegex = RegExp(r'message:\s*([^,\n]+)');
      final Match? messageMatch = messageRegex.firstMatch(exceptionString);
      if (messageMatch != null && messageMatch.groupCount >= 1) {
        message = messageMatch.group(1)?.trim() ?? message;
      }

      // Extract status code if available
      final RegExp statusCodeRegex = RegExp(r'statusCode:\s*(\d+)');
      final Match? statusCodeMatch =
          statusCodeRegex.firstMatch(exceptionString);
      if (statusCodeMatch != null && statusCodeMatch.groupCount >= 1) {
        final String? statusCodeStr = statusCodeMatch.group(1);
        if (statusCodeStr != null) {
          statusCode = int.tryParse(statusCodeStr);
        }
      }

      // Extract response data if available
      final RegExp dataRegex = RegExp(r'data:\s*([^,\n]+)');
      final Match? dataMatch = dataRegex.firstMatch(exceptionString);
      if (dataMatch != null && dataMatch.groupCount >= 1) {
        details = dataMatch.group(1)?.trim();
      }
    } catch (e) {
      // Fallback to defaults if extraction fails
      message = 'Network error occurred';
    }
  }

  return AppError.network(
    message: message,
    statusCode: statusCode,
    details: details,
    isRetryable: _isRetryableStatusCode(statusCode),
  );
}

/// Create storage error
AppError storageError(String message, {String? operation}) {
  return AppError.storage(
    message: message,
    operation: operation,
  );
}

/// Create validation error
AppError validationError(String message, {Map<String, String>? fieldErrors}) {
  return AppError.validation(
    message: message,
    fieldErrors: fieldErrors,
  );
}

/// Create unknown error
AppError unknownError(String message,
    {Object? originalError, StackTrace? stackTrace}) {
  return AppError.unknown(
    message: message,
    originalError: originalError,
    stackTrace: stackTrace,
  );
}

bool _isRetryableStatusCode(int? statusCode) {
  if (statusCode == null) {
    return true;
  }
  return statusCode >= 500 || statusCode == 408 || statusCode == 429;
}
