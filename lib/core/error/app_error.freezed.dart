// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppError {
  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<AppError> get copyWith =>
      _$AppErrorCopyWithImpl<AppError>(this as AppError, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppError &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'AppError(message: $message)';
  }
}

/// @nodoc
abstract mixin class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) _then) =
      _$AppErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res> implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._self, this._then);

  final AppError _self;
  final $Res Function(AppError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_self.copyWith(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class NetworkError extends AppError {
  const NetworkError(
      {required this.message,
      this.statusCode,
      this.details,
      this.isRetryable = false})
      : super._();

  @override
  final String message;
  final int? statusCode;
  final String? details;
  @JsonKey()
  final bool isRetryable;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NetworkErrorCopyWith<NetworkError> get copyWith =>
      _$NetworkErrorCopyWithImpl<NetworkError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NetworkError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.isRetryable, isRetryable) ||
                other.isRetryable == isRetryable));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, message, statusCode, details, isRetryable);

  @override
  String toString() {
    return 'AppError.network(message: $message, statusCode: $statusCode, details: $details, isRetryable: $isRetryable)';
  }
}

/// @nodoc
abstract mixin class $NetworkErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $NetworkErrorCopyWith(
          NetworkError value, $Res Function(NetworkError) _then) =
      _$NetworkErrorCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String message, int? statusCode, String? details, bool isRetryable});
}

/// @nodoc
class _$NetworkErrorCopyWithImpl<$Res> implements $NetworkErrorCopyWith<$Res> {
  _$NetworkErrorCopyWithImpl(this._self, this._then);

  final NetworkError _self;
  final $Res Function(NetworkError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? statusCode = freezed,
    Object? details = freezed,
    Object? isRetryable = null,
  }) {
    return _then(NetworkError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: freezed == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      details: freezed == details
          ? _self.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      isRetryable: null == isRetryable
          ? _self.isRetryable
          : isRetryable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class StorageError extends AppError {
  const StorageError({required this.message, this.details, this.operation})
      : super._();

  @override
  final String message;
  final String? details;
  final String? operation;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $StorageErrorCopyWith<StorageError> get copyWith =>
      _$StorageErrorCopyWithImpl<StorageError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is StorageError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.operation, operation) ||
                other.operation == operation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, details, operation);

  @override
  String toString() {
    return 'AppError.storage(message: $message, details: $details, operation: $operation)';
  }
}

/// @nodoc
abstract mixin class $StorageErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $StorageErrorCopyWith(
          StorageError value, $Res Function(StorageError) _then) =
      _$StorageErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, String? details, String? operation});
}

/// @nodoc
class _$StorageErrorCopyWithImpl<$Res> implements $StorageErrorCopyWith<$Res> {
  _$StorageErrorCopyWithImpl(this._self, this._then);

  final StorageError _self;
  final $Res Function(StorageError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? details = freezed,
    Object? operation = freezed,
  }) {
    return _then(StorageError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _self.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      operation: freezed == operation
          ? _self.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class ValidationError extends AppError {
  const ValidationError(
      {required this.message, final Map<String, String>? fieldErrors})
      : _fieldErrors = fieldErrors,
        super._();

  @override
  final String message;
  final Map<String, String>? _fieldErrors;
  Map<String, String>? get fieldErrors {
    final value = _fieldErrors;
    if (value == null) return null;
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<ValidationError> get copyWith =>
      _$ValidationErrorCopyWithImpl<ValidationError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ValidationError &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, message, const DeepCollectionEquality().hash(_fieldErrors));

  @override
  String toString() {
    return 'AppError.validation(message: $message, fieldErrors: $fieldErrors)';
  }
}

/// @nodoc
abstract mixin class $ValidationErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $ValidationErrorCopyWith(
          ValidationError value, $Res Function(ValidationError) _then) =
      _$ValidationErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, Map<String, String>? fieldErrors});
}

/// @nodoc
class _$ValidationErrorCopyWithImpl<$Res>
    implements $ValidationErrorCopyWith<$Res> {
  _$ValidationErrorCopyWithImpl(this._self, this._then);

  final ValidationError _self;
  final $Res Function(ValidationError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? fieldErrors = freezed,
  }) {
    return _then(ValidationError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      fieldErrors: freezed == fieldErrors
          ? _self._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>?,
    ));
  }
}

/// @nodoc

class AuthError extends AppError {
  const AuthError({required this.message, this.requiresReauth = false})
      : super._();

  @override
  final String message;
  @JsonKey()
  final bool requiresReauth;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AuthErrorCopyWith<AuthError> get copyWith =>
      _$AuthErrorCopyWithImpl<AuthError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AuthError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.requiresReauth, requiresReauth) ||
                other.requiresReauth == requiresReauth));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, requiresReauth);

  @override
  String toString() {
    return 'AppError.auth(message: $message, requiresReauth: $requiresReauth)';
  }
}

/// @nodoc
abstract mixin class $AuthErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $AuthErrorCopyWith(AuthError value, $Res Function(AuthError) _then) =
      _$AuthErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, bool requiresReauth});
}

/// @nodoc
class _$AuthErrorCopyWithImpl<$Res> implements $AuthErrorCopyWith<$Res> {
  _$AuthErrorCopyWithImpl(this._self, this._then);

  final AuthError _self;
  final $Res Function(AuthError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? requiresReauth = null,
  }) {
    return _then(AuthError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      requiresReauth: null == requiresReauth
          ? _self.requiresReauth
          : requiresReauth // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class MediaError extends AppError {
  const MediaError({required this.message, this.mediaType, this.mediaId})
      : super._();

  @override
  final String message;
  final String? mediaType;
  final String? mediaId;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaErrorCopyWith<MediaError> get copyWith =>
      _$MediaErrorCopyWithImpl<MediaError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.mediaType, mediaType) ||
                other.mediaType == mediaType) &&
            (identical(other.mediaId, mediaId) || other.mediaId == mediaId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, mediaType, mediaId);

  @override
  String toString() {
    return 'AppError.media(message: $message, mediaType: $mediaType, mediaId: $mediaId)';
  }
}

/// @nodoc
abstract mixin class $MediaErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $MediaErrorCopyWith(
          MediaError value, $Res Function(MediaError) _then) =
      _$MediaErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, String? mediaType, String? mediaId});
}

/// @nodoc
class _$MediaErrorCopyWithImpl<$Res> implements $MediaErrorCopyWith<$Res> {
  _$MediaErrorCopyWithImpl(this._self, this._then);

  final MediaError _self;
  final $Res Function(MediaError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? mediaType = freezed,
    Object? mediaId = freezed,
  }) {
    return _then(MediaError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      mediaType: freezed == mediaType
          ? _self.mediaType
          : mediaType // ignore: cast_nullable_to_non_nullable
              as String?,
      mediaId: freezed == mediaId
          ? _self.mediaId
          : mediaId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class FileError extends AppError {
  const FileError({required this.message, this.filePath, this.operation})
      : super._();

  @override
  final String message;
  final String? filePath;
  final String? operation;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FileErrorCopyWith<FileError> get copyWith =>
      _$FileErrorCopyWithImpl<FileError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FileError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.operation, operation) ||
                other.operation == operation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, filePath, operation);

  @override
  String toString() {
    return 'AppError.file(message: $message, filePath: $filePath, operation: $operation)';
  }
}

/// @nodoc
abstract mixin class $FileErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $FileErrorCopyWith(FileError value, $Res Function(FileError) _then) =
      _$FileErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, String? filePath, String? operation});
}

/// @nodoc
class _$FileErrorCopyWithImpl<$Res> implements $FileErrorCopyWith<$Res> {
  _$FileErrorCopyWithImpl(this._self, this._then);

  final FileError _self;
  final $Res Function(FileError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? filePath = freezed,
    Object? operation = freezed,
  }) {
    return _then(FileError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: freezed == filePath
          ? _self.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String?,
      operation: freezed == operation
          ? _self.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class PermissionError extends AppError {
  const PermissionError({required this.message, this.permission}) : super._();

  @override
  final String message;
  final String? permission;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PermissionErrorCopyWith<PermissionError> get copyWith =>
      _$PermissionErrorCopyWithImpl<PermissionError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PermissionError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.permission, permission) ||
                other.permission == permission));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, permission);

  @override
  String toString() {
    return 'AppError.permission(message: $message, permission: $permission)';
  }
}

/// @nodoc
abstract mixin class $PermissionErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $PermissionErrorCopyWith(
          PermissionError value, $Res Function(PermissionError) _then) =
      _$PermissionErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, String? permission});
}

/// @nodoc
class _$PermissionErrorCopyWithImpl<$Res>
    implements $PermissionErrorCopyWith<$Res> {
  _$PermissionErrorCopyWithImpl(this._self, this._then);

  final PermissionError _self;
  final $Res Function(PermissionError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? permission = freezed,
  }) {
    return _then(PermissionError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      permission: freezed == permission
          ? _self.permission
          : permission // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class UnknownError extends AppError {
  const UnknownError(
      {required this.message, this.originalError, this.stackTrace})
      : super._();

  @override
  final String message;
  final Object? originalError;
  final StackTrace? stackTrace;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnknownErrorCopyWith<UnknownError> get copyWith =>
      _$UnknownErrorCopyWithImpl<UnknownError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnknownError &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  @override
  String toString() {
    return 'AppError.unknown(message: $message, originalError: $originalError, stackTrace: $stackTrace)';
  }
}

/// @nodoc
abstract mixin class $UnknownErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $UnknownErrorCopyWith(
          UnknownError value, $Res Function(UnknownError) _then) =
      _$UnknownErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, Object? originalError, StackTrace? stackTrace});
}

/// @nodoc
class _$UnknownErrorCopyWithImpl<$Res> implements $UnknownErrorCopyWith<$Res> {
  _$UnknownErrorCopyWithImpl(this._self, this._then);

  final UnknownError _self;
  final $Res Function(UnknownError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(UnknownError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _self.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _self.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class TimeoutError extends AppError {
  const TimeoutError({required this.message, this.duration}) : super._();

  @override
  final String message;
  final Duration? duration;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TimeoutErrorCopyWith<TimeoutError> get copyWith =>
      _$TimeoutErrorCopyWithImpl<TimeoutError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TimeoutError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.duration, duration) ||
                other.duration == duration));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, duration);

  @override
  String toString() {
    return 'AppError.timeout(message: $message, duration: $duration)';
  }
}

/// @nodoc
abstract mixin class $TimeoutErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $TimeoutErrorCopyWith(
          TimeoutError value, $Res Function(TimeoutError) _then) =
      _$TimeoutErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, Duration? duration});
}

/// @nodoc
class _$TimeoutErrorCopyWithImpl<$Res> implements $TimeoutErrorCopyWith<$Res> {
  _$TimeoutErrorCopyWithImpl(this._self, this._then);

  final TimeoutError _self;
  final $Res Function(TimeoutError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? duration = freezed,
  }) {
    return _then(TimeoutError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ));
  }
}

/// @nodoc

class CacheError extends AppError {
  const CacheError({required this.message, this.cacheKey}) : super._();

  @override
  final String message;
  final String? cacheKey;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CacheErrorCopyWith<CacheError> get copyWith =>
      _$CacheErrorCopyWithImpl<CacheError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CacheError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.cacheKey, cacheKey) ||
                other.cacheKey == cacheKey));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, cacheKey);

  @override
  String toString() {
    return 'AppError.cache(message: $message, cacheKey: $cacheKey)';
  }
}

/// @nodoc
abstract mixin class $CacheErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory $CacheErrorCopyWith(
          CacheError value, $Res Function(CacheError) _then) =
      _$CacheErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String message, String? cacheKey});
}

/// @nodoc
class _$CacheErrorCopyWithImpl<$Res> implements $CacheErrorCopyWith<$Res> {
  _$CacheErrorCopyWithImpl(this._self, this._then);

  final CacheError _self;
  final $Res Function(CacheError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? cacheKey = freezed,
  }) {
    return _then(CacheError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      cacheKey: freezed == cacheKey
          ? _self.cacheKey
          : cacheKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
