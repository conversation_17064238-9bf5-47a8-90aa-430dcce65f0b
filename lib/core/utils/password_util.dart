// ignore_for_file: avoid_classes_with_only_static_members

import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';

/// Utility class for secure password handling
class PasswordUtil {
  /// Generate a random salt for password hashing
  static String generateSalt([int length = 32]) {
    final Random random = Random.secure();
    final List<int> saltBytes = List<int>.generate(
      length,
      (_) => random.nextInt(256),
    );
    return base64Encode(saltBytes);
  }

  /// Hash a password with a salt using SHA-256
  ///
  /// Note: For production, consider using a more secure algorithm like Argon2,
  /// bcrypt, or scrypt with a proper implementation library.
  /// This implementation uses SHA-256 which is available in the crypto package,
  /// but is not ideal for password hashing in production environments.
  static String hashPassword(String password, String salt) {
    final List<int> key = utf8.encode(password);
    final List<int> saltBytes = base64Decode(salt);

    // Combine salt and password
    final List<int> combined = <int>[...saltBytes, ...key];

    // Hash using SHA-256
    final Digest digest = sha256.convert(combined);

    // Return base64 encoded hash
    return base64Encode(digest.bytes);
  }

  /// Verify a password against a stored hash and salt
  static bool verifyPassword(String password, String hash, String salt) {
    final String computedHash = hashPassword(password, salt);
    return computedHash == hash;
  }
}
