import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/media_ui_model.dart';
import '../../../utils/format_utils.dart';

class DocumentInfoPanel extends ConsumerWidget {
  const DocumentInfoPanel({
    required this.mediaItem,
    super.key,
  });

  final MediaUiModel mediaItem;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4.0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Document title
            Text(
              mediaItem.title,
              style: Theme.of(context).textTheme.titleLarge,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8.0),

            // Document metadata
            if (mediaItem.metadata != null) ...<Widget>[
              _buildMetadataSection(context),
              const SizedBox(height: 16.0),
            ],

            // Document description
            if (mediaItem.metadata?.description != null) ...<Widget>[
              Text(
                'Description',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 4.0),
              Text(
                mediaItem.metadata!.description!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16.0),
            ],

            // Document tags
            if (mediaItem.metadata?.tags != null) ...<Widget>[
              Text(
                'Tags',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8.0),
              Wrap(
                spacing: 8.0,
                runSpacing: 8.0,
                children: (mediaItem.metadata!.tags! as List<dynamic>)
                    .map((dynamic tag) => Chip(
                          label: Text(tag as String),
                          backgroundColor: Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.1),
                          labelStyle: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ))
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataSection(BuildContext context) {
    final List<Widget> metadataWidgets = <Widget>[];

    // Add document type
    if (mediaItem.metadata?.additionalInfo?['format'] != null) {
      metadataWidgets.add(_buildMetadataItem(
        context,
        Icons.description,
        'Format: ${mediaItem.metadata!.additionalInfo!['format']}',
      ));
    }

    // Add document size
    if (mediaItem.metadata?.additionalInfo?['size'] != null) {
      metadataWidgets.add(
        _buildMetadataItem(
          context,
          Icons.data_usage,
          'Size: ${_formatFileSize(mediaItem.metadata!.additionalInfo!['size'] as int)}',
        ),
      );
    }

    // Add document creation date
    if (mediaItem.metadata?.additionalInfo?['createdAt'] != null) {
      final DateTime createdAt = DateTime.parse(
          mediaItem.metadata!.additionalInfo!['createdAt'] as String);
      metadataWidgets.add(
        _buildMetadataItem(
          context,
          Icons.calendar_today,
          'Created: ${formatDate(createdAt)}',
        ),
      );
    }

    // Add document author
    final String? author = mediaItem.metadata?.author ??
        mediaItem.metadata?.additionalInfo?['author'] as String?;
    if (author != null) {
      metadataWidgets.add(
        _buildMetadataItem(
          context,
          Icons.person,
          'Author: $author',
        ),
      );
    }

    // Add document language
    if (mediaItem.metadata?.additionalInfo?['language'] != null) {
      metadataWidgets.add(
        _buildMetadataItem(
          context,
          Icons.language,
          'Language: ${mediaItem.metadata!.additionalInfo!['language']}',
        ),
      );
    }

    // Add document license
    if (mediaItem.metadata?.additionalInfo?['license'] != null) {
      metadataWidgets.add(
        _buildMetadataItem(
          context,
          Icons.copyright,
          'License: ${mediaItem.metadata!.additionalInfo!['license']}',
        ),
      );
    }

    return Wrap(
      spacing: 16.0,
      runSpacing: 8.0,
      children: metadataWidgets,
    );
  }

  Widget _buildMetadataItem(BuildContext context, IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(
          icon,
          size: 16.0,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 4.0),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  String _formatFileSize(int sizeInBytes) {
    if (sizeInBytes < 1024) {
      return '$sizeInBytes B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Using the utility function from format_utils.dart
}
