import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../data/enums/media_type_enum.dart';
import '../../../data/enums/opentions_format_enum.dart';
import '../../../data/models/media_item.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../utils/exit_confirmation.dart';
import '../../navigation/application/navigation_provider.dart';
import '../application/article_reader_provider.dart';
import 'document_download_dialog.dart';

class ArticleReaderScreen extends HookConsumerWidget {
  const ArticleReaderScreen({
    required this.mediaItem,
    super.key,
  });

  final MediaUiModel mediaItem;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
// Helper function to convert article to HTML
    String convertArticleToHtml(String articleContent, String title) {
      debugPrint('Converting article content to HTML');

      // Split the content by paragraphs (double newlines)
      final List<String> paragraphs = articleContent.split('\n\n');

      // Build HTML content
      final StringBuffer htmlBuffer = StringBuffer();
      htmlBuffer.write('<h1>$title</h1>');

      // Process each paragraph
      for (final String paragraph in paragraphs) {
        final String trimmedParagraph = paragraph.trim();
        if (trimmedParagraph.isEmpty) {
          continue;
        }

        // Check if this paragraph might be a heading (shorter than 100 chars and ends with a colon or question mark)
        if (trimmedParagraph.length < 100 &&
            (trimmedParagraph.endsWith(':') ||
                trimmedParagraph.endsWith('?') ||
                !trimmedParagraph
                    .contains(' ') || // Single word might be a heading
                trimmedParagraph.toUpperCase() == trimmedParagraph)) {
          // ALL CAPS might be a heading
          htmlBuffer.write('<h2>$trimmedParagraph</h2>');
        } else {
          htmlBuffer.write('<p>$trimmedParagraph</p>');
        }
      }

      // Wrap in a complete HTML document
      return '''
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
          <style>
            body {
              font-family: 'Cairo', sans-serif;
              line-height: 1.6;
              padding: 20px;
              direction: rtl;
              text-align: right;
            }
            h1 {
              font-size: 1.8em;
              margin-bottom: 20px;
            }
            h2 {
              font-size: 1.4em;
              margin-top: 25px;
              margin-bottom: 15px;
            }
            p {
              margin-bottom: 15px;
            }
          </style>
        </head>
        <body>
          $htmlBuffer
        </body>
        </html>
      ''';
    }

// Helper function to get HTML URL
    String? getHtmlUrl(MediaUiModel mediaItem) {
      if (mediaItem.metadata?.html != null) {
        return null;
      }

      // Check if we have an HTML option in the options array
      if (mediaItem.options != null) {
        for (final MediaOption option in mediaItem.options!) {
          if ((option.format == OptionFormat.html ||
                  option.format.toJson().toLowerCase().contains('html')) &&
              option.url != null &&
              option.url!.isNotEmpty) {
            return option.url;
          }
        }
      }

      if (mediaItem.metadata?.additionalInfo?.containsKey('options') ?? false) {
        final List<dynamic>? options =
            mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
        if (options != null) {
          for (final dynamic option in options) {
            if (option is Map<dynamic, dynamic> &&
                option['format'] == 'html' &&
                option['url'] != null) {
              return option['url'] as String;
            }
          }
        }
      }

      return null;
    }

    // Helper function to initialize WebView
    void initWebView(WidgetRef ref, bool isDisposed) {
      // Safety check to prevent updates after dispose
      if (isDisposed) {
        return;
      }
      debugPrint('Initializing WebView for item: ${mediaItem.id}');
      debugPrint('Item title: ${mediaItem.title}');
      debugPrint('Item category: ${mediaItem.category}');

      // Log all available properties for debugging
      debugPrint('Item has articleText: ${mediaItem.articleText != null}');
      debugPrint('Item has metadata: ${mediaItem.metadata != null}');
      if (mediaItem.metadata != null) {
        debugPrint(
            'Item has metadata.html: ${mediaItem.metadata!.html != null}');
        debugPrint(
            'Item has metadata.description: ${mediaItem.metadata!.description != null}');
      }
      debugPrint(
          'Item has options: ${mediaItem.options != null && mediaItem.options!.isNotEmpty}');

      final String? htmlUrl = getHtmlUrl(mediaItem);
      debugPrint(
          'HTML URL found: ${htmlUrl != null ? "Yes - $htmlUrl" : "No"}');

      if (htmlUrl != null) {
        debugPrint('Initializing WebView with URL: $htmlUrl');
        ref
            .read(articleReaderNotifierProvider.notifier)
            .initializeWithUrl(htmlUrl);
      } else {
        String? htmlContent;
        String? articleContent;

        // First check options for article format content (highest priority)
        if (mediaItem.options != null) {
          debugPrint(
              'Checking ${mediaItem.options!.length} options for article content');
          for (final MediaOption option in mediaItem.options!) {
            debugPrint('Option format: ${option.format}');

            // Check for article format first (highest priority)
            if (option.format.toJson().toLowerCase() == 'article' &&
                option.description != null) {
              articleContent = option.description;
              debugPrint(
                  'Found article content in option: ${articleContent!.length} characters');
              // Don't break here, continue looking for HTML content as well
            }

            // Also check for HTML content
            if (option.format == OptionFormat.html && option.html != null) {
              htmlContent = option.html;
              debugPrint(
                  'Found HTML content in option: ${htmlContent!.length} characters');
              // Don't break, continue checking for article content
            }
          }
        }

        // If we found article content, use it (convert to HTML)
        if (articleContent != null) {
          debugPrint('Using article content from options');
          // Convert plain text article to HTML with proper formatting
          htmlContent = convertArticleToHtml(articleContent, mediaItem.title);
        }
        // If no article content was found, check metadata for HTML content
        else if (mediaItem.metadata?.html != null) {
          htmlContent = mediaItem.metadata!.html;
          final int previewLength =
              htmlContent!.length >= 50 ? 50 : htmlContent.length;
          debugPrint(
              'Found HTML content in metadata: ${htmlContent.substring(0, previewLength)}...');
        }

        // If still no HTML content, check if we have article text
        if (htmlContent == null && mediaItem.articleText != null) {
          debugPrint('Using articleText as content');
          htmlContent = mediaItem.articleText;
        }

        // If still no content, check description
        if (htmlContent == null && mediaItem.metadata?.description != null) {
          debugPrint('Using metadata.description as content');
          htmlContent = mediaItem.metadata!.description;
        }

        // Check for HTML content in options for any item
        if (htmlContent == null && mediaItem.options != null) {
          for (final MediaOption option in mediaItem.options!) {
            if (option.format == OptionFormat.html && option.html != null) {
              htmlContent = option.html;
              debugPrint('Found HTML content in options');
              break;
            }
          }
        }

        // If still nothing, create a fallback message
        final String content =
            htmlContent ?? 'No content available for ${mediaItem.title}';
        debugPrint('Final content length: ${content.length} characters');

        // More robust HTML detection
        final bool isHtml = content.trim().toLowerCase().startsWith('<') &&
            (content.toLowerCase().contains('</html>') ||
                content.toLowerCase().contains('</body>') ||
                content.toLowerCase().contains('</div>') ||
                content.toLowerCase().contains('</p>') ||
                content.toLowerCase().contains('<html') ||
                content.toLowerCase().contains('<body') ||
                content.toLowerCase().contains('<head'));

        // Force HTML for items in the HTML category
        final bool forceHtml = mediaItem.category == MediaCategory.htmlArticles;

        debugPrint('Content is ${isHtml || forceHtml ? 'HTML' : 'plain text'}');

        // If content is very short and not HTML, it might be an error message
        if (content.length < 50 && !isHtml && !forceHtml) {
          debugPrint(
              'Content is very short, might need to create a better fallback');
          // Create a better fallback with more information
          final String fallbackContent = '''
            <h1>${mediaItem.title}</h1>
            <p>$content</p>
            <p>Item ID: ${mediaItem.id}</p>
            <p>Category: ${mediaItem.category}</p>
          ''';

          // Check if widget is still mounted before updating state
          if (!isDisposed) {
            ref
                .read(articleReaderNotifierProvider.notifier)
                .initializeWithHtml(fallbackContent, isHtml: true);
          }
        } else {
          // Check if widget is still mounted before updating state
          if (!isDisposed) {
            ref
                .read(articleReaderNotifierProvider.notifier)
                .initializeWithHtml(content, isHtml: isHtml || forceHtml);
          }
        }
      }
    }

    // Track if the component is disposed
    final ValueNotifier<bool> isDisposed = useState(false);

    // Initialize WebView on first render
    useEffect(() {
      // Use a post-frame callback to ensure the widget is fully mounted
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!isDisposed.value) {
          initWebView(ref, isDisposed.value);
        }
      });

      // Set isDisposed to true when the widget is disposed
      return () {
        isDisposed.value = true;
      };
    }, const <Object?>[]);

    // Watch the article reader state
    final ArticleReaderState articleReaderState =
        ref.watch(articleReaderNotifierProvider);

    return ExitConfirmationWrapper(
      shouldConfirmExit: () => true,
      child: Scaffold(
        appBar: AppBar(
          // title: Text(mediaItem.title),
          // titleTextStyle: Theme.of(context)
          //     .textTheme
          //     .titleSmall
          //     ?.copyWith(color: kWhiteColor),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => ref.read(navigationProvider).safeGoBack(context),
            tooltip: 'رجوع',
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.text_decrease),
              onPressed: () => ref
                  .read(articleReaderNotifierProvider.notifier)
                  .decreaseFontSize(),
              tooltip: 'Decrease font size',
            ),
            IconButton(
              icon: const Icon(Icons.text_increase),
              onPressed: () => ref
                  .read(articleReaderNotifierProvider.notifier)
                  .increaseFontSize(),
              tooltip: 'Increase font size',
            ),
            IconButton(
              icon: Icon(
                articleReaderState.isDarkMode
                    ? Icons.light_mode
                    : Icons.dark_mode,
              ),
              onPressed: () => ref
                  .read(articleReaderNotifierProvider.notifier)
                  .toggleDarkMode(),
              tooltip: 'Toggle dark mode',
            ),
            // Download button
            Builder(
              builder: (BuildContext context) {
                // Only show download button if we have options
                final List<MediaOption> options =
                    mediaItem.options ?? <MediaOption>[];

                // Only show the button if we have options with URLs
                final bool hasDownloadableOptions = options.any(
                    (MediaOption option) =>
                        option.url != null && option.url!.isNotEmpty);

                if (hasDownloadableOptions) {
                  return IconButton(
                    icon: const Icon(Icons.download_rounded),
                    onPressed: () {
                      showDocumentDownloadDialog(
                        context,
                        ref.read(articleReaderNotifierProvider.notifier),
                        mediaOptions: options,
                      );
                    },
                    tooltip: 'تحميل المستند',
                  );
                } else {
                  return const SizedBox
                      .shrink(); // Hide if no downloadable options
                }
              },
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                final String shareText =
                    '${mediaItem.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';
                SharePlus.instance.share(ShareParams(text: shareText));
              },
              tooltip: 'Share',
            ),
          ],
        ),
        body: Stack(
          children: <Widget>[
            if (articleReaderState.webViewController != null)
              WebViewWidget(controller: articleReaderState.webViewController!),
            if (articleReaderState.isLoading)
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }
}
