// import 'package:flutter/material.dart';
// import 'package:flutter_pdfview/flutter_pdfview.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:url_launcher/url_launcher.dart';

// import '../../../data/models/media_player_state.dart';
// import '../../../utils/exit_confirmation.dart';
// import '../../media_player/application/media_player_controller.dart';
// import 'document_download_dialog.dart';
// import 'document_info_panel.dart';

// class TextMediaViewerScreen extends ConsumerWidget {
//   const TextMediaViewerScreen({
//     required this.mediaId,
//     super.key,
//   });

//   final String mediaId;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     // debugPrint('=== BUILDING TextMediaViewerScreen for mediaId: $mediaId ===');
//     // debugPrint('This confirms the TextMediaViewerScreen is being loaded');

//     try {
//       final MediaPlayerState mediaPlayerState =
//           ref.watch(mediaPlayerControllerProvider(mediaId));
//       final MediaPlayerController mediaPlayerController =
//           ref.watch(mediaPlayerControllerProvider(mediaId).notifier);

//       debugPrint('MediaPlayerState: $mediaPlayerState');
//       debugPrint('MediaItem: ${mediaPlayerState.mediaItem}');
//       debugPrint('Document URL: ${mediaPlayerState.mediaItem?.documentUrl}');

//       // Check if we have a PDF document URL
//       final bool hasPdfUrl = mediaPlayerState.mediaItem?.documentUrl != null &&
//           mediaPlayerState.mediaItem!.documentUrl!
//               .toLowerCase()
//               .endsWith('.pdf');

//       debugPrint(
//           'Has PDF URL: $hasPdfUrl, isDocumentLoaded: ${mediaPlayerState.isDocumentLoaded}, isPlaying: ${mediaPlayerState.isPlaying}');

//       // Case 1: Document not loaded yet - load it for any document type
//       if (mediaPlayerState.isDocumentLoaded == false &&
//           mediaPlayerState.documentLoadError == null) {
//         debugPrint('Document not loaded, loading now...');
//         // Use immediate execution instead of microtask to ensure it happens right away
//         mediaPlayerController.loadDocument();
//       }
//       // Case 2: PDF file that's not playing yet - ensure it auto-plays
//       else if (hasPdfUrl && !mediaPlayerState.isPlaying) {
//         debugPrint('Auto-playing PDF file');
//         // Use immediate execution instead of microtask
//         mediaPlayerController.autoPlayPdf();
//       }

//       // Check if it's a PDF file for additional controls
//       final bool isPdfFile = mediaPlayerState.mediaItem?.documentUrl != null &&
//           mediaPlayerState.mediaItem!.documentUrl!
//               .toLowerCase()
//               .endsWith('.pdf');

//       return ExitConfirmationWrapper(
//         shouldConfirmExit: () => true, // Always show confirmation
//         child: Scaffold(
//           appBar: AppBar(
//             title: Text(mediaPlayerState.mediaItem?.title ?? 'Document Viewer'),
//             leading: IconButton(
//               icon: const Icon(Icons.arrow_back),
//               onPressed: () {
//                 // Instead of just popping, navigate to the home screen if there's nothing to pop
//                 try {
//                   if (Navigator.of(context).canPop()) {
//                     context.pop();
//                   } else {
//                     // Navigate to home if we can't pop
//                     context.go('/home');
//                   }
//                 } catch (e) {
//                   // If any error occurs, navigate to home
//                   debugPrint('Error navigating back: $e');
//                   context.go('/home');
//                 }
//               },
//               tooltip: 'رجوع',
//             ),
//             actions: <Widget>[
//               // PDF-specific controls
//               if (isPdfFile) ...<Widget>[
//                 // Page navigation for PDFs
//                 IconButton(
//                   icon: const Icon(Icons.navigate_before),
//                   onPressed: mediaPlayerState.currentPage != null &&
//                           mediaPlayerState.currentPage! > 1
//                       ? () => mediaPlayerController.setCurrentPage(
//                           (mediaPlayerState.currentPage ?? 2) - 1)
//                       : null,
//                   tooltip: 'Previous Page',
//                 ),
//                 // Page indicator
//                 Center(
//                   child: Text(
//                     'Page ${mediaPlayerState.currentPage ?? 1}',
//                     style: const TextStyle(fontSize: 14),
//                   ),
//                 ),
//                 IconButton(
//                   icon: const Icon(Icons.navigate_next),
//                   onPressed: () => mediaPlayerController
//                       .setCurrentPage((mediaPlayerState.currentPage ?? 0) + 1),
//                   tooltip: 'الصفحة التالية',
//                 ),
//               ],
//               // Play downloaded file button (if available)
//               IconButton(
//                 icon: const Icon(Icons.file_open),
//                 onPressed: () => mediaPlayerController.playMedia(),
//                 tooltip: 'افتح الملف المحمل',
//               ),

//               // Download options button
//               IconButton(
//                 icon: mediaPlayerState.isDownloading ?? true
//                     ? const SizedBox(
//                         width: 20,
//                         height: 20,
//                         child: CircularProgressIndicator(strokeWidth: 2),
//                       )
//                     : const Icon(Icons.download),
//                 tooltip: 'تحميل المستند',
//                 onPressed: mediaPlayerState.isDownloading ?? true
//                     ? null
//                     : () {
//                         // Check if we have options
//                         if (mediaPlayerState.mediaItem?.options != null &&
//                             mediaPlayerState.mediaItem!.options!.isNotEmpty) {
//                           // Show the document download dialog
//                           showDocumentDownloadDialog(
//                             context,
//                             mediaPlayerController,
//                             mediaOptions: mediaPlayerState.mediaItem!.options!,
//                           );
//                         } else {
//                           // If no options, just download with default settings
//                           mediaPlayerController.downloadMedia();
//                         }
//                       },
//               ),
//               // Share button
//               IconButton(
//                 icon: const Icon(Icons.share),
//                 onPressed: () => mediaPlayerController.shareContent(),
//                 tooltip: 'Share Document',
//               ),
//             ],
//           ),
//           body: mediaPlayerState.isLoading
//               ? const Center(child: CircularProgressIndicator())
//               : mediaPlayerState.errorMessage != null
//                   ? Center(
//                       child: Text('Error: ${mediaPlayerState.errorMessage}'))
//                   : Column(
//                       children: <Widget>[
//                         Expanded(
//                           flex: 3,
//                           child: _buildDocumentViewer(
//                               mediaPlayerState, mediaPlayerController, context),
//                         ),
//                         Expanded(
//                           child: DocumentInfoPanel(
//                               mediaItem: mediaPlayerState.mediaItem!),
//                         ),
//                         if (mediaPlayerState.isDownloading ?? true)
//                           Padding(
//                             padding: const EdgeInsets.all(16.0),
//                             child: Column(
//                               children: <Widget>[
//                                 Text(
//                                   'Downloading: ${(mediaPlayerState.downloadProgress! * 100).toStringAsFixed(1)}%',
//                                 ),
//                                 const SizedBox(height: 8),
//                                 LinearProgressIndicator(
//                                   value: mediaPlayerState.downloadProgress,
//                                 ),
//                               ],
//                             ),
//                           ),
//                       ],
//                     ),
//         ),
//       );
//     } catch (e) {
//       // If there's an error, show a fallback UI
//       return ExitConfirmationWrapper(
//         shouldConfirmExit: () => true, // Always show confirmation
//         child: Scaffold(
//           appBar: AppBar(
//             title: const Text('Document Viewer Error'),
//             leading: IconButton(
//               icon: const Icon(Icons.arrow_back),
//               onPressed: () {
//                 // Instead of just popping, navigate to the home screen if there's nothing to pop
//                 try {
//                   if (Navigator.of(context).canPop()) {
//                     context.pop();
//                   } else {
//                     // Navigate to home if we can't pop
//                     context.go('/home');
//                   }
//                 } catch (e) {
//                   // If any error occurs, navigate to home
//                   debugPrint('Error navigating back: $e');
//                   context.go('/home');
//                 }
//               },
//               tooltip: 'رجوع',
//             ),
//           ),
//           body: Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: <Widget>[
//                 const Icon(Icons.error_outline, size: 48, color: Colors.red),
//                 const SizedBox(height: 16),
//                 Text(
//                   'An error occurred: $e',
//                   textAlign: TextAlign.center,
//                   style: Theme.of(context).textTheme.titleMedium,
//                 ),
//                 const SizedBox(height: 24),
//                 ElevatedButton(
//                   onPressed: () => context.pop(),
//                   child: const Text('Go Back'),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       );
//     }
//   }

//   Widget _buildDocumentViewer(
//     MediaPlayerState mediaPlayerState,
//     MediaPlayerController mediaPlayerController,
//     BuildContext context,
//   ) {
//     final bool hasPdfUrl = mediaPlayerState.mediaItem?.documentUrl != null &&
//         mediaPlayerState.mediaItem!.documentUrl!.toLowerCase().endsWith('.pdf');
//     try {
//       // Check if mediaItem is null first
//       if (mediaPlayerState.mediaItem == null) {
//         debugPrint('Media item is null');
//         return const Center(
//           child: Text('Document not found'),
//         );
//       }

//       // Check if document is loading or not loaded yet
//       if (mediaPlayerState.isLoading ||
//           mediaPlayerState.isDocumentLoaded == false) {
//         debugPrint(
//             'Document is loading or not loaded yet: isLoading=${mediaPlayerState.isLoading}, isDocumentLoaded=${mediaPlayerState.isDocumentLoaded}');
//         return Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: <Widget>[
//               const CircularProgressIndicator(),
//               const SizedBox(height: 16),
//               Text(
//                 mediaPlayerState.isLoading
//                     ? 'Loading document...'
//                     : 'Preparing document viewer...',
//                 style: Theme.of(context).textTheme.titleMedium,
//               ),
//               const SizedBox(height: 8),
//               if (hasPdfUrl)
//                 Text(
//                   'PDF document: ${mediaPlayerState.mediaItem!.documentUrl!.split('/').last}',
//                   style: Theme.of(context).textTheme.bodySmall,
//                 ),
//             ],
//           ),
//         );
//       }

//       // Check if there's a document load error
//       if (mediaPlayerState.documentLoadError != null) {
//         return Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: <Widget>[
//               const Icon(Icons.error_outline, size: 48, color: Colors.red),
//               const SizedBox(height: 16),
//               Text(
//                 'Failed to load document: ${mediaPlayerState.documentLoadError}',
//                 textAlign: TextAlign.center,
//                 style: Theme.of(context).textTheme.titleMedium,
//               ),
//             ],
//           ),
//         );
//       }

//       // If it's a PDF file, show the PDF viewer
//       if (hasPdfUrl &&
//           (mediaPlayerState.isDocumentLoaded ?? true) &&
//           mediaPlayerState.downloadedFilePath != null) {
//         final String pdfFilePath = mediaPlayerState.downloadedFilePath!;

//         debugPrint(
//             'Showing PDF viewer for ${mediaPlayerState.mediaItem!.documentUrl} with local path: $pdfFilePath');
//         return PDFView(
//           filePath: pdfFilePath,
//           swipeHorizontal: true,
//           defaultPage: mediaPlayerState.currentPage ?? 0,
//           fitPolicy: FitPolicy.BOTH,
//           onRender: (int? pages) {
//             debugPrint('PDF rendered with $pages pages');
//             mediaPlayerController.setPdfPageCount(pages);
//           },
//           onError: (dynamic error) {
//             debugPrint('Error rendering PDF: $error');
//             mediaPlayerController.setDocumentLoadError(error.toString());
//           },
//           onPageError: (int? page, dynamic error) {
//             debugPrint('Error rendering page $page: $error');
//           },
//           onViewCreated: (PDFViewController pdfViewController) {
//             debugPrint('PDF view created');
//             mediaPlayerController.setPdfViewController(pdfViewController);
//           },
//           onPageChanged: (int? page, int? total) {
//             debugPrint('Page changed to $page of $total');
//             if (page != null) {
//               mediaPlayerController.setCurrentPage(page);
//             }
//           },
//           onLinkHandler: (String? uri) {
//             debugPrint('Link tapped: $uri');
//             if (uri != null) {
//               launchUrl(Uri.parse(uri));
//             }
//           },
//         );
//       }

//       // For HTML content or other document types, show a message
//       return Center(
//         child: Text(
//           'Document type not supported for direct viewing',
//           style: Theme.of(context).textTheme.titleMedium,
//         ),
//       );
//     } catch (e) {
//       debugPrint('Error in _buildDocumentViewer: $e');
//       return Center(
//         child: Text('Error: $e'),
//       );
//     }
//   }
// }
