// lib/features/text_media_viewer/presentation/pdf_info_screen.dart

import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:ionicons/ionicons.dart'; // For icons

import '../../../common/universal_image.dart';
import '../../../common/widgets/favorite_button.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/enums/opentions_format_enum.dart';
import '../../../data/models/media_item.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../gen/assets.gen.dart'; // For fallback image
import '../../../routing/app_router.dart';
import '../../media_player/application/services/service_providers.dart'; // For interaction service
import '../../user_data/application/media_providers.dart'; //

class PdfInfoScreen extends HookConsumerWidget {
  const PdfInfoScreen({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<MediaUiModel?> asyncMediaItem =
        ref.watch(mediaItemProvider(mediaId)); //
    final ThemeData theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: <Widget>[
          if (asyncMediaItem.hasValue &&
              asyncMediaItem.value != null) ...<Widget>[
            FavoriteButton(
              itemId: mediaId,
              itemType: asyncMediaItem.value!.type,
            ),
            IconButton(
              icon: const Icon(Ionicons.share_outline),
              onPressed: () async {
                // Get the media player controller for this media item
                if (asyncMediaItem.value != null) {
                  await ref
                      .read(interactionServiceProvider)
                      .shareContent(asyncMediaItem.value!);
                }
              },
            ),
            IconButton(
              icon: const Icon(Ionicons.settings_outline),
              onPressed: () {
                // Handle settings navigation here
                context.push(SGRoute.settings.route);
              },
            ),
          ],
        ],
      ),
      body: SafeArea(
        child: asyncMediaItem.when(
          data: (MediaUiModel? mediaItem) {
            if (mediaItem == null) {
              return const Center(child: Text('المستند غير موجود.'));
            }

            if (mediaItem.type == MediaType.unknown) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    const Icon(Icons.error_outline,
                        size: 64, color: Colors.orange),
                    const SizedBox(height: 16),
                    const Text(
                      'نوع المستند غير معروف',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد عناصر وسائط متاحة لـ ${mediaItem.type.name}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () => context.pop(),
                      child: const Text('رجوع'),
                    ),
                  ],
                ),
              );
            }

            final MediaOption? pdfOption = mediaItem.options?.firstWhere(
              (MediaOption opt) =>
                  opt.format == OptionFormat.pdf &&
                  opt.url != null &&
                  opt.url!.isNotEmpty,
              orElse: () =>
                  mediaItem.options?.firstWhere(
                    (MediaOption opt) =>
                        opt.url != null &&
                        opt.url!.isNotEmpty &&
                        opt.url!.toLowerCase().endsWith('.pdf'),
                    orElse: () => const MediaOption(
                        optionId: '', format: OptionFormat.unknown),
                  ) ??
                  const MediaOption(optionId: '', format: OptionFormat.unknown),
            );

            String? displayPdfUrl = pdfOption?.url;
            MediaOption? displayPdfOption =
                (pdfOption != null && pdfOption.optionId.isNotEmpty)
                    ? pdfOption
                    : null;

            if (displayPdfUrl == null &&
                mediaItem.documentUrl != null &&
                mediaItem.documentUrl!.toLowerCase().endsWith('.pdf')) {
              displayPdfUrl = mediaItem.documentUrl;
              displayPdfOption ??= MediaOption(
                optionId: 'synthetic_pdf_option',
                format: OptionFormat.pdf,
                url: displayPdfUrl,
                author: mediaItem.metadata?.author,
                section:
                    mediaItem.metadata?.additionalInfo?['section'] as String?,
                totalPages: mediaItem.metadata?.additionalInfo?['totalPages'],
                releaseDate: mediaItem.metadata?.publishDate?.toIso8601String(),
              );
            }

            if (displayPdfUrl == null) {
              return const Center(
                  child: Text('رابط PDF غير متوفر لهذا المستند.'));
            }

            // Extracted content building logic
            final Widget content =
                _buildPdfInfoDetails(context, mediaItem, displayPdfOption, ref);

            return Stack(
              children: <Widget>[
                content,
                // Fading effect overlay
                Positioned(
                  left: 0,
                  right: 0,
                  bottom:
                      kTextTabBarHeight, // Height of the button area + some overlap
                  height: 60, // Height of the fade
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: <Color>[
                          theme.scaffoldBackgroundColor.withValues(alpha: 0.0),
                          theme.scaffoldBackgroundColor.withValues(alpha: 0.8),
                          theme.scaffoldBackgroundColor,
                        ],
                        stops: const <double>[0.0, 0.7, 1.0],
                      ),
                    ),
                  ),
                ),
                // Floating "Read book" button
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                    color: theme
                        .scaffoldBackgroundColor, // To ensure button is not transparent
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        minimumSize: const Size(double.infinity, 50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        textStyle: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () {
                        context.pushNamed(
                          SGRoute.textMediaViewer.name,
                          pathParameters: <String, String>{'id': mediaId},
                        );
                      },
                      child: const Text('اقرأ الكتاب'),
                    ),
                  ),
                ),
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (Object error, StackTrace stack) =>
              Center(child: Text('خطأ في تحميل البيانات: $error')),
        ),
      ),
    );
  }

  Widget _buildPdfInfoDetails(
    BuildContext context,
    MediaUiModel mediaItem,
    MediaOption? pdfOption,
    WidgetRef ref,
  ) {
    final ThemeData theme = Theme.of(context);
    final TextTheme textTheme = theme.textTheme;
    final ColorScheme colorScheme = theme.colorScheme;

    final String title = mediaItem.title;
    final String author =
        pdfOption?.author ?? mediaItem.metadata?.author ?? 'غير معروف';
    final String genre = pdfOption?.section ?? 'غير مصنف';
    final String pages = pdfOption?.totalPages?.toString() ?? 'N/A';

    String releasedDate = 'N/A';
    if (pdfOption?.releaseDate != null) {
      try {
        final DateTime date = DateTime.parse(pdfOption!.releaseDate!);
        releasedDate = DateFormat('dd MMMM yyyy', 'ar').format(date);
      } catch (e) {
        releasedDate = pdfOption?.releaseDate ?? 'غير معروف';
      }
    } else if (mediaItem.metadata?.publishDate != null) {
      releasedDate = DateFormat('dd MMMM yyyy', 'ar')
          .format(mediaItem.metadata!.publishDate!);
    }

    final String description = mediaItem.description ?? 'لا يوجد وصف متاح.';

    return SingleChildScrollView(
      // Added padding to prevent content from being fully hidden by the floating button and fade
      padding: const EdgeInsets.only(
          left: 24.0, right: 24.0, top: 24.0, bottom: 120.0),
      child: Column(
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
              boxShadow: <BoxShadow>[
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: UniversalImage(
                path: mediaItem.thumbnailUrl ?? Assets.img.drImage.path, //
                height: 280,
                width: 200,
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            textAlign: TextAlign.center,
            style: textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            author,
            textAlign: TextAlign.center,
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: <Widget>[
              _MetadataItem(label: 'النوع', value: genre),
              _MetadataItem(label: 'الصفحات', value: pages),
              _MetadataItem(label: 'صدر في', value: releasedDate),
            ],
          ),
          const SizedBox(height: 24),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              'الوصف',
              textAlign: TextAlign.justify,
              textDirection: ui.TextDirection.rtl,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
              height: 1.6,
            ),
            textAlign: TextAlign.justify,
            textDirection: ui.TextDirection.rtl,
          ),
        ],
      ),
    );
  }
}

class _MetadataItem extends StatelessWidget {
  const _MetadataItem({
    required this.label,
    required this.value,
  });

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final TextTheme textTheme = theme.textTheme;
    final ColorScheme colorScheme = theme.colorScheme;

    return Column(
      children: <Widget>[
        Text(
          label,
          style: textTheme.labelMedium?.copyWith(
            color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}
