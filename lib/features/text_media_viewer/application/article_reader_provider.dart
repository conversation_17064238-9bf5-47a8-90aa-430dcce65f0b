import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:webview_flutter/webview_flutter.dart';

part 'article_reader_provider.freezed.dart';
part 'article_reader_provider.g.dart';

@freezed
abstract class ArticleReaderState with _$ArticleReaderState {
  const factory ArticleReaderState({
    @Default(true) bool isLoading,
    @Default(false) bool isDownloading,
    WebViewController? webViewController,
    @Default(16.0) double fontSize,
    @Default(false) bool isDarkMode,
  }) = _ArticleReaderState;
}

@riverpod
class ArticleReaderNotifier extends _$ArticleReaderNotifier {
  @override
  ArticleReaderState build() {
    return const ArticleReaderState();
  }

  Future<void> downloadMedia({String? specificUrl, String? fileType}) async {
    if (specificUrl == null || specificUrl.isEmpty) {
      return;
    }

    try {
      state = state.copyWith(isDownloading: true);

      try {
        if (state.webViewController != null) {
          await state.webViewController!.runJavaScript(
            '''
            var a = document.createElement('a');
            a.href = '$specificUrl';
            a.download = '${fileType ?? 'document'}';
            a.click();
            ''',
          );
        }
      } catch (e) {
        debugPrint('Error downloading document: $e');
      } finally {
        try {
          state = state.copyWith(isDownloading: false);
        } catch (e) {
          debugPrint('Error resetting download state: $e');
        }
      }
    } catch (e) {
      debugPrint('Error in downloadMedia: $e');
    }
  }

  void initializeWithUrl(String url) {
    try {
      final WebViewController controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              try {
                setLoading(true);
              } catch (e) {
                debugPrint('Error in onPageStarted: $e');
              }
            },
            onPageFinished: (String url) {
              try {
                setLoading(false);
                applyStyles();
              } catch (e) {
                debugPrint('Error in onPageFinished: $e');
              }
            },
            onWebResourceError: (WebResourceError error) {
              try {
                debugPrint('WebView error: ${error.description}');
                setLoading(false);
              } catch (e) {
                debugPrint('Error handling WebResourceError: $e');
              }
            },
          ),
        )
        ..loadRequest(Uri.parse(url));

      state = state.copyWith(webViewController: controller);
    } catch (e) {
      debugPrint('Error initializing WebView with URL: $e');
    }
  }

  void initializeWithHtml(String htmlContent, {bool isHtml = false}) {
    debugPrint(
        'Initializing WebView with ${isHtml ? 'HTML' : 'plain text'} content');
    debugPrint('Content length: ${htmlContent.length} characters');

    final int previewLength =
        htmlContent.length > 100 ? 100 : htmlContent.length;
    debugPrint(
        'Content preview: ${htmlContent.substring(0, previewLength)}...');

    String content;
    if (isHtml) {
      if (!htmlContent.trim().toLowerCase().contains('<html') ||
          !htmlContent.trim().toLowerCase().contains('<body')) {
        content = '''
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
          <style>
            body {
              font-family: 'Cairo', sans-serif;
              line-height: 1.6;
              padding: 20px;
              direction: rtl;
              text-align: justify;
            }
          </style>
        </head>
        <body>
          ${_sanitizeHtml(htmlContent)}
        </body>
        </html>
        ''';
      } else {
        content = htmlContent;
      }
    } else {
      content = _wrapHtmlContent(htmlContent);
    }

    debugPrint('Final HTML structure prepared for WebView');

    final WebViewController controller = WebViewController();

    controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    controller.setNavigationDelegate(
      NavigationDelegate(
        onPageStarted: (String url) {
          try {
            debugPrint('WebView page started loading');
            setLoading(true);
          } catch (e) {
            debugPrint('Error in onPageStarted: $e');
          }
        },
        onPageFinished: (String url) {
          try {
            debugPrint('WebView page finished loading');
            setLoading(false);
            applyStyles();
            controller.runJavaScript('''
              if (document.body.innerText.trim() === '') {
                document.body.innerHTML = '<h1>Content could not be loaded</h1><p>Please try again later.</p>';
              }
            ''');
          } catch (e) {
            debugPrint('Error in onPageFinished: $e');
          }
        },
        onWebResourceError: (WebResourceError error) {
          try {
            debugPrint('WebView error: ${error.description}');
            if (error.errorType ==
                    WebResourceErrorType.javaScriptExceptionOccurred ||
                error.errorType == WebResourceErrorType.failedSslHandshake ||
                error.errorType == WebResourceErrorType.hostLookup) {
              setLoading(false);
            }
          } catch (e) {
            debugPrint('Error handling WebResourceError: $e');
          }
        },
      ),
    );

    try {
      debugPrint('Loading HTML content into WebView...');
      controller.loadHtmlString(content, baseUrl: 'https://dralfarih.app/');
      debugPrint('HTML content loaded into WebView');
    } catch (e) {
      debugPrint('Error loading HTML content: $e');
      final String fallbackContent = _createFallbackHtml(htmlContent);
      controller.loadHtmlString(fallbackContent,
          baseUrl: 'https://dralfarih.app/');
      debugPrint('Fallback content loaded instead');
    }

    try {
      state = state.copyWith(webViewController: controller);
      debugPrint('WebViewController set in state');
    } catch (e) {
      debugPrint('Error updating state with WebViewController: $e');
    }
  }

  String _createFallbackHtml(String originalContent) {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
        <style>
          body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            padding: 20px;
            direction: rtl;
            text-align: justify;
          }
        </style>
      </head>
      <body>
        <div>${_sanitizeHtml(originalContent)}</div>
      </body>
      </html>
    ''';
  }

  String _sanitizeHtml(String html) {
    if (!html.trim().startsWith('<')) {
      return '<p>$html</p>';
    }
    final String sanitized = html
        .replaceAll(
            RegExp(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>',
                caseSensitive: false),
            '')
        .replaceAll(
            RegExp(r'<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>',
                caseSensitive: false),
            '');
    return sanitized;
  }

  String _wrapHtmlContent(String content) {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
        <style>
          body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            padding: 20px;
            direction: rtl;
            text-align: justify;
            font-size: ${state.fontSize}px;
            color: ${state.isDarkMode ? '#ffffff' : '#333333'};
            background-color: ${state.isDarkMode ? '#121212' : '#ffffff'};
          }
          h1, h2, h3 {
            color: ${state.isDarkMode ? '#e1e1e1' : '#222222'};
          }
          a {
            color: ${state.isDarkMode ? '#90caf9' : '#1976d2'};
          }
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
          }
        </style>
      </head>
      <body>
        $content
      </body>
      </html>
    ''';
  }

  void applyStyles() {
    try {
      if (state.webViewController == null) {
        return;
      }
      final String jsCode = '''
        document.body.style.fontSize = "${state.fontSize}px";
        document.body.style.color = "${state.isDarkMode ? '#ffffff' : '#333333'}";
        document.body.style.backgroundColor = "${state.isDarkMode ? '#121212' : '#ffffff'}";
        document.body.style.textAlign = "justify";
        document.body.style.direction = "rtl";
        var headings = document.querySelectorAll('h1, h2, h3');
        for (var i = 0; i < headings.length; i++) {
          headings[i].style.color = "${state.isDarkMode ? '#e1e1e1' : '#222222'}";
        }
        var links = document.querySelectorAll('a');
        for (var i = 0; i < links.length; i++) {
          links[i].style.color = "${state.isDarkMode ? '#90caf9' : '#1976d2'}";
        }
      ''';
      state.webViewController!.runJavaScript(jsCode);
    } catch (e) {
      debugPrint('Error applying styles: $e');
    }
  }

  void increaseFontSize() {
    try {
      state = state.copyWith(fontSize: state.fontSize + 2);
      applyStyles();
    } catch (e) {
      debugPrint('Error increasing font size: $e');
    }
  }

  void decreaseFontSize() {
    try {
      final double newSize = (state.fontSize - 2).clamp(12.0, 32.0);
      state = state.copyWith(fontSize: newSize);
      applyStyles();
    } catch (e) {
      debugPrint('Error decreasing font size: $e');
    }
  }

  void toggleDarkMode() {
    try {
      state = state.copyWith(isDarkMode: !state.isDarkMode);
      applyStyles();
    } catch (e) {
      debugPrint('Error toggling dark mode: $e');
    }
  }

  void setLoading(bool loading) {
    try {
      state = state.copyWith(isLoading: loading);
    } catch (e) {
      debugPrint('Error setting loading state: $e');
    }
  }
}
