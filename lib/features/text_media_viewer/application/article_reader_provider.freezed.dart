// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'article_reader_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ArticleReaderState implements DiagnosticableTreeMixin {
  bool get isLoading;
  bool get isDownloading;
  WebViewController? get webViewController;
  double get fontSize;
  bool get isDarkMode;

  /// Create a copy of ArticleReaderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ArticleReaderStateCopyWith<ArticleReaderState> get copyWith =>
      _$ArticleReaderStateCopyWithImpl<ArticleReaderState>(
          this as ArticleReaderState, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'ArticleReaderState'))
      ..add(DiagnosticsProperty('isLoading', isLoading))
      ..add(DiagnosticsProperty('isDownloading', isDownloading))
      ..add(DiagnosticsProperty('webViewController', webViewController))
      ..add(DiagnosticsProperty('fontSize', fontSize))
      ..add(DiagnosticsProperty('isDarkMode', isDarkMode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ArticleReaderState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.webViewController, webViewController) ||
                other.webViewController == webViewController) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize) &&
            (identical(other.isDarkMode, isDarkMode) ||
                other.isDarkMode == isDarkMode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, isDownloading,
      webViewController, fontSize, isDarkMode);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ArticleReaderState(isLoading: $isLoading, isDownloading: $isDownloading, webViewController: $webViewController, fontSize: $fontSize, isDarkMode: $isDarkMode)';
  }
}

/// @nodoc
abstract mixin class $ArticleReaderStateCopyWith<$Res> {
  factory $ArticleReaderStateCopyWith(
          ArticleReaderState value, $Res Function(ArticleReaderState) _then) =
      _$ArticleReaderStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      bool isDownloading,
      WebViewController? webViewController,
      double fontSize,
      bool isDarkMode});
}

/// @nodoc
class _$ArticleReaderStateCopyWithImpl<$Res>
    implements $ArticleReaderStateCopyWith<$Res> {
  _$ArticleReaderStateCopyWithImpl(this._self, this._then);

  final ArticleReaderState _self;
  final $Res Function(ArticleReaderState) _then;

  /// Create a copy of ArticleReaderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isDownloading = null,
    Object? webViewController = freezed,
    Object? fontSize = null,
    Object? isDarkMode = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isDownloading: null == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool,
      webViewController: freezed == webViewController
          ? _self.webViewController
          : webViewController // ignore: cast_nullable_to_non_nullable
              as WebViewController?,
      fontSize: null == fontSize
          ? _self.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as double,
      isDarkMode: null == isDarkMode
          ? _self.isDarkMode
          : isDarkMode // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _ArticleReaderState
    with DiagnosticableTreeMixin
    implements ArticleReaderState {
  const _ArticleReaderState(
      {this.isLoading = true,
      this.isDownloading = false,
      this.webViewController,
      this.fontSize = 16.0,
      this.isDarkMode = false});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isDownloading;
  @override
  final WebViewController? webViewController;
  @override
  @JsonKey()
  final double fontSize;
  @override
  @JsonKey()
  final bool isDarkMode;

  /// Create a copy of ArticleReaderState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ArticleReaderStateCopyWith<_ArticleReaderState> get copyWith =>
      __$ArticleReaderStateCopyWithImpl<_ArticleReaderState>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'ArticleReaderState'))
      ..add(DiagnosticsProperty('isLoading', isLoading))
      ..add(DiagnosticsProperty('isDownloading', isDownloading))
      ..add(DiagnosticsProperty('webViewController', webViewController))
      ..add(DiagnosticsProperty('fontSize', fontSize))
      ..add(DiagnosticsProperty('isDarkMode', isDarkMode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ArticleReaderState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.webViewController, webViewController) ||
                other.webViewController == webViewController) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize) &&
            (identical(other.isDarkMode, isDarkMode) ||
                other.isDarkMode == isDarkMode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, isDownloading,
      webViewController, fontSize, isDarkMode);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ArticleReaderState(isLoading: $isLoading, isDownloading: $isDownloading, webViewController: $webViewController, fontSize: $fontSize, isDarkMode: $isDarkMode)';
  }
}

/// @nodoc
abstract mixin class _$ArticleReaderStateCopyWith<$Res>
    implements $ArticleReaderStateCopyWith<$Res> {
  factory _$ArticleReaderStateCopyWith(
          _ArticleReaderState value, $Res Function(_ArticleReaderState) _then) =
      __$ArticleReaderStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isDownloading,
      WebViewController? webViewController,
      double fontSize,
      bool isDarkMode});
}

/// @nodoc
class __$ArticleReaderStateCopyWithImpl<$Res>
    implements _$ArticleReaderStateCopyWith<$Res> {
  __$ArticleReaderStateCopyWithImpl(this._self, this._then);

  final _ArticleReaderState _self;
  final $Res Function(_ArticleReaderState) _then;

  /// Create a copy of ArticleReaderState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? isDownloading = null,
    Object? webViewController = freezed,
    Object? fontSize = null,
    Object? isDarkMode = null,
  }) {
    return _then(_ArticleReaderState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isDownloading: null == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool,
      webViewController: freezed == webViewController
          ? _self.webViewController
          : webViewController // ignore: cast_nullable_to_non_nullable
              as WebViewController?,
      fontSize: null == fontSize
          ? _self.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as double,
      isDarkMode: null == isDarkMode
          ? _self.isDarkMode
          : isDarkMode // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
