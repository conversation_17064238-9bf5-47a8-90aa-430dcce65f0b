// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pdf_reader_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PdfReaderState {
  bool get isLoading;
  String? get errorMessage;
  double get zoomLevel;
  bool get isDownloading;
  double get downloadProgress;

  /// Create a copy of PdfReaderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PdfReaderStateCopyWith<PdfReaderState> get copyWith =>
      _$PdfReaderStateCopyWithImpl<PdfReaderState>(
          this as PdfReaderState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PdfReaderState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.zoomLevel, zoomLevel) ||
                other.zoomLevel == zoomLevel) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.downloadProgress, downloadProgress) ||
                other.downloadProgress == downloadProgress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, errorMessage,
      zoomLevel, isDownloading, downloadProgress);

  @override
  String toString() {
    return 'PdfReaderState(isLoading: $isLoading, errorMessage: $errorMessage, zoomLevel: $zoomLevel, isDownloading: $isDownloading, downloadProgress: $downloadProgress)';
  }
}

/// @nodoc
abstract mixin class $PdfReaderStateCopyWith<$Res> {
  factory $PdfReaderStateCopyWith(
          PdfReaderState value, $Res Function(PdfReaderState) _then) =
      _$PdfReaderStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      String? errorMessage,
      double zoomLevel,
      bool isDownloading,
      double downloadProgress});
}

/// @nodoc
class _$PdfReaderStateCopyWithImpl<$Res>
    implements $PdfReaderStateCopyWith<$Res> {
  _$PdfReaderStateCopyWithImpl(this._self, this._then);

  final PdfReaderState _self;
  final $Res Function(PdfReaderState) _then;

  /// Create a copy of PdfReaderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? errorMessage = freezed,
    Object? zoomLevel = null,
    Object? isDownloading = null,
    Object? downloadProgress = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      zoomLevel: null == zoomLevel
          ? _self.zoomLevel
          : zoomLevel // ignore: cast_nullable_to_non_nullable
              as double,
      isDownloading: null == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool,
      downloadProgress: null == downloadProgress
          ? _self.downloadProgress
          : downloadProgress // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _PdfReaderState implements PdfReaderState {
  const _PdfReaderState(
      {this.isLoading = true,
      this.errorMessage,
      this.zoomLevel = 1.0,
      this.isDownloading = false,
      this.downloadProgress = 0.0});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final double zoomLevel;
  @override
  @JsonKey()
  final bool isDownloading;
  @override
  @JsonKey()
  final double downloadProgress;

  /// Create a copy of PdfReaderState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PdfReaderStateCopyWith<_PdfReaderState> get copyWith =>
      __$PdfReaderStateCopyWithImpl<_PdfReaderState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PdfReaderState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.zoomLevel, zoomLevel) ||
                other.zoomLevel == zoomLevel) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.downloadProgress, downloadProgress) ||
                other.downloadProgress == downloadProgress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, errorMessage,
      zoomLevel, isDownloading, downloadProgress);

  @override
  String toString() {
    return 'PdfReaderState(isLoading: $isLoading, errorMessage: $errorMessage, zoomLevel: $zoomLevel, isDownloading: $isDownloading, downloadProgress: $downloadProgress)';
  }
}

/// @nodoc
abstract mixin class _$PdfReaderStateCopyWith<$Res>
    implements $PdfReaderStateCopyWith<$Res> {
  factory _$PdfReaderStateCopyWith(
          _PdfReaderState value, $Res Function(_PdfReaderState) _then) =
      __$PdfReaderStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      String? errorMessage,
      double zoomLevel,
      bool isDownloading,
      double downloadProgress});
}

/// @nodoc
class __$PdfReaderStateCopyWithImpl<$Res>
    implements _$PdfReaderStateCopyWith<$Res> {
  __$PdfReaderStateCopyWithImpl(this._self, this._then);

  final _PdfReaderState _self;
  final $Res Function(_PdfReaderState) _then;

  /// Create a copy of PdfReaderState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? errorMessage = freezed,
    Object? zoomLevel = null,
    Object? isDownloading = null,
    Object? downloadProgress = null,
  }) {
    return _then(_PdfReaderState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      zoomLevel: null == zoomLevel
          ? _self.zoomLevel
          : zoomLevel // ignore: cast_nullable_to_non_nullable
              as double,
      isDownloading: null == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool,
      downloadProgress: null == downloadProgress
          ? _self.downloadProgress
          : downloadProgress // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

// dart format on
