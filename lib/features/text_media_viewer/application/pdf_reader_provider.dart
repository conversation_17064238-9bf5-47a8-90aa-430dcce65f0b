import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:share_plus/share_plus.dart';

part 'pdf_reader_provider.freezed.dart';
part 'pdf_reader_provider.g.dart';

/// State class for the PDF reader
@freezed
abstract class PdfReaderState with _$PdfReaderState {
  const factory PdfReaderState({
    @Default(true) bool isLoading,
    String? errorMessage,
    @Default(1.0) double zoomLevel,
    @Default(false) bool isDownloading,
    @Default(0.0) double downloadProgress,
  }) = _PdfReaderState;
}

/// Provider for the PDF reader state
@riverpod
class PdfReaderNotifier extends _$PdfReaderNotifier {
  @override
  PdfReaderState build() {
    return const PdfReaderState();
  }

  /// Sets the loading state
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// Sets an error message
  void setError(String message) {
    state = state.copyWith(
      errorMessage: message,
      isLoading: false,
    );
  }

  /// Increases the zoom level
  void increaseZoom() {
    state = state.copyWith(zoomLevel: state.zoomLevel + 0.25);
  }

  /// Decreases the zoom level
  void decreaseZoom() {
    final double newZoom = (state.zoomLevel - 0.25).clamp(0.5, 3.0);
    state = state.copyWith(zoomLevel: newZoom);
  }

  /// Downloads a file
  Future<void> downloadFile(String fileUrl, String title,
      {String extension = '.pdf'}) async {
    state = state.copyWith(
      isDownloading: true,
      downloadProgress: 0.0,
    );

    if (fileUrl.isEmpty) {
      state = state.copyWith(
        isDownloading: false,
        errorMessage: 'No file URL available',
      );
      return;
    }

    try {
      // Get the temporary directory
      final Directory tempDir = await getTemporaryDirectory();

      // Extract filename from URL if possible
      String fileName;

      try {
        // Parse the URL
        final Uri uri = Uri.parse(fileUrl);
        // Get the path component
        final String path = uri.path;
        // Extract the filename from the path
        final String urlFileName = path.split('/').last;

        // Check if the URL has a valid filename
        if (urlFileName.isNotEmpty && urlFileName.contains('.')) {
          fileName = urlFileName;
        } else {
          // Fallback to title with extension
          fileName = '${title.replaceAll(' ', '_')}$extension';
        }
      } catch (e) {
        // If URL parsing fails, use the title with extension
        fileName = '${title.replaceAll(' ', '_')}$extension';
      }

      final String filePath = '${tempDir.path}/$fileName';

      // Download the file
      final http.Client client = http.Client();
      final http.Request request = http.Request('GET', Uri.parse(fileUrl));
      final http.StreamedResponse response = await client.send(request);

      final File file = File(filePath);
      final IOSink sink = file.openWrite();

      final int totalBytes = response.contentLength ?? 0;
      int receivedBytes = 0;

      await response.stream.forEach((List<int> chunk) {
        sink.add(chunk);
        receivedBytes += chunk.length;

        if (totalBytes > 0) {
          state = state.copyWith(
            downloadProgress: receivedBytes / totalBytes,
          );
        }
      });

      await sink.close();

      // Share the file
      await SharePlus.instance.share(ShareParams(
        files: <XFile>[
          XFile(filePath),
        ],
        text:
            'Sharing $fileName --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله',
      ));

      state = state.copyWith(isDownloading: false);
    } catch (e) {
      state = state.copyWith(
        isDownloading: false,
        errorMessage: 'Failed to download file: $e',
      );
    }
  }

  /// Downloads media with a specific URL and file type
  /// This method is used by the document download dialog
  Future<void> downloadMedia({String? specificUrl, String? fileType}) async {
    if (specificUrl == null || specificUrl.isEmpty) {
      state = state.copyWith(
        errorMessage: 'No URL available for download',
      );
      return;
    }

    // Create a file extension based on the file type
    String extension = '.pdf';
    if (fileType == 'docx') {
      extension = '.docx';
    } else if (fileType == 'txt') {
      extension = '.txt';
    } else if (fileType == 'html') {
      extension = '.html';
    } else if (fileType == 'audio') {
      // Try to determine audio extension from URL
      if (specificUrl.toLowerCase().endsWith('.mp3')) {
        extension = '.mp3';
      } else if (specificUrl.toLowerCase().endsWith('.wav')) {
        extension = '.wav';
      } else if (specificUrl.toLowerCase().endsWith('.ogg')) {
        extension = '.ogg';
      } else if (specificUrl.toLowerCase().endsWith('.aac')) {
        extension = '.aac';
      } else {
        extension = '.mp3'; // Default audio extension
      }
    } else if (fileType == 'video') {
      // Try to determine video extension from URL
      if (specificUrl.toLowerCase().endsWith('.mp4')) {
        extension = '.mp4';
      } else if (specificUrl.toLowerCase().endsWith('.mov')) {
        extension = '.mov';
      } else if (specificUrl.toLowerCase().endsWith('.avi')) {
        extension = '.avi';
      } else if (specificUrl.toLowerCase().endsWith('.mkv')) {
        extension = '.mkv';
      } else {
        extension = '.mp4'; // Default video extension
      }
    }

    // Generate a title based on the file type
    String title = 'Document';
    if (fileType != null) {
      title = fileType.toUpperCase();
    }

    // Try to extract a meaningful filename from the URL
    String extractedName = '';
    try {
      final Uri uri = Uri.parse(specificUrl);
      final String path = uri.path;
      final String urlFileName = path.split('/').last;

      // If we have a valid filename in the URL, use its name part (without extension)
      if (urlFileName.isNotEmpty && urlFileName.contains('.')) {
        // Get the name part without extension
        final int lastDotIndex = urlFileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
          extractedName = urlFileName.substring(0, lastDotIndex);
          // Use this as the title if it's meaningful (not just a random string)
          if (extractedName.length > 3) {
            title = extractedName;
          }
        }
      }
    } catch (e) {
      // If URL parsing fails, just use the default title
    }

    // Download the file with the correct extension
    await downloadFile(specificUrl, title, extension: extension);
  }
}
