import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/enums/action_type_enum.dart';
import '../application/user_data_providers.dart';

class HistoryTracker extends ConsumerStatefulWidget {
  const HistoryTracker({
    required this.itemId,
    required this.actionType,
    required this.child,
    this.trackOnInit = true,
    super.key,
  });

  final String itemId;
  final ActionType actionType;
  final Widget child;
  final bool trackOnInit;

  @override
  ConsumerState<HistoryTracker> createState() => _HistoryTrackerState();
}

class _HistoryTrackerState extends ConsumerState<HistoryTracker> {
  bool _tracked = false;

  @override
  void initState() {
    super.initState();
    if (widget.trackOnInit) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _trackAction();
      });
    }
  }

  Future<void> _trackAction() async {
    if (_tracked || !mounted) {
      return;
    }

    await ref.read(historyNotifierProvider.notifier).addToHistory(
          itemId: widget.itemId,
          actionType: widget.actionType,
        );

    if (!mounted) {
      return; // Check if still mounted after async operation
    }
    _tracked = true;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.trackOnInit && !_tracked) {
      _trackAction();
    }

    return widget.child;
  }
}
