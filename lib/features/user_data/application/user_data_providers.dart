import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/enums/action_type_enum.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/user_data_models.dart';
import '../../../data/services/user_data_service.dart';
import '../../../di/components/service_locator.dart';
import '../../auth/application/user_provider.dart';

part 'user_data_providers.g.dart';

final UserDataService _userDataService = getIt<UserDataService>();

// Favorites providers
@Riverpod(keepAlive: true)
class FavoritesNotifier extends _$FavoritesNotifier {
  @override
  Future<List<FavoriteItem>> build() async {
    final String userId = ref.watch(currentUserIdProvider);
    return _userDataService.getFavorites(userId: userId);
  }

  Future<bool> toggleFavorite({
    required String itemId,
    required MediaType type,
  }) async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return false;
    }

    state = const AsyncValue<List<FavoriteItem>>.loading();

    try {
      final String userId = ref.read(currentUserIdProvider);
      final bool isFavorited = await _userDataService.toggleFavorite(
        userId: userId,
        itemId: itemId,
        type: type,
      );

      // Check if ref is still mounted after the async operation
      if (!ref.mounted) {
        return isFavorited;
      }

      // Refresh favorites list
      final List<FavoriteItem> favorites =
          await _userDataService.getFavorites(userId: userId);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = AsyncValue<List<FavoriteItem>>.data(favorites);
      }

      return isFavorited;
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<FavoriteItem>>.error(e, StackTrace.current);
      }
      return false;
    }
  }

  Future<void> clearFavorites() async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return;
    }

    state = const AsyncValue<List<FavoriteItem>>.loading();

    try {
      final String userId = ref.read(currentUserIdProvider);
      await _userDataService.clearUserData(
        userId: userId,
        clearProgress: false,
        clearHistory: false,
      );

      // Check if ref is still mounted before updating state
      if (ref.mounted) {
        state = const AsyncValue<List<FavoriteItem>>.data(<FavoriteItem>[]);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<FavoriteItem>>.error(e, StackTrace.current);
      }
    }
  }
}

// Check if an item is favorited
@Riverpod(keepAlive: true)
Future<bool> isFavorite(Ref ref, String itemId) async {
  final String userId = ref.watch(currentUserIdProvider);
  return _userDataService.isFavorite(
    userId: userId,
    itemId: itemId,
  );
}

// Progress providers
@Riverpod(keepAlive: true)
class ProgressNotifier extends _$ProgressNotifier {
  @override
  Future<List<ProgressItem>> build() async {
    final String userId = ref.watch(currentUserIdProvider);
    return _userDataService.getAllProgress(userId: userId);
  }

  Future<void> saveProgress({
    required String itemId,
    required double positionSeconds,
  }) async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      final String userId = ref.read(currentUserIdProvider);
      await _userDataService.saveProgress(
        userId: userId,
        itemId: itemId,
        positionSeconds: positionSeconds,
      );

      // Check if ref is still mounted after the async operation
      if (!ref.mounted) {
        return;
      }

      // Refresh progress list
      final List<ProgressItem> progress =
          await _userDataService.getAllProgress(userId: userId);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = AsyncValue<List<ProgressItem>>.data(progress);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<ProgressItem>>.error(e, StackTrace.current);
      }
    }
  }

  Future<void> clearProgress() async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return;
    }

    state = const AsyncValue<List<ProgressItem>>.loading();

    try {
      final String userId = ref.read(currentUserIdProvider);
      await _userDataService.clearUserData(
        userId: userId,
        clearFavorites: false,
        clearHistory: false,
      );

      // Check if ref is still mounted before updating state
      if (ref.mounted) {
        state = const AsyncValue<List<ProgressItem>>.data(<ProgressItem>[]);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<ProgressItem>>.error(e, StackTrace.current);
      }
    }
  }
}

// Get progress for a specific item
@Riverpod(keepAlive: true)
Future<double?> itemProgress(Ref ref, String itemId) async {
  final String userId = ref.watch(currentUserIdProvider);
  return _userDataService.getProgress(
    userId: userId,
    itemId: itemId,
  );
}

// History providers
@Riverpod(keepAlive: true)
class HistoryNotifier extends _$HistoryNotifier {
  @override
  Future<List<HistoryItem>> build() async {
    final String userId = ref.watch(currentUserIdProvider);
    return _userDataService.getHistory(userId: userId);
  }

  Future<void> addToHistory({
    required String itemId,
    required ActionType actionType,
  }) async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      final String userId = ref.read(currentUserIdProvider);
      await _userDataService.addToHistory(
        userId: userId,
        itemId: itemId,
        actionType: actionType,
      );

      // Check if ref is still mounted after the async operation
      if (!ref.mounted) {
        return;
      }

      // Refresh history list
      final List<HistoryItem> history =
          await _userDataService.getHistory(userId: userId);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = AsyncValue<List<HistoryItem>>.data(history);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<HistoryItem>>.error(e, StackTrace.current);
      }
    }
  }

  Future<void> clearHistory() async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return;
    }

    state = const AsyncValue<List<HistoryItem>>.loading();

    try {
      final String userId = ref.read(currentUserIdProvider);
      await _userDataService.clearUserData(
        userId: userId,
        clearFavorites: false,
        clearProgress: false,
      );

      // Check if ref is still mounted before updating state
      if (ref.mounted) {
        state = const AsyncValue<List<HistoryItem>>.data(<HistoryItem>[]);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<HistoryItem>>.error(e, StackTrace.current);
      }
    }
  }
}

// All user data operations
@Riverpod(keepAlive: true)
class UserDataNotifier extends _$UserDataNotifier {
  @override
  void build() {
    // This notifier doesn't need to maintain state
    // It just coordinates actions between other providers
  }

  Future<void> clearAllUserData() async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      final String userId = ref.read(currentUserIdProvider);
      await _userDataService.clearUserData(
        userId: userId,
      );

      // Check if ref is still mounted before updating providers
      if (!ref.mounted) {
        return;
      }

      // Refresh all providers
      ref.invalidate(favoritesNotifierProvider);
      ref.invalidate(progressNotifierProvider);
      ref.invalidate(historyNotifierProvider);
    } catch (e) {
      // Handle error
      debugPrint('Error clearing user data: $e');
    }
  }

  Future<void> transferAnonymousData(String userId) async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      await _userDataService.transferAnonymousData(
        userId: userId,
      );

      // Check if ref is still mounted before updating providers
      if (!ref.mounted) {
        return;
      }

      // Refresh all providers
      ref.invalidate(favoritesNotifierProvider);
      ref.invalidate(progressNotifierProvider);
      ref.invalidate(historyNotifierProvider);
    } catch (e) {
      // Handle error
      debugPrint('Error transferring anonymous data: $e');
    }
  }
}
