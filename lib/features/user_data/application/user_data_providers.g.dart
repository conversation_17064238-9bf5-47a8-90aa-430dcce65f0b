// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(FavoritesNotifier)
const favoritesNotifierProvider = FavoritesNotifierProvider._();

final class FavoritesNotifierProvider
    extends $AsyncNotifierProvider<FavoritesNotifier, List<FavoriteItem>> {
  const FavoritesNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'favoritesNotifierProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$favoritesNotifierHash();

  @$internal
  @override
  FavoritesNotifier create() => FavoritesNotifier();

  @$internal
  @override
  $AsyncNotifierProviderElement<FavoritesNotifier, List<FavoriteItem>>
      $createElement($ProviderPointer pointer) =>
          $AsyncNotifierProviderElement(pointer);
}

String _$favoritesNotifierHash() => r'e771ad292e325903dfd88c5e090abe096db61501';

abstract class _$FavoritesNotifier extends $AsyncNotifier<List<FavoriteItem>> {
  FutureOr<List<FavoriteItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<FavoriteItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<FavoriteItem>>>,
        AsyncValue<List<FavoriteItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(isFavorite)
const isFavoriteProvider = IsFavoriteFamily._();

final class IsFavoriteProvider
    extends $FunctionalProvider<AsyncValue<bool>, FutureOr<bool>>
    with $FutureModifier<bool>, $FutureProvider<bool> {
  const IsFavoriteProvider._(
      {required IsFavoriteFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'isFavoriteProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$isFavoriteHash();

  @override
  String toString() {
    return r'isFavoriteProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<bool> create(Ref ref) {
    final argument = this.argument as String;
    return isFavorite(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is IsFavoriteProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$isFavoriteHash() => r'29f9fd1e8bfb84e19224f1d0db8980dba34d3d13';

final class IsFavoriteFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<bool>, String> {
  const IsFavoriteFamily._()
      : super(
          retry: null,
          name: r'isFavoriteProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: false,
        );

  IsFavoriteProvider call(
    String itemId,
  ) =>
      IsFavoriteProvider._(argument: itemId, from: this);

  @override
  String toString() => r'isFavoriteProvider';
}

@ProviderFor(ProgressNotifier)
const progressNotifierProvider = ProgressNotifierProvider._();

final class ProgressNotifierProvider
    extends $AsyncNotifierProvider<ProgressNotifier, List<ProgressItem>> {
  const ProgressNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'progressNotifierProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$progressNotifierHash();

  @$internal
  @override
  ProgressNotifier create() => ProgressNotifier();

  @$internal
  @override
  $AsyncNotifierProviderElement<ProgressNotifier, List<ProgressItem>>
      $createElement($ProviderPointer pointer) =>
          $AsyncNotifierProviderElement(pointer);
}

String _$progressNotifierHash() => r'bbbed05ff1405027698d492befa3bd51ac0dbf42';

abstract class _$ProgressNotifier extends $AsyncNotifier<List<ProgressItem>> {
  FutureOr<List<ProgressItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<ProgressItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<ProgressItem>>>,
        AsyncValue<List<ProgressItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(itemProgress)
const itemProgressProvider = ItemProgressFamily._();

final class ItemProgressProvider
    extends $FunctionalProvider<AsyncValue<double?>, FutureOr<double?>>
    with $FutureModifier<double?>, $FutureProvider<double?> {
  const ItemProgressProvider._(
      {required ItemProgressFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'itemProgressProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$itemProgressHash();

  @override
  String toString() {
    return r'itemProgressProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<double?> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<double?> create(Ref ref) {
    final argument = this.argument as String;
    return itemProgress(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ItemProgressProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$itemProgressHash() => r'3012657c1ddde73b9ef8cd7fe644f6a600ea3758';

final class ItemProgressFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<double?>, String> {
  const ItemProgressFamily._()
      : super(
          retry: null,
          name: r'itemProgressProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: false,
        );

  ItemProgressProvider call(
    String itemId,
  ) =>
      ItemProgressProvider._(argument: itemId, from: this);

  @override
  String toString() => r'itemProgressProvider';
}

@ProviderFor(HistoryNotifier)
const historyNotifierProvider = HistoryNotifierProvider._();

final class HistoryNotifierProvider
    extends $AsyncNotifierProvider<HistoryNotifier, List<HistoryItem>> {
  const HistoryNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'historyNotifierProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$historyNotifierHash();

  @$internal
  @override
  HistoryNotifier create() => HistoryNotifier();

  @$internal
  @override
  $AsyncNotifierProviderElement<HistoryNotifier, List<HistoryItem>>
      $createElement($ProviderPointer pointer) =>
          $AsyncNotifierProviderElement(pointer);
}

String _$historyNotifierHash() => r'beed1c6cf83ade29408c76042c296429387ec994';

abstract class _$HistoryNotifier extends $AsyncNotifier<List<HistoryItem>> {
  FutureOr<List<HistoryItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<HistoryItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<HistoryItem>>>,
        AsyncValue<List<HistoryItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(UserDataNotifier)
const userDataNotifierProvider = UserDataNotifierProvider._();

final class UserDataNotifierProvider
    extends $NotifierProvider<UserDataNotifier, void> {
  const UserDataNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'userDataNotifierProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$userDataNotifierHash();

  @$internal
  @override
  UserDataNotifier create() => UserDataNotifier();

  @$internal
  @override
  $NotifierProviderElement<UserDataNotifier, void> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(void value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<void>(value),
    );
  }
}

String _$userDataNotifierHash() => r'c3841cf6f026b3b3b89dd485f751de4985ff0e9e';

abstract class _$UserDataNotifier extends $Notifier<void> {
  void build();
  @$mustCallSuper
  @override
  void runBuild() {
    build();
    final ref = this.ref as $Ref<void>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<void>, void, Object?, Object?>;
    element.handleValue(ref, null);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
