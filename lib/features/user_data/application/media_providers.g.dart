// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Provider for all media items
@ProviderFor(mediaList)
const mediaListProvider = MediaListProvider._();

/// Provider for all media items
final class MediaListProvider extends $FunctionalProvider<
        AsyncValue<List<MediaUiModel>>, FutureOr<List<MediaUiModel>>>
    with
        $FutureModifier<List<MediaUiModel>>,
        $FutureProvider<List<MediaUiModel>> {
  /// Provider for all media items
  const MediaListProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaListProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaListHash();

  @$internal
  @override
  $FutureProviderElement<List<MediaUiModel>> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<List<MediaUiModel>> create(Ref ref) {
    return mediaList(ref);
  }
}

String _$mediaListHash() => r'd0c8ea871624f85c3f6f951713c8b49a92299de1';

/// Provider to get a specific media item by ID
@ProviderFor(mediaItem)
const mediaItemProvider = MediaItemFamily._();

/// Provider to get a specific media item by ID
final class MediaItemProvider extends $FunctionalProvider<
        AsyncValue<MediaUiModel?>, FutureOr<MediaUiModel?>>
    with $FutureModifier<MediaUiModel?>, $FutureProvider<MediaUiModel?> {
  /// Provider to get a specific media item by ID
  const MediaItemProvider._(
      {required MediaItemFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'mediaItemProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaItemHash();

  @override
  String toString() {
    return r'mediaItemProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<MediaUiModel?> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<MediaUiModel?> create(Ref ref) {
    final argument = this.argument as String;
    return mediaItem(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is MediaItemProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$mediaItemHash() => r'880b56ff31012ef5b1e0c70ceb3e26138c47ed1f';

/// Provider to get a specific media item by ID
final class MediaItemFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<MediaUiModel?>, String> {
  const MediaItemFamily._()
      : super(
          retry: null,
          name: r'mediaItemProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Provider to get a specific media item by ID
  MediaItemProvider call(
    String itemId,
  ) =>
      MediaItemProvider._(argument: itemId, from: this);

  @override
  String toString() => r'mediaItemProvider';
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
