import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_provider.freezed.dart';
part 'search_provider.g.dart';

/// State class for the search screen
@freezed
abstract class SearchState with _$SearchState {
  const factory SearchState({
    @Default('') String searchQuery,
    String? selectedType,
  }) = _SearchState;
}

/// Provider for the search state
@riverpod
class SearchNotifier extends _$SearchNotifier {
  @override
  SearchState build() {
    return const SearchState();
  }

  /// Updates the search query
  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  /// Sets the selected media type filter
  void setSelectedType(String? type) {
    state = state.copyWith(selectedType: type);
  }

  /// Resets the search state
  void reset() {
    state = const SearchState();
  }
}
