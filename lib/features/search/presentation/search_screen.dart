import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../common/hooks/use_debounce.dart';
import '../../../common/svg_icon.dart';
import '../../../common/universal_image.dart';
import '../../../constants/colors.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_items/media_navigation_helper.dart';
import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../gen/assets.gen.dart';
import '../../../utils/format_utils.dart';
import '../application/search_provider.dart';

class SearchScreen extends HookConsumerWidget {
  const SearchScreen({super.key});

  // Media types list (constant)
  static const List<String> _mediaTypes = <String>[
    'الكل',
    'صوت',
    'فيديو',
    'تغريدة',
    'كتاب',
    'مقالة'
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use hooks for controller
    final TextEditingController searchController = useTextEditingController();

    // Track the current search text for debouncing
    final ValueNotifier<String> searchText = useState('');

    // Get current search state
    final SearchState searchState = ref.watch(searchNotifierProvider);

    // Create a debounced callback for updating the global search provider
    final Function updateGlobalSearch = useDebouncedCallback(
      (String query) {
        ref.read(mediaSearchProvider.notifier).value = query;
      },
      const Duration(milliseconds: 300),
    );

    // Initialize the controller with the current search query
    useEffect(() {
      if (searchController.text != searchState.searchQuery) {
        searchController.text = searchState.searchQuery;
        searchText.value = searchState.searchQuery;
      }
      return null;
    }, <Object?>[searchState.searchQuery]);

    // Set up the controller listener
    useEffect(() {
      void listener() {
        final String currentText = searchController.text;
        final SearchState currentSearchState = ref.read(searchNotifierProvider);

        if (currentText != currentSearchState.searchQuery) {
          // Update the local search state immediately for UI feedback
          ref
              .read(searchNotifierProvider.notifier)
              .updateSearchQuery(currentText);

          // Update the local state for debouncing
          searchText.value = currentText;

          // Trigger the debounced global search update
          updateGlobalSearch(currentText);
        }
      }

      searchController.addListener(listener);

      // Cleanup on dispose
      return () {
        searchController.removeListener(listener);

        // Use a microtask to avoid potential circular dependencies during disposal
        Future<void>.microtask(() {
          // Clear the search query when leaving the search screen
          ref.read(mediaSearchProvider.notifier).reset();

          // No need to invalidate mediaListProvider and mediaUiListProvider
          // This avoids unnecessary refetching of potentially large data sets
          // The filteredMediaProvider will automatically return all items when search query is empty

          debugPrint('SearchScreen: Reset search query on dispose');
        });
      };
    }, const <Object?>[]);

    return PopScope(
        onPopInvoked: (bool didPop) {
          if (didPop) {
            // Reset search query when navigating back
            // First reset the local search state
            ref.read(searchNotifierProvider.notifier).reset();

            // Then reset the global search provider in a microtask
            // to avoid potential circular dependencies
            Future<void>.microtask(() {
              ref.read(mediaSearchProvider.notifier).reset();
            });

            debugPrint('SearchScreen: Reset search query on back navigation');
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: CupertinoSearchTextField(
              controller: searchController,
              placeholder: 'البحث ...',
              placeholderStyle: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(height: 1, color: kGrayColor),
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(height: 1.5, color: klightBlackColor),
              decoration: BoxDecoration(
                color: kPriColor,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: kGrayColor),
              ),
            ),
          ),
          body: Column(
            children: <Widget>[
              // Search results
              Expanded(
                child: _buildSearchResults(context, searchState, ref),
              ),
            ],
          ),
        ));
  }

  Widget _buildSearchResults(
      BuildContext context, SearchState searchState, WidgetRef ref) {
    final List<MediaUiModel> mediaItems = ref.watch(filteredMediaProvider);

    if (searchState.searchQuery.isEmpty) {
      return const Center(
        child: Text('أدخل كلمة للبحث عن المحتوى'),
      );
    }

    // Determine which media types have results
    final Set<String> availableTypes = <String>{'الكل'};
    final Map<MediaType, String> englishToArabicMap = <MediaType, String>{
      MediaType.audio: 'صوت',
      MediaType.video: 'فيديو',
      MediaType.tweet: 'تغريدة',
      MediaType.text: 'كتاب',
      MediaType.html: 'مقالة',
    };

    // Add available types based on search results
    for (final MediaUiModel item in mediaItems) {
      final String arabicType = englishToArabicMap[item.type] ?? item.type.name;
      availableTypes.add(arabicType);
    }

    // Filter by selected type if needed
    final List<MediaUiModel> filteredItems =
        searchState.selectedType == null || searchState.selectedType == 'الكل'
            ? mediaItems
            : mediaItems.where((MediaUiModel item) {
                // Map Arabic type names to English for filtering
                String englishType;
                switch (searchState.selectedType) {
                  case 'صوت':
                    englishType = 'audio';
                    break;
                  case 'فيديو':
                    englishType = 'video';
                    break;
                  case 'تغريدة':
                    englishType = 'tweet';
                    break;
                  case 'كتاب':
                    englishType = 'text';
                    break;
                  case 'مقالة':
                    englishType = 'html';
                    break;
                  default:
                    englishType = searchState.selectedType!.toLowerCase();
                }
                return item.type == MediaType.fromString(englishType);
              }).toList();

    // Build the filter chips for available types
    final Widget filterChips = SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: _mediaTypes
            .where((String type) => availableTypes.contains(type))
            .map((String type) {
          final bool isSelected = searchState.selectedType == type ||
              (searchState.selectedType == null && type == 'الكل');
          return Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: FilterChip(
              label: Text(type),
              selected: isSelected,
              onSelected: (bool selected) {
                ref
                    .read(searchNotifierProvider.notifier)
                    .setSelectedType(selected ? type : null);
              },
            ),
          );
        }).toList(),
      ),
    );

    if (filteredItems.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          filterChips,
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(Icons.search_off, size: 64),
                  const SizedBox(height: 16),
                  Text('لا توجد نتائج لـ "${searchState.searchQuery}"'),
                ],
              ),
            ),
          ),
        ],
      );
    }

    // Group items by type for better organization
    final Map<MediaType, List<MediaUiModel>> groupedItems =
        <MediaType, List<MediaUiModel>>{};
    for (final MediaUiModel item in filteredItems) {
      final MediaType type = item.type;
      if (!groupedItems.containsKey(type)) {
        groupedItems[type] = <MediaUiModel>[];
      }
      groupedItems[type]!.add(item);
    }

    // Build the list with section headers
    return Column(
      children: <Widget>[
        // Show filter chips at the top
        filterChips,

        // Show search results
        Expanded(
          child: ListView.builder(
            itemCount:
                groupedItems.keys.length * 2, // Double for headers and sections
            itemBuilder: (BuildContext context, int index) {
              // Even indices are headers, odd indices are lists
              if (index.isEven) {
                final int headerIndex = index ~/ 2;
                final MediaType type = groupedItems.keys.elementAt(headerIndex);
                return Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                  child: Text(
                    'نتائج ${_getArabicTypeName(type.toString().split('.').last)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                );
              } else {
                final int sectionIndex = index ~/ 2;
                final MediaType type =
                    groupedItems.keys.elementAt(sectionIndex);
                final List<MediaUiModel> sectionItems = groupedItems[type]!;

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: sectionItems.length,
                  itemBuilder: (BuildContext context, int itemIndex) {
                    final MediaUiModel item = sectionItems[itemIndex];
                    return _buildMediaItem(context, item, ref);
                  },
                );
              }
            },
          ),
        ),
      ],
    );
  }

  // Helper method to convert English type names to Arabic
  String _getArabicTypeName(String englishType) {
    switch (englishType.toLowerCase()) {
      case 'audio':
        return 'صوت';
      case 'video':
        return 'فيديو';
      case 'tweet':
        return 'تغريدة';
      case 'text':
        return 'كتاب';
      case 'html':
        return 'مقالة';
      default:
        return englishType;
    }
  }

  Widget _buildMediaItem(
      BuildContext context, MediaUiModel item, WidgetRef ref) {
    // Use the same card style as the home page
    final MediaType mediaType = item.type;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          // Navigate to the media player
          const MediaNavigationHelper mediaHelper = MediaNavigationHelper();
          mediaHelper.navigateToMediaPlayer(context, item, ref: ref);
        },
        child: _buildMediaItemContent(context, item, mediaType),
      ),
    );
  }

  Widget _buildMediaItemContent(
      BuildContext context, MediaUiModel item, MediaType mediaType) {
    switch (mediaType) {
      case MediaType.audio:
        return _buildAudioItem(context, item);
      case MediaType.video:
        return _buildVideoItem(context, item);
      case MediaType.text:
      case MediaType.html:
      case MediaType.pdf:
      case MediaType.document:
        return _buildDocumentItem(context, item);
      case MediaType.tweet:
        return _buildTweetItem(context, item);

      case MediaType.unknown:
        return _buildDefaultItem(context, item);
    }
  }

  Widget _buildAudioItem(BuildContext context, MediaUiModel item) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: <Widget>[
          // Audio icon
          SvgIcon(
            image: Assets.svg.headphone,
            height: 40,
            width: 40,
            color: kPrimaryLight,
          ),
          const Gap(14),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  item.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (item.description != null)
                  Text(
                    item.description!,
                    style: Theme.of(context).textTheme.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Row(
                  children: <Widget>[
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      item.durationSeconds != null
                          ? formatDurationFromSeconds(item.durationSeconds!)
                          : _getArabicTypeName(item.type.name),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoItem(BuildContext context, MediaUiModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // Video thumbnail with play button
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              // Thumbnail
              if (item.thumbnailUrl != null)
                UniversalImage(
                  path: item.thumbnailUrl!,
                  width: double.infinity,
                  height: double.infinity,
                )
              else
                Container(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  child: Icon(
                    Icons.videocam,
                    size: 50,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),

              // Play button overlay
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ],
          ),
        ),

        // Video details
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                item.title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              if (item.description != null)
                Text(
                  item.description!,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              const SizedBox(height: 4),
              Row(
                children: <Widget>[
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    item.durationSeconds != null
                        ? formatDurationFromSeconds(item.durationSeconds!)
                        : _getArabicTypeName(item.type.name),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentItem(BuildContext context, MediaUiModel item) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Document icon/thumbnail
          Container(
            width: 60,
            height: 80,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(4),
            ),
            child: item.thumbnailUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: UniversalImage(
                      path: item.thumbnailUrl!,
                    ),
                  )
                : Icon(
                    item.type == MediaType.pdf
                        ? Icons.picture_as_pdf
                        : Icons.insert_drive_file,
                    size: 30,
                    color: Theme.of(context).colorScheme.primary,
                  ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  item.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (item.description != null)
                  Text(
                    item.description!,
                    style: Theme.of(context).textTheme.bodySmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Text(
                  '${_getArabicTypeName(item.type.name)} • ${item.category}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTweetItem(BuildContext context, MediaUiModel item) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Tweet icon
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.lightBlue.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.format_quote,
              color: Colors.lightBlue,
              size: 30,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  item.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (item.tweetContent != null)
                  Text(
                    item.tweetContent!,
                    style: Theme.of(context).textTheme.bodySmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                if (item.tweetAuthor != null)
                  Text(
                    '@${item.tweetAuthor}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.lightBlue,
                        ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultItem(BuildContext context, MediaUiModel item) {
    return ListTile(
      leading: Icon(
        Icons.insert_drive_file,
        size: 40,
        color: Theme.of(context).colorScheme.primary,
      ),
      title: Text(item.title),
      subtitle:
          Text('${_getArabicTypeName(item.type.name)} • ${item.category}'),
    );
  }

  // Using the utility function from format_utils.dart
}
