import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_ui_model.dart';
import 'widgets/tweet_card.dart';

class TweetDetailsScreen extends ConsumerWidget {
  const TweetDetailsScreen({
    required this.tweetId,
    super.key,
  });

  final String tweetId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(mediaUiListProvider).when(
          data: (List<MediaUiModel> items) {
            debugPrint('Looking for tweet with ID: $tweetId');

            // Find the tweet by ID
            MediaUiModel? tweet;
            try {
              tweet = items.firstWhere(
                (MediaUiModel item) => item.id == tweetId,
              );
              debugPrint('Found tweet: ${tweet.title}');
            } catch (e) {
              debugPrint('Error finding tweet: $e');
              // If not found, show error
              return Scaffold(
                appBar: AppBar(title: const Text('تغريدة')),
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const Text('لم يتم العثور على التغريدة',
                          style: TextStyle(fontSize: 18)),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => context.pop(),
                        child: const Text('العودة'),
                      ),
                    ],
                  ),
                ),
              );
            }

            // The tweet will never be null due to the firstWhere with orElse throwing an exception
            // But we'll keep error handling in the UI for robustness

            return Scaffold(
              appBar: AppBar(
                title: const Text('تغريدة'),
                actions: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      // Share functionality will be handled by the TweetCard
                    },
                  ),
                ],
              ),
              body: SingleChildScrollView(
                child: TweetCard(
                  tweet: tweet,
                  isDetailView: true,
                  showFullContent: true,
                ),
              ),
            );
          },
          loading: () => Scaffold(
            appBar: AppBar(title: const Text('تغريدة')),
            body: const Center(child: CircularProgressIndicator()),
          ),
          error: (Object error, StackTrace? stackTrace) => Scaffold(
            appBar: AppBar(title: const Text('تغريدة')),
            body: Center(child: Text('خطأ: $error')),
          ),
        );
  }
}
