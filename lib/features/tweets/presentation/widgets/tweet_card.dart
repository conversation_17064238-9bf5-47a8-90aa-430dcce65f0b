import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../common/universal_image.dart';
import '../../../../data/enums/media_type_enum.dart';
import '../../../../data/models/media_ui_model.dart';
import '../../../media_player/application/services/service_providers.dart';

/// A reusable tweet card widget that can be used in both the home screen and tweet details screen.
class TweetCard extends ConsumerStatefulWidget {
  const TweetCard({
    required this.tweet,
    this.onTap,
    this.isDetailView = false,
    this.showFullContent = false,
    super.key,
  });

  /// The tweet data to display
  final MediaUiModel tweet;

  /// Callback when the tweet is tapped
  final VoidCallback? onTap;

  /// Whether this is being shown in the detail view
  final bool isDetailView;

  /// Whether to show the full content or truncate it
  final bool showFullContent;

  @override
  ConsumerState<TweetCard> createState() => _TweetCardState();
}

class _TweetCardState extends ConsumerState<TweetCard> {
  late bool isLiked;
  late bool isRetweeted;
  late int likeCount;
  late int retweetCount;
  late int commentCount;

  @override
  void initState() {
    super.initState();
    // Initialize with data from metadata or defaults
    likeCount = widget.tweet.metadata?.likeCount ?? 0;
    retweetCount = widget.tweet.metadata?.retweetCount ?? 0;
    commentCount = widget.tweet.metadata?.commentCount ?? 0;
    isLiked = false;
    isRetweeted = false;
  }

  void _toggleLike() {
    setState(() {
      isLiked = !isLiked;
      likeCount = isLiked ? likeCount + 1 : likeCount - 1;
    });

    final String message =
        isLiked ? 'تم الإعجاب بالتغريدة' : 'تم إلغاء الإعجاب';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _toggleRetweet() {
    setState(() {
      isRetweeted = !isRetweeted;
      retweetCount = isRetweeted ? retweetCount + 1 : retweetCount - 1;
    });

    final String message =
        isRetweeted ? 'تم إعادة التغريد' : 'تم إلغاء إعادة التغريد';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _addComment() {
    setState(() {
      commentCount = commentCount + 1;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إضافة تعليق وهمي'),
        duration: Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _shareTweet() async {
    final String shareText =
        '${widget.tweet.tweetContent ?? widget.tweet.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';

    try {
      await ref.read(interactionServiceProvider).shareContent(widget.tweet);
    } catch (e) {
      SharePlus.instance.share(ShareParams(text: shareText));
    }
  }

  @override
  Widget build(BuildContext context) {
    final String formattedDate = widget.tweet.tweetDate != null
        ? DateFormat('h:mm a · MMM d, yyyy').format(widget.tweet.tweetDate!)
        : '';

    final String tweetContent = widget.tweet.tweetContent ??
        widget.tweet.metadata?.description ??
        widget.tweet.title;

    // Determine if we should truncate the content
    final String displayContent = widget.showFullContent || widget.isDetailView
        ? tweetContent
        : tweetContent.length > 100
            ? '${tweetContent.substring(0, 100)}...'
            : tweetContent;

    return GestureDetector(
      onTap: widget.isDetailView ? null : widget.onTap,
      child: Container(
        margin: EdgeInsets.all(widget.isDetailView ? 0 : 12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(widget.isDetailView ? 0 : 12),
          boxShadow: widget.isDetailView
              ? null
              : <BoxShadow>[
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Tweet header
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Author avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: widget.tweet.thumbnailUrl != null
                        ? NetworkImage(widget.tweet.thumbnailUrl!)
                        : null,
                    child: widget.tweet.thumbnailUrl == null
                        ? const Icon(Icons.person)
                        : null,
                  ),
                  const SizedBox(width: 12),
                  // Author info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            Text(
                              widget.tweet.tweetAuthor ?? 'د. الفريح',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              color: Colors.blue[400],
                              size: 16,
                            ),
                          ],
                        ),
                        Text(
                          '@${(widget.tweet.tweetAuthor ?? 'dralfarih').replaceAll(' ', '_').toLowerCase()}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // More options
                  if (widget.isDetailView)
                    IconButton(
                      icon: const Icon(Icons.more_horiz),
                      onPressed: () {
                        // Show options menu
                        showModalBottomSheet(
                          context: context,
                          builder: (BuildContext context) {
                            return SafeArea(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  ListTile(
                                    leading: const Icon(Icons.share),
                                    title: const Text('شارك التغريدة'),
                                    onTap: () {
                                      Navigator.pop(context);
                                      _shareTweet();
                                    },
                                  ),
                                  if (widget.tweet.audioUrl != null)
                                    ListTile(
                                      leading: const Icon(Icons.audiotrack),
                                      title: const Text('سماع الصوت'),
                                      onTap: () {
                                        Navigator.pop(context);
                                        // Navigate to audio player
                                        context.push(
                                            '/mediaPlayer/${widget.tweet.id}');
                                      },
                                    ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                ],
              ),
            ),

            // Tweet content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                displayContent,
                style: const TextStyle(fontSize: 16),
              ),
            ),

            // Tweet image if available
            if (widget.tweet.tweetImageUrl != null ||
                (widget.tweet.thumbnailUrl != null &&
                    widget.tweet.type != MediaType.audio))
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: UniversalImage(
                    path: widget.tweet.tweetImageUrl ??
                        widget.tweet.thumbnailUrl!,
                  ),
                ),
              ),

            // Tweet date
            if (formattedDate.isNotEmpty)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Text(
                  formattedDate,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),

            // Divider
            Divider(color: Colors.grey[300]),

            // Tweet stats
            if (widget.isDetailView)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Row(
                  children: <Widget>[
                    _TweetStat(
                      count: '$retweetCount',
                      label: 'إعادة',
                    ),
                    const SizedBox(width: 24),
                    _TweetStat(
                      count: '$likeCount',
                      label: 'إعجاب',
                    ),
                    const SizedBox(width: 24),
                    _TweetStat(
                      count: '$commentCount',
                      label: 'تعليق',
                    ),
                  ],
                ),
              ),

            if (widget.isDetailView) Divider(color: Colors.grey[300]),

            // Tweet actions
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  _TweetAction(
                    icon: Icons.chat_bubble_outline,
                    label: 'تعليق',
                    onTap: _addComment,
                  ),
                  _TweetAction(
                    icon: isRetweeted ? Icons.repeat : Icons.repeat,
                    label: 'إعادة',
                    onTap: _toggleRetweet,
                    isActive: isRetweeted,
                    activeColor: Colors.green,
                  ),
                  _TweetAction(
                    icon: isLiked ? Icons.favorite : Icons.favorite_border,
                    label: 'إعجاب',
                    onTap: _toggleLike,
                    isActive: isLiked,
                    activeColor: Colors.red,
                  ),
                  _TweetAction(
                    icon: Icons.share_outlined,
                    label: 'مشاركة',
                    onTap: _shareTweet,
                  ),
                ],
              ),
            ),

            // Audio player if available
            if (widget.isDetailView && widget.tweet.audioUrl != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('سماع صوت'),
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size.fromHeight(50),
                  ),
                  onPressed: () {
                    // Navigate to audio player
                    context.push('/mediaPlayer/${widget.tweet.id}');
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _TweetStat extends StatelessWidget {
  const _TweetStat({
    required this.count,
    required this.label,
  });

  final String count;
  final String label;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Text(
          count,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}

class _TweetAction extends StatelessWidget {
  const _TweetAction({
    required this.icon,
    required this.label,
    required this.onTap,
    this.isActive = false,
    this.activeColor,
  });

  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isActive;
  final Color? activeColor;

  @override
  Widget build(BuildContext context) {
    final Color iconColor =
        isActive && activeColor != null ? activeColor! : Colors.grey[700]!;

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Icon(icon, size: 20, color: iconColor),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: iconColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
