import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_ui_model.dart';
import 'widgets/tweet_card.dart';

class TweetsListScreen extends ConsumerWidget {
  const TweetsListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<MediaUiModel> allMedia = ref.watch(filteredMediaProvider);

    // Filter only tweet items
    final List<MediaUiModel> tweetItems = allMedia.where((MediaUiModel item) {
      final MediaType itemType = item.type;
      final MediaCategory itemCategory = item.category;
      return (itemType == MediaType.tweet) ||
          (itemType == MediaType.text &&
              itemCategory == MediaCategory.tweetCategory) ||
          (item.tweetContent != null && item.tweetContent!.isNotEmpty);
    }).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tweets'),
        centerTitle: true,
      ),
      body: tweetItems.isEmpty
          ? const Center(
              child: Text('No tweets available'),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: tweetItems.length,
              itemBuilder: (BuildContext context, int index) {
                final MediaUiModel tweet = tweetItems[index];
                return TweetCard(
                  tweet: tweet,
                  onTap: () {
                    context.pushNamed('tweetDetails',
                        pathParameters: <String, String>{'id': tweet.id});
                  },
                );
              },
            ),
    );
  }
}
