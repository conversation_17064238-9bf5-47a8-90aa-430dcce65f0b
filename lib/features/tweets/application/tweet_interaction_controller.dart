import 'package:riverpod_annotation/riverpod_annotation.dart';

// This will be generated after running build_runner
part 'tweet_interaction_controller.g.dart';

/// Controller for handling tweet interactions like likes, retweets, etc.
@riverpod
class TweetLikeController extends _$TweetLikeController {
  @override
  bool build(String tweetId) {
    // Default state - not liked
    return false;
  }

  /// Toggle like status for a tweet
  void toggleLike() {
    state = !state;
    // Here you would typically call an API to update the like status
    _updateLikeStatus(tweetId, state);
  }

  /// Update like status on the server
  Future<void> _updateLikeStatus(String tweetId, bool isLiked) async {
    // Implementation for API call to update like status
    // Example:
    // await ref.read(apiServiceProvider).updateLikeStatus(tweetId, isLiked);
  }
}

/// Controller for handling tweet bookmark functionality
@riverpod
class TweetBookmarkController extends _$TweetBookmarkController {
  @override
  bool build(String tweetId) {
    // Default state - not bookmarked
    return false;
  }

  /// Toggle bookmark status for a tweet
  void toggleBookmark() {
    state = !state;
    // Here you would typically call an API to update the bookmark status
    _updateBookmarkStatus(tweetId, state);
  }

  /// Update bookmark status on the server
  Future<void> _updateBookmarkStatus(String tweetId, bool isBookmarked) async {
    // Implementation for API call to update bookmark status
    // Example:
    // await ref.read(apiServiceProvider).updateBookmarkStatus(tweetId, isBookmarked);
  }
}

/// Controller for handling tweet retweet functionality
@riverpod
class TweetRetweetController extends _$TweetRetweetController {
  @override
  bool build(String tweetId) {
    // Default state - not retweeted
    return false;
  }

  /// Toggle retweet status for a tweet
  void toggleRetweet() {
    state = !state;
    // Here you would typically call an API to update the retweet status
    _updateRetweetStatus(tweetId, state);
  }

  /// Update retweet status on the server
  Future<void> _updateRetweetStatus(String tweetId, bool isRetweeted) async {
    // Implementation for API call to update retweet status
    // Example:
    // await ref.read(apiServiceProvider).updateRetweetStatus(tweetId, isRetweeted);
  }
}

/// Provider for tweet like count
@riverpod
class TweetLikeCountProvider extends _$TweetLikeCountProvider {
  @override
  int build(String tweetId) {
    // Fetch initial like count from repository or API
    return 0; // Default value
  }

  /// Increment like count
  void increment() {
    state = state + 1;
  }

  /// Decrement like count
  void decrement() {
    if (state > 0) {
      state = state - 1;
    }
  }
}

/// Provider for tweet retweet count
@riverpod
class TweetRetweetCountProvider extends _$TweetRetweetCountProvider {
  @override
  int build(String tweetId) {
    // Fetch initial retweet count from repository or API
    return 0; // Default value
  }

  /// Increment retweet count
  void increment() {
    state = state + 1;
  }

  /// Decrement retweet count
  void decrement() {
    if (state > 0) {
      state = state - 1;
    }
  }
}

/// Provider for tweet comment count
@riverpod
class TweetCommentCountProvider extends _$TweetCommentCountProvider {
  @override
  int build(String tweetId) {
    // Fetch initial comment count from repository or API
    return 0; // Default value
  }

  /// Increment comment count
  void increment() {
    state = state + 1;
  }

  /// Decrement comment count
  void decrement() {
    if (state > 0) {
      state = state - 1;
    }
  }
}
