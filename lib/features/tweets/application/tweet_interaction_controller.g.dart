// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tweet_interaction_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Controller for handling tweet interactions like likes, retweets, etc.
@ProviderFor(TweetLikeController)
const tweetLikeControllerProvider = TweetLikeControllerFamily._();

/// Controller for handling tweet interactions like likes, retweets, etc.
final class TweetLikeControllerProvider
    extends $NotifierProvider<TweetLikeController, bool> {
  /// Controller for handling tweet interactions like likes, retweets, etc.
  const TweetLikeControllerProvider._(
      {required TweetLikeControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetLikeControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetLikeControllerHash();

  @override
  String toString() {
    return r'tweetLikeControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetLikeController create() => TweetLikeController();

  @$internal
  @override
  $NotifierProviderElement<TweetLikeController, bool> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<bool>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetLikeControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetLikeControllerHash() =>
    r'4b13f6e03f09af7389c12bb8d60c2f6e6425c6d8';

/// Controller for handling tweet interactions like likes, retweets, etc.
final class TweetLikeControllerFamily extends $Family
    with $ClassFamilyOverride<TweetLikeController, bool, bool, bool, String> {
  const TweetLikeControllerFamily._()
      : super(
          retry: null,
          name: r'tweetLikeControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Controller for handling tweet interactions like likes, retweets, etc.
  TweetLikeControllerProvider call(
    String tweetId,
  ) =>
      TweetLikeControllerProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetLikeControllerProvider';
}

abstract class _$TweetLikeController extends $Notifier<bool> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  bool build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<bool>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<bool>, bool, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Controller for handling tweet bookmark functionality
@ProviderFor(TweetBookmarkController)
const tweetBookmarkControllerProvider = TweetBookmarkControllerFamily._();

/// Controller for handling tweet bookmark functionality
final class TweetBookmarkControllerProvider
    extends $NotifierProvider<TweetBookmarkController, bool> {
  /// Controller for handling tweet bookmark functionality
  const TweetBookmarkControllerProvider._(
      {required TweetBookmarkControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetBookmarkControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetBookmarkControllerHash();

  @override
  String toString() {
    return r'tweetBookmarkControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetBookmarkController create() => TweetBookmarkController();

  @$internal
  @override
  $NotifierProviderElement<TweetBookmarkController, bool> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<bool>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetBookmarkControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetBookmarkControllerHash() =>
    r'74fa47a4d792a65c63cea2a4350eebcaedd02f84';

/// Controller for handling tweet bookmark functionality
final class TweetBookmarkControllerFamily extends $Family
    with
        $ClassFamilyOverride<TweetBookmarkController, bool, bool, bool,
            String> {
  const TweetBookmarkControllerFamily._()
      : super(
          retry: null,
          name: r'tweetBookmarkControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Controller for handling tweet bookmark functionality
  TweetBookmarkControllerProvider call(
    String tweetId,
  ) =>
      TweetBookmarkControllerProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetBookmarkControllerProvider';
}

abstract class _$TweetBookmarkController extends $Notifier<bool> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  bool build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<bool>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<bool>, bool, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Controller for handling tweet retweet functionality
@ProviderFor(TweetRetweetController)
const tweetRetweetControllerProvider = TweetRetweetControllerFamily._();

/// Controller for handling tweet retweet functionality
final class TweetRetweetControllerProvider
    extends $NotifierProvider<TweetRetweetController, bool> {
  /// Controller for handling tweet retweet functionality
  const TweetRetweetControllerProvider._(
      {required TweetRetweetControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetRetweetControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetRetweetControllerHash();

  @override
  String toString() {
    return r'tweetRetweetControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetRetweetController create() => TweetRetweetController();

  @$internal
  @override
  $NotifierProviderElement<TweetRetweetController, bool> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<bool>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetRetweetControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetRetweetControllerHash() =>
    r'cf5c5b442378650f31f1fd5d444863e182dd14cf';

/// Controller for handling tweet retweet functionality
final class TweetRetweetControllerFamily extends $Family
    with
        $ClassFamilyOverride<TweetRetweetController, bool, bool, bool, String> {
  const TweetRetweetControllerFamily._()
      : super(
          retry: null,
          name: r'tweetRetweetControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Controller for handling tweet retweet functionality
  TweetRetweetControllerProvider call(
    String tweetId,
  ) =>
      TweetRetweetControllerProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetRetweetControllerProvider';
}

abstract class _$TweetRetweetController extends $Notifier<bool> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  bool build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<bool>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<bool>, bool, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Provider for tweet like count
@ProviderFor(TweetLikeCountProvider)
const tweetLikeCountProviderProvider = TweetLikeCountProviderFamily._();

/// Provider for tweet like count
final class TweetLikeCountProviderProvider
    extends $NotifierProvider<TweetLikeCountProvider, int> {
  /// Provider for tweet like count
  const TweetLikeCountProviderProvider._(
      {required TweetLikeCountProviderFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetLikeCountProviderProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetLikeCountProviderHash();

  @override
  String toString() {
    return r'tweetLikeCountProviderProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetLikeCountProvider create() => TweetLikeCountProvider();

  @$internal
  @override
  $NotifierProviderElement<TweetLikeCountProvider, int> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<int>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetLikeCountProviderProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetLikeCountProviderHash() =>
    r'f6fd5e19a24c45d65bb7569b1c632e83b8ce7266';

/// Provider for tweet like count
final class TweetLikeCountProviderFamily extends $Family
    with $ClassFamilyOverride<TweetLikeCountProvider, int, int, int, String> {
  const TweetLikeCountProviderFamily._()
      : super(
          retry: null,
          name: r'tweetLikeCountProviderProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Provider for tweet like count
  TweetLikeCountProviderProvider call(
    String tweetId,
  ) =>
      TweetLikeCountProviderProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetLikeCountProviderProvider';
}

abstract class _$TweetLikeCountProvider extends $Notifier<int> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  int build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Provider for tweet retweet count
@ProviderFor(TweetRetweetCountProvider)
const tweetRetweetCountProviderProvider = TweetRetweetCountProviderFamily._();

/// Provider for tweet retweet count
final class TweetRetweetCountProviderProvider
    extends $NotifierProvider<TweetRetweetCountProvider, int> {
  /// Provider for tweet retweet count
  const TweetRetweetCountProviderProvider._(
      {required TweetRetweetCountProviderFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetRetweetCountProviderProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetRetweetCountProviderHash();

  @override
  String toString() {
    return r'tweetRetweetCountProviderProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetRetweetCountProvider create() => TweetRetweetCountProvider();

  @$internal
  @override
  $NotifierProviderElement<TweetRetweetCountProvider, int> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<int>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetRetweetCountProviderProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetRetweetCountProviderHash() =>
    r'd0b401e1096b23bbc377565f9e5c27c169b8255e';

/// Provider for tweet retweet count
final class TweetRetweetCountProviderFamily extends $Family
    with
        $ClassFamilyOverride<TweetRetweetCountProvider, int, int, int, String> {
  const TweetRetweetCountProviderFamily._()
      : super(
          retry: null,
          name: r'tweetRetweetCountProviderProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Provider for tweet retweet count
  TweetRetweetCountProviderProvider call(
    String tweetId,
  ) =>
      TweetRetweetCountProviderProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetRetweetCountProviderProvider';
}

abstract class _$TweetRetweetCountProvider extends $Notifier<int> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  int build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Provider for tweet comment count
@ProviderFor(TweetCommentCountProvider)
const tweetCommentCountProviderProvider = TweetCommentCountProviderFamily._();

/// Provider for tweet comment count
final class TweetCommentCountProviderProvider
    extends $NotifierProvider<TweetCommentCountProvider, int> {
  /// Provider for tweet comment count
  const TweetCommentCountProviderProvider._(
      {required TweetCommentCountProviderFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetCommentCountProviderProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetCommentCountProviderHash();

  @override
  String toString() {
    return r'tweetCommentCountProviderProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetCommentCountProvider create() => TweetCommentCountProvider();

  @$internal
  @override
  $NotifierProviderElement<TweetCommentCountProvider, int> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<int>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetCommentCountProviderProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetCommentCountProviderHash() =>
    r'2c9dc720b2fc5824b55ffab29a18f75870356298';

/// Provider for tweet comment count
final class TweetCommentCountProviderFamily extends $Family
    with
        $ClassFamilyOverride<TweetCommentCountProvider, int, int, int, String> {
  const TweetCommentCountProviderFamily._()
      : super(
          retry: null,
          name: r'tweetCommentCountProviderProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Provider for tweet comment count
  TweetCommentCountProviderProvider call(
    String tweetId,
  ) =>
      TweetCommentCountProviderProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetCommentCountProviderProvider';
}

abstract class _$TweetCommentCountProvider extends $Notifier<int> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  int build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
