import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/widgets/user_data_list_item.dart';
import '../../../data/models/user_data_models.dart';
import '../../../utils/context_extensions.dart';
import '../../user_data/application/media_providers.dart';
import '../../user_data/application/user_data_providers.dart';

class FavoritesScreen extends ConsumerWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add auto-refresh to ensure the list updates when items are removed
    final AsyncValue<List<FavoriteItem>> favoritesAsync =
        ref.watch(favoritesNotifierProvider);

    // Listen for changes to the favorites list
    ref.listen<AsyncValue<List<FavoriteItem>>>(
      favoritesNotifierProvider,
      (_, __) {
        // This will trigger when the favorites list changes
      },
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _confirmClearFavorites(context, ref),
            tooltip: 'حذف جميع المفضلة',
          ),
        ],
      ),
      backgroundColor: context.colorScheme.surface,
      body: favoritesAsync.when(
        data: (List<FavoriteItem> favorites) {
          if (favorites.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildFavoritesList(context, ref, favorites);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.favorite_border,
            size: 64,
            color: context.colorScheme.secondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عناصر في المفضلة',
            style: context.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك إضافة العناصر إلى المفضلة بالضغط على أيقونة القلب',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList(
    BuildContext context,
    WidgetRef ref,
    List<FavoriteItem> favorites,
  ) {
    return ListView.builder(
      itemCount: favorites.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (BuildContext context, int index) {
        final FavoriteItem favorite = favorites[index];

        return UserDataListItem.favorite(
          itemId: favorite.itemId,
          dateTime: favorite.createdAt,
          onRemove: () => _removeFavorite(context, favorite, ref),
        );
      },
    );
  }

  Future<void> _removeFavorite(
      BuildContext context, FavoriteItem favorite, WidgetRef ref) async {
    // Toggle favorite (which will remove it since it's already favorited)
    await ref.read(favoritesNotifierProvider.notifier).toggleFavorite(
          itemId: favorite.itemId,
          type: favorite.type,
        );

    // Force refresh the favorites list
    ref.invalidate(favoritesNotifierProvider);

    // Also invalidate the specific media item to ensure it's refreshed
    ref.invalidate(mediaItemProvider(favorite.itemId));

    // Show a snackbar to confirm the action
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تمت إزالة العنصر من المفضلة')),
      );
    }
  }

  Future<void> _confirmClearFavorites(
      BuildContext context, WidgetRef ref) async {
    final bool confirm = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('حذف المفضلة'),
              content: const Text('هل أنت متأكد أنك تريد حذف جميع المفضلة؟'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: const Text('حذف'),
                ),
              ],
            );
          },
        ) ??
        false;

    if (confirm && context.mounted) {
      await ref.read(favoritesNotifierProvider.notifier).clearFavorites();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف جميع المفضلة بنجاح')),
        );
      }
    }
  }
}
