// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';

// import '../../../../config/theme/theme_logic.dart';
// import '../widgets/theme_showcase.dart';

// class ThemeSettingsPage extends ConsumerWidget {
//   const ThemeSettingsPage({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final ThemeMode currentThemeMode = ref.watch(themeLogicProvider).themeMode;

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Theme Settings'),
//       ),
//       body: Column(
//         children: <Widget>[
//           // Theme mode selector
//           Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Card(
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: <Widget>[
//                     Text(
//                       'Theme Mode',
//                       style: Theme.of(context).textTheme.titleLarge,
//                     ),
//                     const SizedBox(height: 16),
//                     _buildThemeModeSelector(context, ref, currentThemeMode),
//                   ],
//                 ),
//               ),
//             ),
//           ),

//           // Theme showcase
//           const Expanded(
//             child: ThemeShowcase(),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildThemeModeSelector(
//       BuildContext context, WidgetRef ref, ThemeMode currentThemeMode) {
//     return SegmentedButton<ThemeMode>(
//       segments: const <ButtonSegment<ThemeMode>>[
//         ButtonSegment<ThemeMode>(
//           value: ThemeMode.light,
//           icon: Icon(Icons.light_mode),
//           label: Text('Light'),
//         ),
//         ButtonSegment<ThemeMode>(
//           value: ThemeMode.dark,
//           icon: Icon(Icons.dark_mode),
//           label: Text('Dark'),
//         ),
//         ButtonSegment<ThemeMode>(
//           value: ThemeMode.system,
//           icon: Icon(Icons.settings_suggest),
//           label: Text('System'),
//         ),
//       ],
//       selected: <ThemeMode>{currentThemeMode},
//       onSelectionChanged: (Set<ThemeMode> selection) {
//         if (selection.isNotEmpty) {
//           ref.read(themeLogicProvider.notifier).setThemeMode(selection.first);
//         }
//       },
//     );
//   }
// }
