import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/widgets/user_data_list_item.dart';
import '../../../data/models/user_data_models.dart';
import '../../../utils/confirmation_dialog.dart';
import '../../../utils/context_extensions.dart';
import '../../user_data/application/user_data_providers.dart';

class HistoryScreen extends ConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<HistoryItem>> historyAsync =
        ref.watch(historyNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل المشاهدة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _confirmClearHistory(context, ref),
            tooltip: 'حذف سجل المشاهدة',
          ),
        ],
      ),
      backgroundColor: context.colorScheme.surface,
      body: historyAsync.when(
        data: (List<HistoryItem> history) {
          if (history.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildHistoryList(context, ref, history);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.history,
            size: 64,
            color: context.colorScheme.secondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'سجل المشاهدة فارغ',
            style: context.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا العناصر التي قمت بمشاهدتها',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList(
    BuildContext context,
    WidgetRef ref,
    List<HistoryItem> history,
  ) {
    // Sort history by timestamp (newest first)
    final List<HistoryItem> sortedHistory = List<HistoryItem>.from(history)
      ..sort(
          (HistoryItem a, HistoryItem b) => b.timestamp.compareTo(a.timestamp));

    return ListView.builder(
      itemCount: sortedHistory.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (BuildContext context, int index) {
        final HistoryItem historyItem = sortedHistory[index];

        return UserDataListItem.history(
          itemId: historyItem.itemId,
          dateTime: historyItem.timestamp,
          actionType: historyItem.actionType,
        );
      },
    );
  }

  Future<void> _confirmClearHistory(BuildContext context, WidgetRef ref) async {
    final bool? confirm = await ConfirmationDialog.showClearHistory(
      context: context,
    );

    if ((confirm ?? false) && context.mounted) {
      await ref.read(historyNotifierProvider.notifier).clearHistory();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف سجل المشاهدة بنجاح')),
        );
      }
    }
  }
}
