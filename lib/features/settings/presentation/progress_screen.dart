import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/widgets/user_data_list_item.dart';
import '../../../data/models/user_data_models.dart';
import '../../../utils/confirmation_dialog.dart';
import '../../../utils/context_extensions.dart';
import '../../user_data/application/user_data_providers.dart';

class ProgressScreen extends ConsumerWidget {
  const ProgressScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<ProgressItem>> progressAsync =
        ref.watch(progressNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقدم المشاهدة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _confirmClearProgress(context, ref),
            tooltip: 'حذف تقدم المشاهدة',
          ),
        ],
      ),
      backgroundColor: context.colorScheme.surface,
      body: progressAsync.when(
        data: (List<ProgressItem> progressItems) {
          if (progressItems.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildProgressList(context, ref, progressItems);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.play_circle_outline,
            size: 64,
            color: context.colorScheme.secondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد تقدم مشاهدة',
            style: context.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'سيظهر هنا تقدم مشاهدتك للفيديوهات والصوتيات',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressList(
    BuildContext context,
    WidgetRef ref,
    List<ProgressItem> progressItems,
  ) {
    // Sort progress by last updated (newest first)
    final List<ProgressItem> sortedProgress =
        List<ProgressItem>.from(progressItems)
          ..sort((ProgressItem a, ProgressItem b) =>
              b.lastUpdated.compareTo(a.lastUpdated));

    return ListView.builder(
      itemCount: sortedProgress.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (BuildContext context, int index) {
        final ProgressItem progressItem = sortedProgress[index];

        return UserDataListItem.progress(
          itemId: progressItem.itemId,
          dateTime: progressItem.lastUpdated,
          positionSeconds: progressItem.positionSeconds,
        );
      },
    );
  }

  Future<void> _confirmClearProgress(
      BuildContext context, WidgetRef ref) async {
    final bool? confirm = await ConfirmationDialog.showClearProgress(
      context: context,
    );

    if ((confirm ?? false) && context.mounted) {
      await ref.read(progressNotifierProvider.notifier).clearProgress();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف تقدم المشاهدة بنجاح')),
        );
      }
    }
  }
}
