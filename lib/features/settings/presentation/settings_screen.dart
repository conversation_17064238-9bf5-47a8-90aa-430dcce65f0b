import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../routing/app_router.dart';
import '../../../utils/confirmation_dialog.dart';
import '../../../utils/context_extensions.dart';
import '../../auth/application/user_provider.dart';
import '../../home/<USER>/header.dart';
import '../../home/<USER>/theme_widget.dart';
import '../../user_data/application/user_data_providers.dart';
import 'settings_tile.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bool isAuthenticated = ref.watch(isAuthenticatedProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      backgroundColor: context.colorScheme.surface,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const Header(text: 'المظهر'),
            const Divider(),
            const ThemeWidget(),
            const SizedBox(height: 16),
            const Header(text: 'الحساب'),
            const Divider(),
            if (isAuthenticated) ...<Widget>[
              SettingsTile(
                title: 'الملف الشخصي',
                icon: Icons.person,
                onTap: () => context.push(SGRoute.profile.route),
              ),
              SettingsTile(
                title: 'تغيير كلمة المرور',
                icon: Icons.lock_outline,
                onTap: () => context.push(SGRoute.changePassword.route),
              ),
              SettingsTile(
                title: 'تسجيل الخروج',
                icon: Icons.logout,
                onTap: () => _confirmLogout(context, ref),
              ),
            ] else ...<Widget>[
              SettingsTile(
                title: 'تسجيل الدخول',
                icon: Icons.login,
                onTap: () => context.push(SGRoute.login.route),
              ),
              SettingsTile(
                title: 'إنشاء حساب',
                icon: Icons.person_add,
                onTap: () => context.push(SGRoute.register.route),
              ),
            ],
            const Header(text: 'البيانات'),
            const Divider(),
            SettingsTile(
              title: 'المفضلة',
              icon: Icons.favorite,
              onTap: () => _navigateToFavorites(context),
              trailing: IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: () => _confirmClearData(
                  context,
                  ref,
                  'المفضلة',
                  'هل أنت متأكد أنك تريد حذف جميع المفضلة؟',
                  () => ref
                      .read(favoritesNotifierProvider.notifier)
                      .clearFavorites(),
                ),
              ),
            ),
            SettingsTile(
              title: 'سجل المشاهدة',
              icon: Icons.history,
              onTap: () => _navigateToHistory(context),
              trailing: IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: () => _confirmClearData(
                  context,
                  ref,
                  'سجل المشاهدة',
                  'هل أنت متأكد أنك تريد حذف سجل المشاهدة؟',
                  () =>
                      ref.read(historyNotifierProvider.notifier).clearHistory(),
                ),
              ),
            ),
            SettingsTile(
              title: 'تقدم المشاهدة',
              icon: Icons.play_circle_outline,
              onTap: () => _navigateToProgress(context),
              trailing: IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: () => _confirmClearData(
                  context,
                  ref,
                  'تقدم المشاهدة',
                  'هل أنت متأكد أنك تريد حذف تقدم المشاهدة؟',
                  () => ref
                      .read(progressNotifierProvider.notifier)
                      .clearProgress(),
                ),
              ),
            ),
            SettingsTile(
              title: 'حذف جميع البيانات',
              icon: Icons.delete_forever,
              iconColor: Colors.red,
              onTap: () => _confirmClearData(
                context,
                ref,
                'جميع البيانات',
                'هل أنت متأكد أنك تريد حذف جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.',
                () => ref
                    .read(userDataNotifierProvider.notifier)
                    .clearAllUserData(),
              ),
            ),
            if (isAuthenticated) ...<Widget>[
              const SizedBox(height: 16),
              const Header(text: 'خيارات متقدمة'),
              const Divider(),
              SettingsTile(
                title: 'حذف الحساب',
                icon: Icons.no_accounts,
                iconColor: Colors.red,
                onTap: () => _confirmDeleteAccount(context, ref),
              ),
            ],
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  void _navigateToFavorites(BuildContext context) {
    context.push(SGRoute.favorites.route);
  }

  void _navigateToHistory(BuildContext context) {
    context.push(SGRoute.history.route);
  }

  void _navigateToProgress(BuildContext context) {
    context.push(SGRoute.progress.route);
  }

  Future<void> _confirmClearData(
    BuildContext context,
    WidgetRef ref,
    String dataType,
    String message,
    Future<void> Function() clearAction,
  ) async {
    final bool? confirm = await ConfirmationDialog.showClearData(
      context: context,
      dataType: dataType,
    );

    if ((confirm ?? false) && context.mounted) {
      await clearAction();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف $dataType بنجاح')),
        );
      }
    }
  }

  Future<void> _confirmLogout(BuildContext context, WidgetRef ref) async {
    final bool? confirm = await ConfirmationDialog.showLogout(
      context: context,
    );

    if ((confirm ?? false) && context.mounted) {
      await ref.read(userNotifierProvider.notifier).logout();
      if (context.mounted) {
        context.go(SGRoute.home.route);
      }
    }
  }

  Future<void> _confirmDeleteAccount(
      BuildContext context, WidgetRef ref) async {
    final bool? confirm = await ConfirmationDialog.showDeleteAccount(
      context: context,
    );

    if ((confirm ?? false) && context.mounted) {
      final bool success =
          await ref.read(userNotifierProvider.notifier).deleteAccount();
      if (context.mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الحساب بنجاح')),
          );
          context.go(SGRoute.home.route);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل حذف الحساب. حاول مرة أخرى.')),
          );
        }
      }
    }
  }
}
