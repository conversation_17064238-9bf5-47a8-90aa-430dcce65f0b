import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/svg_icon.dart';
import '../../../common/universal_image.dart';
import '../../../constants/colors.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_item.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../gen/assets.gen.dart';
import '../../../utils/format_utils.dart';

/// Reusable component for displaying media title
class MediaTitle extends ConsumerWidget {
  const MediaTitle({
    super.key,
    required this.title,
    this.style,
    this.maxLines = 1,
    this.overflow = TextOverflow.ellipsis,
  });

  final String title;
  final TextStyle? style;
  final int maxLines;
  final TextOverflow overflow;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Text(
      title,
      style: style ??
          Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(fontWeight: FontWeight.bold, fontSize: 14),
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Reusable component for displaying media description
class MediaDescription extends ConsumerWidget {
  const MediaDescription({
    super.key,
    required this.description,
    this.style,
    this.maxLines = 2,
    this.overflow = TextOverflow.ellipsis,
    this.padding,
  });

  final String description;
  final TextStyle? style;
  final int maxLines;
  final TextOverflow overflow;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Text(
        description,
        textAlign: TextAlign.justify,
        textDirection: TextDirection.rtl,
        style: style ??
            Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                ),
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

// /// Reusable component for displaying media category
// class MediaCategoryWidget extends StatelessWidget {
//   const MediaCategoryWidget({
//     super.key,
//     required this.category,
//     this.style,
//     this.maxLines = 1,
//     this.overflow = TextOverflow.ellipsis,
//   });

//   final String category;
//   final TextStyle? style;
//   final int maxLines;
//   final TextOverflow overflow;

//   @override
//   Widget build(BuildContext context) {
//     return Text(
//       category,
//       style: style ?? Theme.of(context).textTheme.bodySmall,
//       maxLines: maxLines,
//       overflow: overflow,
//     );
//   }
// }

/// Reusable component for displaying media thumbnail
class MediaThumbnail extends ConsumerWidget {
  const MediaThumbnail({
    super.key,
    required this.thumbnailUrl,
    required this.fallbackIcon,
    this.width = 50,
    this.height = 50,
    this.fit = BoxFit.cover,
    this.borderRadius,
  });

  final String? thumbnailUrl;
  final IconData fallbackIcon;
  final double width;
  final double height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Widget child = thumbnailUrl != null
        ? UniversalImage(
            path: thumbnailUrl!,
            width: width,
            height: height,
            fit: fit,
          )
        : Icon(fallbackIcon, size: width);

    return Card(child: child);
  }
}

/// Reusable component for displaying duration info
class OptionsIcon extends ConsumerWidget {
  const OptionsIcon({
    super.key,
    required this.image,
    this.title,
    this.durationSeconds,
    this.iconSize = 14,
    this.date,
  });
  final DateTime? date;
  final String? title;
  final int? durationSeconds;
  final double iconSize;
  final String image;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final String? formattedDuration = durationSeconds != null
        ? formatDurationFromSeconds(durationSeconds!)
        : null;
    final String? formattedDate = date != null ? formatDate(date!) : null;
    return Row(
      spacing: 4,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        SvgIcon(
          image: image,
          height: iconSize,
          width: iconSize,
          color: kPrimaryLight,
        ),
        Text(
          formattedDate ?? formattedDuration ?? title ?? '',
          style: Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(fontSize: 12, color: klightBlackColor),
        ),
      ],
    );
  }

  // Using the utility functions from format_utils.dart
}

/// Reusable component for displaying pages info
class BookInfo extends ConsumerWidget {
  const BookInfo({
    super.key,
    required this.optionTitle,
    required this.optionValue,
  });

  final String optionTitle;
  final String optionValue;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextStyle? textStyle =
        Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 10,
            );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          optionTitle,
          style: textStyle,
        ),
        const SizedBox(width: 4),
        Text(
          optionValue,
          style: textStyle,
        ),
      ],
    );
  }
}

/// Reusable component for displaying media info row
class MediaInfoRow extends ConsumerWidget {
  const MediaInfoRow({
    super.key,
    required this.item,
    this.showPages = false,
    this.spacing = 16.0,
  });

  final MediaUiModel item;
  final bool showPages;
  final double spacing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if this is a book and has book-specific fields
    final bool isBook = item.category == MediaCategory.books;
    final MediaOption? bookOption = isBook ? _getBookOption(item) : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          spacing: 10,
          children: <Widget>[
            if (!showPages) ...<Widget>[
              OptionsIcon(
                  image: Assets.svg.replay,
                  durationSeconds: item.durationSeconds),
              OptionsIcon(image: Assets.svg.calender, date: item.createdAt),
            ],
          ],
        ),

        // Display book-specific information if available
        if (isBook && bookOption != null) ...<Widget>[
          const SizedBox(height: 4),
          _buildBookInfo(context, bookOption),
        ],
      ],
    );
  }

  // Helper method to get the first option that contains book metadata
  MediaOption? _getBookOption(MediaUiModel item) {
    return item.options?.firstWhere(
      (MediaOption option) =>
          option.author != null ||
          option.publisher != null ||
          option.totalPages != null,
      orElse: () => item.options!.first,
    );
  }

  // Build the book information section
  Widget _buildBookInfo(BuildContext context, MediaOption bookOption) {
    return Column(
      spacing: 4,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          spacing: 14,
          children: <Widget>[
            if (bookOption.totalPages != null)
              BookInfo(
                optionTitle: 'عدد الصفحات:',
                optionValue: '${bookOption.totalPages!}',
              ),
            if (bookOption.publisher != null)
              BookInfo(
                optionTitle: 'الناشر:',
                optionValue: bookOption.publisher!,
              ),
          ],
        ),
        // if (bookOption.author != null)
        //   BookInfo(
        //     optionTitle: 'مؤلف:',
        //     optionValue: bookOption.author!,
        //   ),
        // if (bookOption.section != null)
        //   BookInfo(
        //     optionTitle: 'قسم:',
        //     optionValue: bookOption.section!,
        //   ),

        if (bookOption.releaseDate != null)
          BookInfo(
            optionTitle: 'تاريخ الإصدار:',
            optionValue: bookOption.releaseDate!,
          ),
      ],
    );
  }
}

/// Reusable component for document icon container
class DocumentIconContainer extends ConsumerWidget {
  const DocumentIconContainer({
    super.key,
    required this.item,
    this.width = 67,
    this.height = 100,
    this.iconSize = 40,
  });

  final MediaUiModel item;
  final double width;
  final double height;
  final double iconSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const <BoxShadow>[
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: item.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: UniversalImage(
                path: item.thumbnailUrl!,
              ),
            )
          : Icon(
              Icons.picture_as_pdf,
              size: iconSize,
              color: Theme.of(context).colorScheme.primary,
            ),
    );
  }
}

/// Reusable component for video card content
class VideoCardContent extends ConsumerWidget {
  const VideoCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: <Widget>[
        const SizedBox(height: 8),
        // Video thumbnail
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Stack(
            children: <Widget>[
              MediaThumbnail(
                thumbnailUrl: item.thumbnailUrl,
                fallbackIcon: Icons.video_library,
                width: double.infinity,
                height: double.infinity,
              ),
              // Play button overlay
              Center(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Details
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              MediaTitle(title: item.title),
              if (item.metadata?.description != null) ...<Widget>[
                const SizedBox(height: 4),
                MediaDescription(description: item.metadata!.description!),
              ],
              const SizedBox(height: 8),
              MediaInfoRow(item: item),
            ],
          ),
        ),
      ],
    );
  }
}

/// Reusable component for audio card content
class AudioCardContent extends ConsumerWidget {
  const AudioCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        children: <Widget>[
          // Audio icon
          SvgIcon(
            image: Assets.svg.headphone,
            height: 40,
            width: 40,
            color: kPrimaryLight,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              spacing: 4,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(
                  title: item.title,
                ),
                if (item.metadata?.description != null)
                  MediaDescription(description: item.metadata!.description!),
                OptionsIcon(
                    image: Assets.svg.replay,
                    durationSeconds: item.durationSeconds),
              ],
            ),
          ),
          SvgIcon(
            image: Assets.svg.arrowNext,
            height: 12,
            width: 6,
            color: kPrimaryLight,
          ),
        ],
      ),
    );
  }
}

/// Reusable component for document card content
class DocumentCardContent extends ConsumerWidget {
  const DocumentCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          DocumentIconContainer(item: item),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(title: item.title),
                const SizedBox(height: 8),
                MediaInfoRow(item: item, showPages: true),
                if (item.metadata?.description != null) ...<Widget>[
                  const SizedBox(height: 8),
                  Text(
                    'نبذة عن الكتاب:',
                    style: Theme.of(context)
                        .textTheme
                        .titleSmall
                        ?.copyWith(fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  MediaDescription(
                    description: item.metadata!.description!,
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: 16),
          Padding(
            padding: const EdgeInsets.only(top: 60),
            child: SvgIcon(
              image: Assets.svg.arrowNext,
              height: 12,
              width: 6,
              color: kPrimaryLight,
            ),
          ),
        ],
      ),
    );
  }
}

/// Reusable component for document card content
class DefaultCardContent extends ConsumerWidget {
  const DefaultCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(title: item.title),
                const SizedBox(height: 8),
                if (item.metadata?.description != null) ...<Widget>[
                  const SizedBox(height: 4),
                  MediaDescription(
                    description: item.metadata!.description!,
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: 16),
          SvgIcon(
            image: Assets.svg.arrowNext,
            height: 12,
            width: 6,
            color: kPrimaryLight,
          ),
        ],
      ),
    );
  }
}
