import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:open_file/open_file.dart';

import '../../../common/universal_image.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_items/media_navigation_helper.dart';
import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_player_state.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../utils/icons_changer.dart';
import '../../media_player/application/media_player_controller.dart';
import '../../media_player/presentation/audio_player_widget.dart';
import '../../media_player/presentation/video_player_widget.dart';

// Note: We're now using SelectedMediaNotifier from media_provider.dart

class CategoryItemsPage extends ConsumerStatefulWidget {
  const CategoryItemsPage({
    required this.type,
    required this.category,
    super.key,
  });

  final MediaType type;
  final MediaCategory category;

  @override
  ConsumerState<CategoryItemsPage> createState() => _CategoryItemsPageState();
}

class _CategoryItemsPageState extends ConsumerState<CategoryItemsPage> {
  // Store the selected media ID as a class variable
  String? _currentSelectedMediaId;
  @override
  Widget build(BuildContext context) {
    // Get all media items
    final List<MediaUiModel> allItems = ref.watch(filteredMediaProvider);

    // Get the selected media ID from the provider
    final String? selectedMediaId = ref.watch(selectedMediaNotifierProvider);

    // Store the current selected media ID for use in dispose
    _currentSelectedMediaId = selectedMediaId;

    List<MediaUiModel> items = <MediaUiModel>[];

    // Handle special case for audio type
    if (widget.type == MediaType.audio) {
      items = allItems.where((MediaUiModel item) {
        final MediaType itemType = item.type;
        return (itemType == MediaType.audio ||
                (itemType == MediaType.tweet && item.audioUrl != null)) &&
            item.category == widget.category;
      }).toList();
    } else {
      items = allItems.where((MediaUiModel item) {
        final MediaType itemType = item.type;
        final MediaType requestedType = widget.type;
        return itemType == requestedType && item.category == widget.category;
      }).toList();
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category.name),
        actions: selectedMediaId != null
            ? <Widget>[
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    ref
                        .read(selectedMediaNotifierProvider.notifier)
                        .clearSelection();
                  },
                ),
              ]
            : null,
      ),
      body: Column(
        children: <Widget>[
          // Media player section (shown when an item is selected)
          if (selectedMediaId != null) _buildMediaPlayer(),

          // List of media items or empty state
          Expanded(
            child: items.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No media items available for ${widget.type}/${widget.category}',
                          style: Theme.of(context).textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () => ref.invalidate(mediaListProvider),
                          child: const Text('Refresh'),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: items.length,
                    itemBuilder: (BuildContext context, int index) {
                      final MediaUiModel item = items[index];
                      final bool isSelected = selectedMediaId == item.id;

                      return Card(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        color: isSelected
                            ? Theme.of(context).colorScheme.primaryContainer
                            : null,
                        child: ListTile(
                          title: Text(item.title),
                          subtitle: Text(item.category.name),
                          leading: item.thumbnailUrl != null
                              ? UniversalImage(
                                  path: item.thumbnailUrl!,
                                  width: 50,
                                  height: 50,
                                )
                              : Icon(
                                  getIconForType(widget.type),
                                  size: 50,
                                ),
                          trailing: isSelected
                              ? const Icon(Icons.play_circle_filled,
                                  color: Colors.green)
                              : null,
                          onTap: () {
                            // For document/PDF media types, navigate to the text media viewer
                            final MediaType itemType = item.type;
                            if (itemType.isDocument ||
                                itemType == MediaType.text ||
                                (item.documentUrl != null &&
                                    item.documentUrl!.isNotEmpty)) {
                              // Use the navigation helper to navigate to the appropriate media player
                              const MediaNavigationHelper()
                                  .navigateToMediaPlayer(context, item,
                                      ref: ref);
                            } else {
                              // For other media types, use the inline player
                              // Toggle selection if tapping the same item, otherwise select the new item
                              ref
                                  .read(selectedMediaNotifierProvider.notifier)
                                  .toggleSelection(item.id);
                            }
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaPlayer() {
    try {
      // Get the selected media ID from the provider
      final String? selectedMediaId = ref.watch(selectedMediaNotifierProvider);

      if (selectedMediaId == null) {
        return const SizedBox.shrink();
      }

      // Create a provider that we can safely watch
      final MediaPlayerControllerProvider mediaPlayerProvider =
          mediaPlayerControllerProvider(selectedMediaId);

      // Use a try-catch block to handle the case where the provider might not be initialized yet
      MediaPlayerState? mediaPlayerState;
      MediaPlayerController? mediaPlayerController;

      try {
        // Get the media player state and controller
        mediaPlayerState = ref.watch(mediaPlayerProvider);
        mediaPlayerController = ref.watch(mediaPlayerProvider.notifier);

        // Don't auto-play the media when it's selected
        // Let the user explicitly press the play button
        if (mediaPlayerState != null &&
            mediaPlayerController != null &&
            !mediaPlayerState.isPlaying &&
            !mediaPlayerState.isLoading &&
            mediaPlayerState.errorMessage == null) {
          debugPrint('Media ready for playback: $selectedMediaId');
          // No auto-play here
        }
      } catch (e) {
        // If we get an error, return a loading indicator
        return const SizedBox(
          height: 300,
          child: Center(child: CircularProgressIndicator()),
        );
      }

      // Safety check - if either is null, show loading
      if (mediaPlayerState == null || mediaPlayerController == null) {
        return const SizedBox(
          height: 300,
          child: Center(child: CircularProgressIndicator()),
        );
      }

      // Show loading indicator if the media is still loading
      if (mediaPlayerState.isLoading) {
        return const SizedBox(
          height: 300,
          child: Center(child: CircularProgressIndicator()),
        );
      }

      // Show error message if there's an error
      if (mediaPlayerState.errorMessage != null) {
        // Check if it's a "File already downloaded" message
        final String errorMessage = mediaPlayerState.errorMessage!;
        final bool isFileAlreadyDownloaded =
            errorMessage.contains('File already downloaded');

        if (isFileAlreadyDownloaded) {
          // Extract the file path from the error message
          final String filePath =
              errorMessage.split('File already downloaded: ').last;

          return SizedBox(
            height: 300,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(Icons.file_download_done,
                      size: 48, color: Colors.green),
                  const SizedBox(height: 16),
                  const Text(
                    'File already downloaded',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    filePath,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      ElevatedButton.icon(
                        icon: const Icon(Icons.open_in_new),
                        label: const Text('Open File'),
                        onPressed: () {
                          // Open the file using the OpenFile package
                          OpenFile.open(filePath);
                        },
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('Play Media'),
                        onPressed: () {
                          // Use the existing method to play downloaded media
                          mediaPlayerController?.playMedia();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }

        // Check if it's an SSL certificate error
        final bool isSSLError = errorMessage.contains('SSL') ||
            errorMessage.contains('certificate') ||
            errorMessage.contains('Chain validation');

        return SizedBox(
          height: 300,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  isSSLError
                      ? 'SSL Certificate Error: Unable to verify the media source. This may be due to an expired certificate or incorrect device time.'
                      : 'Error: ${mediaPlayerState.errorMessage}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    ElevatedButton(
                      onPressed: () {
                        // Try to reload the media
                        ref
                            .read(selectedMediaNotifierProvider.notifier)
                            .reloadMedia();
                      },
                      child: const Text('Retry'),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref
                            .read(selectedMediaNotifierProvider.notifier)
                            .clearSelection();
                      },
                      child: const Text('Close Player'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      }

      // Show media player if media item is available
      if (mediaPlayerState.mediaItem != null) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.black
                    .withValues(red: 0, green: 0, blue: 0, alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: <Widget>[
              // Media player header with title and actions
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        mediaPlayerState.mediaItem?.title ?? 'Now Playing',
                        style: Theme.of(context).textTheme.titleMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Play downloaded file button
                    IconButton(
                      icon: const Icon(Icons.play_circle_outline),
                      onPressed: () => mediaPlayerController?.playMedia(),
                      tooltip: 'Play downloaded file',
                    ),
                    // Download button
                    IconButton(
                      icon: mediaPlayerState.isDownloading ?? false
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.download),
                      onPressed: mediaPlayerState.isDownloading ?? false
                          ? null
                          : () => mediaPlayerController?.downloadMedia(),
                      tooltip: 'Download media',
                    ),
                    // Share button
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () => mediaPlayerController?.shareContent(),
                      tooltip: 'Share media',
                    ),
                    // Close button
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        ref
                            .read(selectedMediaNotifierProvider.notifier)
                            .clearSelection();
                      },
                      tooltip: 'Close player',
                    ),
                  ],
                ),
              ),

              // Media player (audio or video)
              SizedBox(
                height: 200,
                child:
                    _buildMediaContent(mediaPlayerState, mediaPlayerController),
              ),

              // Download progress indicator
              if (mediaPlayerState.isDownloading ?? false)
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'Downloading: ${(mediaPlayerState.downloadProgress! * 100).toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: mediaPlayerState.downloadProgress,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      }

      // Fallback if no media item is available
      return const SizedBox(
        height: 300,
        child: Center(child: Text('No media available')),
      );
    } catch (e) {
      // Fallback for any errors
      return SizedBox(
        height: 300,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'An error occurred: $e',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref
                      .read(selectedMediaNotifierProvider.notifier)
                      .clearSelection();
                },
                child: const Text('Close Player'),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildMediaContent(
    MediaPlayerState mediaPlayerState,
    MediaPlayerController mediaPlayerController,
  ) {
    try {
      // Check if mediaItem is null first
      if (mediaPlayerState.mediaItem == null) {
        return const Center(
          child: Text('Media item not found'),
        );
      }

      final MediaType mediaType = mediaPlayerState.mediaItem!.type;

      if (mediaType == MediaType.audio) {
        return AudioPlayerWidget(
          mediaId: mediaPlayerState.mediaItem!.id,
          thumbnailUrl: mediaPlayerState.mediaItem?.thumbnailUrl,
          isPlaying: mediaPlayerState.isPlaying,
          audioPlayer: mediaPlayerController.audioPlayerInstance,
        );
      } else if (mediaType == MediaType.video) {
        final ChewieController? chewieController =
            mediaPlayerController.chewieControllerInstance;
        if (chewieController != null) {
          return VideoPlayerWidget(chewieController: chewieController);
        } else {
          return const Center(
            child: Text('Video player not initialized'),
          );
        }
      } else if (mediaType == MediaType.pdf ||
          mediaType == MediaType.document ||
          mediaType == MediaType.text ||
          (mediaPlayerState.mediaItem!.documentUrl != null &&
              mediaPlayerState.mediaItem!.documentUrl!.isNotEmpty)) {
        // For document types, show a preview and a button to open the full viewer
        final String? documentUrl = mediaPlayerState.mediaItem!.documentUrl;
        final bool isDocx = documentUrl != null &&
            (documentUrl.toLowerCase().endsWith('.docx') ||
                documentUrl.toLowerCase().endsWith('.doc'));

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(
                isDocx ? Icons.description : Icons.picture_as_pdf,
                size: 64,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                isDocx
                    ? 'Word Document: ${mediaPlayerState.mediaItem!.title}'
                    : 'Document: ${mediaPlayerState.mediaItem!.title}',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              if (isDocx)
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 24.0, vertical: 8.0),
                  child: Text(
                    'This is a Microsoft Word document which will open in the document viewer.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.open_in_new),
                label: const Text('Open in Document Viewer'),
                onPressed: () {
                  // Navigate to the text media viewer
                  const MediaNavigationHelper().navigateToMediaPlayer(
                      context, mediaPlayerState.mediaItem!);
                },
              ),
            ],
          ),
        );
      }

      return Center(
        child: Text('Unsupported media type: $mediaType'),
      );
    } catch (e) {
      return Center(
        child: Text('Error building media player: $e'),
      );
    }
  }

  @override
  void dispose() {
    // Use the stored selected media ID instead of reading from the provider
    // This avoids using ref in dispose which can cause issues

    // Make sure to clean up any resources when the widget is disposed
    if (_currentSelectedMediaId != null) {
      try {
        // Note: The controller's dispose will be called automatically by Riverpod
        // We don't need to do anything here
        debugPrint('Disposing widget with media ID: $_currentSelectedMediaId');
      } catch (e) {
        // If there's any error during disposal, log it
        debugPrint('Error during disposal: $e');
      }
    }
    super.dispose();
  }
}
