// class _HomeMiniPlayer extends ConsumerWidget {
//   const _HomeMiniPlayer({required this.mediaId});
//   final String mediaId;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final MediaPlayerState mediaPlayerState =
//         ref.watch(mediaPlayerControllerProvider(mediaId));
//     final MediaPlayerController mediaPlayerController =
//         ref.watch(mediaPlayerControllerProvider(mediaId).notifier);
//     final MediaUiModel? mediaItem =
//         ref.watch(mediaItemDetailsProvider(mediaId));

//     if (mediaPlayerState.isLoading || mediaItem == null) {
//       return const SizedBox.shrink();
//     }

//     IconData mediaTypeIcon;
//     String mediaTypeText = '';
//     final MediaType itemType = mediaItem.type;

//     switch (itemType) {
//       case MediaType.audio:
//         mediaTypeIcon = Icons.audiotrack;
//         break;
//       case MediaType.video:
//         mediaTypeIcon = Icons.videocam;
//         break;
//       case MediaType.pdf:
//         mediaTypeIcon = Icons.picture_as_pdf;
//         mediaTypeText = 'ملف PDF';
//         break;
//       case MediaType.tweet:
//         mediaTypeIcon = Icons.format_quote;
//         mediaTypeText = 'تغريدة';
//         break;
//       case MediaType.document:
//         mediaTypeIcon = Icons.insert_drive_file;
//         mediaTypeText = 'مستند';
//         break;
//       case MediaType.html:
//         mediaTypeIcon = Icons.insert_drive_file;
//         mediaTypeText = 'مقال';
//         break;
//       default:
//         mediaTypeIcon = Icons.insert_drive_file;
//         mediaTypeText = 'غير معروف';
//     }

//     return GestureDetector(
//       onTap: () => const MediaNavigationHelper()
//           .navigateToMediaPlayer(context, mediaItem, ref: ref),
//       child: Container(
//         height: 70,
//         decoration: BoxDecoration(
//           color: Theme.of(context).colorScheme.primary,
//           borderRadius: const BorderRadius.only(
//               topLeft: Radius.circular(10), topRight: Radius.circular(10)),
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
//         child: Row(
//           children: <Widget>[
//             Container(
//               width: 40,
//               height: 40,
//               decoration: BoxDecoration(
//                   color: Theme.of(context).colorScheme.primaryContainer,
//                   borderRadius: BorderRadius.circular(8)),
//               child: Icon(mediaTypeIcon, color: kWhiteColor),
//             ),
//             const SizedBox(width: 12),
//             Expanded(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: <Widget>[
//                   Text(mediaItem.title,
//                       style: const TextStyle(
//                           fontWeight: FontWeight.bold, color: kWhiteColor),
//                       overflow: TextOverflow.ellipsis,
//                       maxLines: 1),
//                   const SizedBox(height: 4),
//                   if (itemType == MediaType.audio ||
//                       itemType == MediaType.video) ...<Widget>[
//                     LinearProgressIndicator(
//                       value: mediaPlayerState.totalDuration.inMilliseconds > 0
//                           ? mediaPlayerState.currentPosition.inMilliseconds /
//                               mediaPlayerState.totalDuration.inMilliseconds
//                           : 0.0,
//                       valueColor:
//                           const AlwaysStoppedAnimation<Color>(kWhiteColor),
//                       backgroundColor: kPrimaryShadow.withAlpha(128),
//                     ),
//                     Text(
//                       '${_formatDuration(mediaPlayerState.currentPosition)} / ${_formatDuration(mediaPlayerState.totalDuration)}',
//                       style: const TextStyle(fontSize: 12, color: kWhiteColor),
//                     ),
//                   ] else
//                     Text(mediaTypeText,
//                         style:
//                             const TextStyle(fontSize: 12, color: kWhiteColor)),
//                 ],
//               ),
//             ),
//             if (itemType == MediaType.audio || itemType == MediaType.video)
//               IconButton(
//                 icon: Icon(
//                     mediaPlayerState.isPlaying ? Icons.pause : Icons.play_arrow,
//                     color: kWhiteColor),
//                 onPressed: () => mediaPlayerState.isPlaying
//                     ? mediaPlayerController.pause()
//                     : mediaPlayerController.playMedia(),
//               ),
//             IconButton(
//               icon: const Icon(Icons.fullscreen, color: kWhiteColor),
//               onPressed: () => const MediaNavigationHelper()
//                   .navigateToMediaPlayer(context, mediaItem, ref: ref),
//             ),
//             IconButton(
//               icon: const Icon(Icons.close, color: kWhiteColor),
//               onPressed: () {
//                 if (mediaPlayerState.isPlaying) {
//                   mediaPlayerController.pause();
//                 }
//                 ref.read(miniPlayerMediaIdProvider.notifier).clearMediaId();
//                 ref
//                     .read(selectedMediaNotifierProvider.notifier)
//                     .clearSelection();
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   String _formatDuration(Duration duration) {
//     String twoDigits(int n) => n.toString().padLeft(2, '0');
//     final String minutes = twoDigits(duration.inMinutes.remainder(60));
//     final String seconds = twoDigits(duration.inSeconds.remainder(60));
//     return '$minutes:$seconds';
//   }
// }


  //  if (miniPlayerMediaId != null)
  //               Positioned(
  //                 bottom: 0,
  //                 left: 0,
  //                 right: 0,
  //                 child: _HomeMiniPlayer(mediaId: miniPlayerMediaId),
  //               ),
