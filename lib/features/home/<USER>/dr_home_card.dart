import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../common/svg_icon.dart';
import '../../../common/universal_image.dart';
import '../../../constants/colors.dart';
import '../../../gen/assets.gen.dart';
import '../../../routing/app_router.dart';

class DrHomeCard extends ConsumerWidget {
  const DrHomeCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.sizeOf(context);
    return SizedBox(
      height: size.height * 0.4,
      width: size.width,
      child: Stack(
        clipBehavior: Clip.none,
        // alignment: Alignment.center,
        children: <Widget>[
          UniversalImage(
            path: Assets.img.drImage.path,
          ),
          const Positioned(
            top: 0,
            child: CustomAppBar(),
          ),
          // White card section
          Positioned.fill(
            top: size.height * 0.24,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.1),
              child: const DrImageHeader(),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomAppBar extends ConsumerWidget {
  const CustomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.sizeOf(context);

    return Container(
      height: 60,
      width: size.width,
      decoration: BoxDecoration(
        color: kPrimaryLight.withValues(alpha: 0.4),
        borderRadius: const BorderRadius.only(
          bottomRight: Radius.circular(10),
          bottomLeft: Radius.circular(10),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          spacing: 10,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            IctionIcon(
              image: Assets.svg.menu,
              onTap: () {
                // debugPrint('Navigating to settings: ${SGRoute.settings.route}');
                // context.push(SGRoute.settings.route);
              },
            ),
            const Spacer(),
            IctionIcon(
              image: Assets.svg.search,
              onTap: () {
                debugPrint('Navigating to search: ${SGRoute.search.route}');
                context.push(SGRoute.search.route);
              },
            ),
            IctionIcon(
                image: Assets.svg.settings,
                onTap: () {
                  debugPrint(
                      'Navigating to settings: ${SGRoute.settings.route}');
                  context.push(SGRoute.settings.route);
                }),
          ],
        ),
      ),
    );
  }
}

class IctionIcon extends ConsumerWidget {
  const IctionIcon({
    super.key,
    required this.image,
    this.onTap,
  });
  final String image;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          color: kPrimaryShadow.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: SvgIcon(
            image: image,
            height: 20,
            width: 20,
          ),
        ),
      ),
    );
  }
}

class DrImageHeader extends ConsumerWidget {
  const DrImageHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.sizeOf(context);

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        width: size.width * 0.75,
        height: 130,
        decoration: BoxDecoration(
          color: kPriLightColor,
          border: Border.all(color: kPrimaryLight.withValues(alpha: 0.5)),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
            bottomRight: Radius.circular(20),
            bottomLeft: Radius.circular(20),
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 6,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 14,
          children: <Widget>[
            Text(
              'فضيلـــة الشيـــخ أ.د',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            UniversalImage(
              path: Assets.img.drDiwanifont.path,
              fit: BoxFit.contain,
              width: size.width * 0.75,
            ),
          ],
        ),
      ),
    );
  }
}
