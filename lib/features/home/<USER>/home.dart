import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

import '../../../common/svg_icon.dart';
import '../../../common/universal_image.dart';
import '../../../constants/colors.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_items/media_navigation_helper.dart';
import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../data/models/tab_categories/tab_category_model.dart';
import '../../../data/models/tab_categories/tab_category_provider.dart';
import '../../../gen/assets.gen.dart';
import '../../../routing/app_router.dart';
import '../../../utils/exit_confirmation.dart';
import '../../tweets/presentation/widgets/tweet_card.dart';
import 'media_card_components.dart';

final GlobalKey<ScaffoldState> homeScreenScaffoldKey =
    GlobalKey<ScaffoldState>();

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  late TabController _mainTabController;

  @override
  void initState() {
    super.initState();

    _mainTabController = TabController(length: 1, vsync: this);
    _mainTabController.addListener(_handleTabChange);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateTabControllerLength();
    });
  }

  void _updateTabControllerLength() {
    final AsyncValue<List<MainTabCategory>> mainCategoriesAsync =
        ref.read(mainTabCategoriesProvider);
    mainCategoriesAsync.whenData((List<MainTabCategory> categories) {
      if (mounted &&
          _mainTabController.length != categories.length &&
          categories.isNotEmpty) {
        final int oldIndex = _mainTabController.index;
        _mainTabController.removeListener(_handleTabChange);
        _mainTabController.dispose();
        _mainTabController = TabController(
          length: categories.length,
          vsync: this,
          initialIndex: (oldIndex < categories.length) ? oldIndex : 0,
        );
        _mainTabController.addListener(_handleTabChange);

        final int selectedMainTab = ref.read(selectedMainTabProvider);
        if (_mainTabController.index != selectedMainTab) {
          _mainTabController.animateTo(selectedMainTab);
        }
        setState(() {});
      } else if (mounted &&
          categories.isEmpty &&
          _mainTabController.length != 1) {
        _mainTabController.removeListener(_handleTabChange);
        _mainTabController.dispose();
        _mainTabController = TabController(length: 1, vsync: this);
        _mainTabController.addListener(_handleTabChange);
        setState(() {});
      }
    });
  }

  void _handleTabChange() {
    if (mounted && !_mainTabController.indexIsChanging) {
      final int newIndex = _mainTabController.index;
      if (ref.read(selectedMainTabProvider) != newIndex) {
        ref.read(selectedMainTabProvider.notifier).setTab(newIndex);
        ref.read(selectedSecondaryTabProvider.notifier).setTab(0);
      }
    }
  }

  @override
  void dispose() {
    _mainTabController.removeListener(_handleTabChange);
    _mainTabController.dispose();
    super.dispose();
  }

  Future<bool> _showExitDialog(BuildContext context) async {
    return showExitConfirmation(context);
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<List<MainTabCategory>> mainCategoriesAsync =
        ref.watch(mainTabCategoriesProvider);
    final int selectedMainTab = ref.watch(selectedMainTabProvider);

    mainCategoriesAsync.whenData((List<MainTabCategory> categories) {
      if (mounted &&
          categories.isNotEmpty &&
          _mainTabController.length == categories.length) {
        if (_mainTabController.index != selectedMainTab) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && _mainTabController.index != selectedMainTab) {
              _mainTabController.animateTo(selectedMainTab);
            }
          });
        }
      }
    });

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          return;
        }
        final bool shouldPop = await _showExitDialog(context);
        if (shouldPop && mounted) {
          final BuildContext currentContext = context;
          if (currentContext.mounted && currentContext.canPop()) {
            currentContext.pop();
          } else if (mounted) {
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        key: homeScreenScaffoldKey,
        drawer: const _AppDrawer(),
        appBar: const PreferredSize(
          preferredSize: Size.fromHeight(50),
          child: CustomAppbar(),
        ),
        body: SafeArea(
          child: Stack(
            children: <Widget>[
              mainCategoriesAsync.when(
                data: (List<MainTabCategory> categories) {
                  if (categories.isEmpty) {
                    return const Center(child: Text('لا توجد فئات لعرضها'));
                  }

                  if (_mainTabController.length != categories.length) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _updateTabControllerLength();
                    });

                    return const Center(child: CircularProgressIndicator());
                  }
                  return CustomScrollView(
                    slivers: <Widget>[
                      const SliverToBoxAdapter(child: ShaikArabicFont()),
                      _MainCategoriesTabBar(
                          categories: categories,
                          tabController: _mainTabController),
                      _SecondaryCategoriesTabBar(
                          categories: categories,
                          selectedMainTabIndex: selectedMainTab),
                      _ContentArea(
                          categories: categories,
                          selectedMainTabIndex: selectedMainTab,
                          selectedSecondaryTabIndex:
                              ref.watch(selectedSecondaryTabProvider)),
                      const SliverToBoxAdapter(child: SizedBox(height: 100)),
                    ],
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (Object error, StackTrace stackTrace) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Icon(Icons.error_outline,
                          color: Theme.of(context).colorScheme.error, size: 48),
                      const SizedBox(height: 16),
                      Text('خطأ في تحميل الفئات: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => ref.refresh(mainTabCategoriesProvider),
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomAppbar extends ConsumerWidget {
  const CustomAppbar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 10),
      leading: Builder(
        builder: (BuildContext context) {
          return IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            tooltip: 'القائمة الجانبية',
          );
        },
      ),
      actions: <Widget>[
        _ActionIcon(
            iconPath: Assets.svg.settings,
            onTap: () => context.push(SGRoute.settings.route)),
      ],
    );
  }
}

class ShaikArabicFont extends ConsumerWidget {
  const ShaikArabicFont({
    super.key,
    this.showSearch = true,
  });

  final bool showSearch;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.sizeOf(context);
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: size.width * 0.1,
      ),
      child: Column(
        children: <Widget>[
          const Gap(10),
          UniversalImage(
            path: Assets.img.group58.path,
            fit: BoxFit.contain,
            height: 151,
            width: 338,
          ),
          if (showSearch) ...<Widget>[
            const Gap(10),
            GestureDetector(
              onTap: () => context.push(SGRoute.search.route),
              child: AbsorbPointer(
                child: CupertinoSearchTextField(
                  placeholder: 'البحث ...',
                  placeholderStyle: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(height: 1, color: klightBlackColor),
                  decoration: BoxDecoration(
                    color: kPriColor,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: kGrayColor),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _ActionIcon extends ConsumerWidget {
  const _ActionIcon({required this.iconPath, required this.onTap});
  final String iconPath;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4.0),
        padding: const EdgeInsets.all(8),
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          color: kPrimaryShadow.withAlpha(51),
          borderRadius: BorderRadius.circular(5),
        ),
        child: SvgIcon(image: iconPath, height: 20, width: 20),
      ),
    );
  }
}

class _MainCategoriesTabBar extends ConsumerWidget {
  const _MainCategoriesTabBar(
      {required this.categories, required this.tabController});
  final List<MainTabCategory> categories;
  final TabController tabController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: TabBar(
          controller: tabController,
          isScrollable: true,
          splashBorderRadius: BorderRadius.circular(6),
          indicatorSize: TabBarIndicatorSize.label,
          unselectedLabelColor: klightBlackColor,
          labelColor: kBlackColor,
          unselectedLabelStyle: Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(fontWeight: FontWeight.bold),
          labelStyle: Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(fontWeight: FontWeight.bold),
          padding: EdgeInsets.zero,
          tabAlignment: TabAlignment.start,
          tabs: categories
              .map((MainTabCategory category) => Tab(text: category.titleAr))
              .toList(),
        ),
      ),
    );
  }
}

class _SecondaryCategoriesTabBar extends ConsumerWidget {
  const _SecondaryCategoriesTabBar(
      {required this.categories, required this.selectedMainTabIndex});
  final List<MainTabCategory> categories;
  final int selectedMainTabIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (selectedMainTabIndex >= categories.length ||
        categories[selectedMainTabIndex].secondaryTabs.isEmpty) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    final List<SecondaryTabCategory> secondaryTabs =
        categories[selectedMainTabIndex].secondaryTabs;
    final int selectedSecondaryTab = ref.watch(selectedSecondaryTabProvider);

    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        decoration: const BoxDecoration(color: kPriLightColor),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: secondaryTabs.length,
          itemBuilder: (BuildContext context, int index) {
            final bool isSelected = index == selectedSecondaryTab;
            final SecondaryTabCategory tab = secondaryTabs[index];
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 12),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isSelected ? kSelectedColor : kPriColor,
                  borderRadius: const BorderRadius.all(Radius.circular(6)),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary.withAlpha(128)
                        : kGrayColor,
                  ),
                ),
                child: InkWell(
                  onTap: () => ref
                      .read(selectedSecondaryTabProvider.notifier)
                      .setTab(index),
                  child: Text(
                    tab.titleAr,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : klightBlackColor,
                          fontSize: 14,
                        ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class _ContentArea extends ConsumerWidget {
  const _ContentArea({
    required this.categories,
    required this.selectedMainTabIndex,
    required this.selectedSecondaryTabIndex,
  });
  final List<MainTabCategory> categories;
  final int selectedMainTabIndex;
  final int selectedSecondaryTabIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (selectedMainTabIndex >= categories.length ||
        categories[selectedMainTabIndex].secondaryTabs.isEmpty ||
        selectedSecondaryTabIndex >=
            categories[selectedMainTabIndex].secondaryTabs.length) {
      return const SliverToBoxAdapter(
        child: Center(
            child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('لا توجد بيانات لعرضها في هذا التبويب.'),
        )),
      );
    }

    final SecondaryTabCategory secondaryCategory =
        categories[selectedMainTabIndex]
            .secondaryTabs[selectedSecondaryTabIndex];
    final MediaType mediaType = secondaryCategory.mediaType;

    if (mediaType == MediaType.tweet) {
      return const _TweetContentList();
    } else {
      return _MediaContentList(
          mediaType: secondaryCategory.mediaType,
          category: MediaCategory.fromString(secondaryCategory.id));
    }
  }
}

class _MediaContentList extends ConsumerWidget {
  const _MediaContentList({required this.mediaType, required this.category});
  final MediaType mediaType;
  final MediaCategory category;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<MediaUiModel> items = ref.watch(categoryFilteredMediaProvider(
        mediaType: mediaType, category: category));

    if (items.isEmpty) {
      return SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(Icons.search_off,
                    size: 64, color: Theme.of(context).colorScheme.secondary),
                const SizedBox(height: 16),
                Text('لا توجد عناصر متاحة في $category',
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => ref.refresh(mediaListProvider),
                  child: const Text('تحديث'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          final MediaUiModel item = items[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              children: <Widget>[
                ItemsCard(item: item, mediaType: mediaType),
                if (index < items.length - 1) const Divider(),
              ],
            ),
          );
        },
        childCount: items.length,
      ),
    );
  }
}

class _TweetContentList extends ConsumerWidget {
  const _TweetContentList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<MediaUiModel> tweetItems =
        ref.watch(filteredMediaProvider).where((MediaUiModel item) {
      final MediaType mediaType = item.type;
      return mediaType == MediaType.tweet ||
          (item.tweetContent != null && item.tweetContent!.isNotEmpty);
    }).toList();

    if (tweetItems.isEmpty) {
      return SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(Icons.format_quote,
                    size: 64, color: Theme.of(context).colorScheme.secondary),
                const SizedBox(height: 16),
                const Text('لا توجد تغريدات متاحة',
                    style: TextStyle(fontSize: 18),
                    textAlign: TextAlign.center),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.push(SGRoute.tweets.route),
                  child: const Text('عرض جميع التغريدات'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          final MediaUiModel tweet = tweetItems[index];
          return TweetCard(
            tweet: tweet,
            onTap: () => context.pushNamed('tweetDetails',
                pathParameters: <String, String>{'id': tweet.id}),
          );
        },
        childCount: tweetItems.length,
      ),
    );
  }
}

class _AppDrawer extends StatelessWidget {
  const _AppDrawer();

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          DrawerHeader(
            decoration: BoxDecoration(color: Theme.of(context).primaryColor),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                CircleAvatar(
                    radius: 30,
                    backgroundImage: AssetImage(Assets.img.drImage.path)),
                const SizedBox(height: 10),
                const Text('فضيلة الشيخ أ.د',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                Text('الدكتور الفريح',
                    style: TextStyle(
                        color: Colors.white.withAlpha(204), fontSize: 14)),
              ],
            ),
          ),
          ListTile(
              leading: const Icon(Icons.home),
              title: const Text('الرئيسية'),
              onTap: () => Navigator.pop(context)),
          ListTile(
              leading: const Icon(Icons.search),
              title: const Text('البحث'),
              onTap: () {
                Navigator.pop(context);
                context.push(SGRoute.search.route);
              }),
          ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('الإعدادات'),
              onTap: () {
                Navigator.pop(context);
                context.push(SGRoute.settings.route);
              }),
          const Divider(),
          ListTile(
              leading: const Icon(Icons.info),
              title: const Text('حول التطبيق'),
              onTap: () {
                Navigator.pop(context);
                context.push(SGRoute.aboutUs.route);
              }),
          ListTile(
              leading: const Icon(Icons.contact_support),
              title: const Text('اتصل بنا'),
              onTap: () => Navigator.pop(context)),
        ],
      ),
    );
  }
}

class ItemsCard extends ConsumerWidget {
  const ItemsCard({
    super.key,
    required this.item,
    required this.mediaType,
  });
  final MediaUiModel item;
  final MediaType mediaType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaType itemActualType = item.type;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      child: InkWell(
        onTap: () => const MediaNavigationHelper()
            .navigateToMediaPlayer(context, item, ref: ref),
        child: _buildCardContent(context, itemActualType),
      ),
    );
  }

  Widget _buildCardContent(BuildContext context, MediaType type) {
    switch (type) {
      case MediaType.video:
        return VideoCardContent(item: item);
      case MediaType.audio:
        return AudioCardContent(item: item);
      case MediaType.pdf:
      case MediaType.document:
      case MediaType.text:
        return DocumentCardContent(item: item);
      case MediaType.html:
        return DefaultCardContent(item: item);
      case MediaType.tweet:
      case MediaType.unknown:
        return DefaultCardContent(item: item);
    }
  }
}
