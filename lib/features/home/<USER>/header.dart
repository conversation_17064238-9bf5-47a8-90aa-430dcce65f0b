import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class Header extends ConsumerWidget {
  const Header({super.key, required this.text});

  final String text;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(right: 15, left: 15, top: 24, bottom: 4),
      child: Text(
        text,
        textAlign: TextAlign.right,
        style:
            Theme.of(context).textTheme.titleLarge!.apply(fontWeightDelta: 2),
      ),
    );
  }
}
