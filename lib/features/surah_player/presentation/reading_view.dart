import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../data/models/media_player_state.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../utils/format_utils.dart'; // For formatting duration
import '../../media_player/application/media_player_controller.dart';

class ReadingView extends HookConsumerWidget {
  const ReadingView({
    super.key,
    required this.mediaId,
    this.scrollController,
  });

  final String mediaId;
  final ScrollController? scrollController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerController playerNotifier =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);
    final MediaPlayerState playerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final MediaUiModel? surah = playerState.mediaItem;

    if (surah == null) {
      return const Center(child: Text('لا توجد بيانات للسورة للقراءة'));
    }

    final bool isAudioForReadingViewPlaying =
        playerState.isPlaying && playerState.currentViewIndex == 1;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
              Text(
                surah.title,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              const SizedBox(width: 48), // Balance the layout
            ],
          ),
        ),
      ),
      body: Column(
        children: <Widget>[
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // if (surah.thumbnailUrl != null)
                  //   Center(
                  //     child: ClipRRect(
                  //       borderRadius: BorderRadius.circular(12.0),
                  //       child: CachedNetworkImage(
                  //         imageUrl: surah.thumbnailUrl!,
                  //         height: 150, // Adjust as needed
                  //         fit: BoxFit.contain,
                  //         placeholder: (BuildContext context, String url) =>
                  //             Container(
                  //           height: 150,
                  //           color: Colors.grey[200],
                  //           child: const Center(
                  //               child: CircularProgressIndicator()),
                  //         ),
                  //         errorWidget: (BuildContext context, String url,
                  //                 Object error) =>
                  //             Container(
                  //           height: 150,
                  //           color: Colors.grey[200],
                  //           child: const Icon(Icons.broken_image,
                  //               size: 50, color: Colors.grey),
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  // const Gap(16),
                  // Center(
                  //   child: Column(
                  //     children: <Widget>[
                  //       Text(surah.title,
                  //           style: Theme.of(context)
                  //               .textTheme
                  //               .headlineSmall
                  //               ?.copyWith(
                  //                 fontWeight: FontWeight.bold,
                  //               )),
                  //       const Gap(4),
                  //     ],
                  //   ),
                  // ),
                  // const Gap(10),
                  // Divider(color: Colors.grey.shade300),
                  // const Gap(10),
                  Text(surah.description ?? '',
                      textAlign: TextAlign.justify,
                      textDirection: TextDirection.rtl,
                      style:
                          Theme.of(context).textTheme.bodyMedium?.copyWith()),
                  const Gap(8),
                ],
              ),
            ),
          ),
          // Mini Player Controls for Reading View
          if (surah.audioUrl != null && surah.audioUrl!.isNotEmpty)
            Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest
                      .withValues(alpha: 0.95),
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  )),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Slider(
                    value: playerState.currentPosition.inMilliseconds
                        .toDouble()
                        .clamp(
                          0.0,
                          playerState.totalDuration.inMilliseconds.toDouble() >
                                  0
                              ? playerState.totalDuration.inMilliseconds
                                  .toDouble()
                              : 1.0,
                        ),
                    max: playerState.totalDuration.inMilliseconds.toDouble() > 0
                        ? playerState.totalDuration.inMilliseconds.toDouble()
                        : 1.0,
                    onChanged: (double value) {
                      playerNotifier
                          .seekTo(Duration(milliseconds: value.round()));
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    inactiveColor: Theme.of(context)
                        .colorScheme
                        .primary
                        .withAlpha((0.3 * 255).round()),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Text(formatDuration(playerState.currentPosition),
                            style: Theme.of(context).textTheme.bodySmall),
                        Text(formatDuration(playerState.totalDuration),
                            style: Theme.of(context).textTheme.bodySmall),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(Ionicons.play_back_outline),
                        iconSize: 22,
                        onPressed: playerNotifier.seekBackward,
                      ),
                      IconButton(
                        icon: Icon(isAudioForReadingViewPlaying
                            ? Ionicons.pause_sharp
                            : Ionicons.play_sharp),
                        iconSize: 32,
                        color: Theme.of(context).colorScheme.primary,
                        onPressed: isAudioForReadingViewPlaying
                            ? playerNotifier.pause
                            : playerNotifier.playMedia,
                      ),
                      IconButton(
                        icon: const Icon(Ionicons.play_forward_outline),
                        iconSize: 22,
                        onPressed: playerNotifier.seekForward,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
