import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';

class PlayerAppBar extends StatelessWidget implements PreferredSizeWidget {
  const PlayerAppBar({
    super.key,
    required this.title,
    required this.isFavorite,
    required this.onFavorite,
    required this.onShare,
    required this.onSettings,
  });
  final String title;
  final bool isFavorite;
  final VoidCallback onFavorite;
  final VoidCallback onShare;
  final VoidCallback onSettings;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Ionicons.arrow_back_outline),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500)),
      centerTitle: true,
      actions: <Widget>[
        IconButton(
          icon: Icon(isFavorite ? Ionicons.heart : Ionicons.heart_outline),
          color: isFavorite ? Colors.red : null,
          onPressed: onFavorite,
        ),
        IconButton(
          icon: const Icon(Ionicons.share_outline),
          onPressed: onShare,
        ),
        IconButton(
          icon: const Icon(Ionicons.settings_outline),
          onPressed: onSettings,
        ),
      ],
      elevation: 0.5,
      shadowColor: Colors.grey.withValues(alpha: 0.2),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
