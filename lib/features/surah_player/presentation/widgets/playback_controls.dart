import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../../data/models/media_player_state.dart';
import '../../../../utils/format_utils.dart';
import '../../../media_player/application/media_player_controller.dart';

class PlaybackControlsWidget extends ConsumerWidget {
  const PlaybackControlsWidget({
    super.key,
    required this.mediaId,
    this.isVideo,
  });

  final String mediaId;
  final bool? isVideo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerController playerNotifier =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);
    final MediaPlayerState playerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));

    final String currentPositionStr =
        formatDuration(playerState.currentPosition);
    final String totalDurationStr = formatDuration(playerState.totalDuration);

    return Column(
      children: <Widget>[
        // Slider
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 3.0,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 7.0),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 14.0),
            activeTrackColor: Theme.of(context).colorScheme.primary,
            inactiveTrackColor: Theme.of(context)
                .colorScheme
                .primary
                .withAlpha((0.3 * 255).round()),
            thumbColor: Theme.of(context).colorScheme.primary,
            overlayColor:
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: playerState.currentPosition.inMilliseconds.toDouble().clamp(
                  0.0,
                  playerState.totalDuration.inMilliseconds.toDouble() > 0
                      ? playerState.totalDuration.inMilliseconds.toDouble()
                      : 1.0, // Prevent division by zero if duration is 0
                ),
            max: playerState.totalDuration.inMilliseconds.toDouble() > 0
                ? playerState.totalDuration.inMilliseconds.toDouble()
                : 1.0, // Max should be at least a small positive value
            onChanged: playerState.isLoading
                ? null
                : (double value) {
                    playerNotifier
                        .seekTo(Duration(milliseconds: value.round()));
                  },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(currentPositionStr,
                  style: Theme.of(context).textTheme.bodySmall),
              Text(totalDurationStr,
                  style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ),
        const Gap(10),
        // Buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            IconButton(
              icon: const Icon(Ionicons.play_back_outline),
              iconSize: 28,
              onPressed:
                  playerState.isLoading ? null : playerNotifier.seekBackward,
              color: playerState.isLoading
                  ? Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3)
                  : Theme.of(context).colorScheme.onSurface,
            ),
            Container(
              decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.4),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    )
                  ]),
              child: playerState.isLoading
                  ? const SizedBox(
                      width: 56, // Match the IconButton size
                      height: 56,
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(playerState.isPlaying
                          ? Ionicons.pause_sharp
                          : Icons.play_arrow_rounded),
                      iconSize: 40,
                      color: Colors.white,
                      onPressed: playerState.isPlaying
                          ? playerNotifier.pause
                          : playerNotifier.playMedia,
                    ),
            ),
            IconButton(
              icon: const Icon(Ionicons.play_forward_outline),
              iconSize: 28,
              onPressed:
                  playerState.isLoading ? null : playerNotifier.seekForward,
              color: playerState.isLoading
                  ? Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3)
                  : Theme.of(context).colorScheme.onSurface,
            ),
            // IconButton(
            //   icon: Icon(
            //     playerState.loopMode == LoopMode.one
            //         ? Ionicons.repeat_outline
            //         : playerState.loopMode == LoopMode.all
            //             ? Ionicons.repeat_outline
            //             : Ionicons.repeat_outline,
            //   ),
            //   iconSize: 24,
            //   onPressed: playerNotifier.toggleLoopMode,
            //   color: playerState.loopMode != LoopMode.off
            //       ? Theme.of(context).colorScheme.primary
            //       : Theme.of(context).colorScheme.onSurfaceVariant,
            // ),
          ],
        ),
      ],
    );
  }
}
