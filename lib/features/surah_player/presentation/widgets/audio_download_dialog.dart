import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:open_file/open_file.dart';

import '../../../../data/enums/opentions_format_enum.dart';
import '../../../../data/models/media_item.dart';
import '../../../../data/models/media_player_state.dart';
import '../../../../data/models/media_ui_model.dart';
import '../../../../utils/error_popup.dart';
import '../../../media_player/application/media_player_controller.dart';
import '../../../media_player/application/services/service_providers.dart';

class AudioDownloadDialog extends ConsumerWidget {
  // Changed to ConsumerWidget as initState logic moved to ref.listen
  const AudioDownloadDialog({
    super.key,
    required this.mediaId,
  });

  final String mediaId;

  // Helper method to show a generic success popup (can be customized further)
  void _showSuccessPopup(BuildContext context, String message,
      {IconData icon = Icons.check_circle_outline,
      Color backgroundColor = Colors.green}) {
    ErrorPopup.show(
      context: context,
      message: message,
      icon: icon,
      backgroundColor: backgroundColor,
      textColor: Colors.white,
      iconColor: Colors.white,
    );
  }

  // Helper method to show a success popup with actions (Open File, Open Folder)
  void _showSuccessPopupWithActions(
      BuildContext context, WidgetRef ref, String message, String filePath) {
    // Auto-open the file after a short delay
    final Timer autoOpenTimer = Timer(const Duration(seconds: 2), () {
      if (context.mounted) {
        // Check if dialog (or its parent context) is still active
        _openFile(context, filePath);
      }
    });

    showDialog(
      context: context,
      barrierDismissible:
          false, // User must explicitly choose an action or close
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: <Widget>[
              Icon(Icons.check_circle_outline, color: Colors.green, size: 28),
              SizedBox(width: 12),
              Expanded(
                  child: Text('تم بنجاح!',
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green))),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(message, style: const TextStyle(fontSize: 16)),
              const SizedBox(height: 16),
              Container(
                // Info box for auto-open
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8)),
                child: const Row(children: <Widget>[
                  Icon(Icons.info_outline, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                      child: Text('سيتم فتح الملف تلقائياً خلال ثوانٍ قليلة',
                          style: TextStyle(fontSize: 12, color: Colors.blue))),
                ]),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                autoOpenTimer.cancel(); // Cancel auto-open if user interacts
                Navigator.of(dialogContext).pop();
              },
              child: const Text('إغلاق'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                autoOpenTimer.cancel();
                Navigator.of(dialogContext).pop();
                _openFile(context,
                    filePath); // Pass the original context for ScaffoldMessenger
              },
              icon: const Icon(Icons.open_in_new),
              label: const Text('فتح الملف الآن'),
              style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue, foregroundColor: Colors.white),
            ),
            ElevatedButton.icon(
              onPressed: () {
                autoOpenTimer.cancel();
                Navigator.of(dialogContext).pop();
                _openDownloadFolder(context, ref);
              },
              icon: const Icon(Icons.folder_open),
              label: const Text('فتح المجلد'),
              style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green, foregroundColor: Colors.white),
            ),
          ],
        );
      },
    ).then((_) => autoOpenTimer
        .cancel()); // Ensure timer is cancelled if dialog is dismissed by other means
  }

  Future<void> _openFile(BuildContext context, String filePath) async {
    try {
      final OpenResult result = await OpenFile.open(filePath);
      if (!context.mounted) {
        return;
      }

      if (result.type == ResultType.done) {
        _showSuccessPopup(context, 'تم فتح الملف بنجاح');
      } else {
        ErrorPopup.show(
            context: context,
            message:
                'لم يتم العثور على تطبيق لفتح هذا الملف. ${result.message}',
            iconColor: Colors.white,
            backgroundColor: Colors.orange,
            textColor: Colors.white);
      }
    } catch (e) {
      if (context.mounted)
        ErrorPopup.show(context: context, message: 'خطأ في فتح الملف: $e');
    }
  }

  Future<void> _openDownloadFolder(BuildContext context, WidgetRef ref) async {
    try {
      final String downloadsPath =
          await ref.read(mediaDownloadServiceProvider).getDownloadsPath();
      debugPrint('Attempting to open download folder: $downloadsPath');

      // OpenFile.open can often open directories on supported platforms.
      final OpenResult result = await OpenFile.open(downloadsPath);

      if (!context.mounted) {
        return;
      }

      if (result.type == ResultType.done) {
        _showSuccessPopup(context, 'تم فتح مجلد التحميل.',
            icon: Ionicons.folder_open_outline);
      } else {
        // Fallback or specific info if OpenFile fails for a directory
        debugPrint(
            'OpenFile failed for directory: ${result.message}. Showing path info.');
        _showDownloadLocationInfo(context, downloadsPath);
      }
    } catch (e) {
      debugPrint('Error opening folder: $e');
      if (context.mounted) {
        _showDownloadLocationInfo(context, null); // Show generic message
      }
    }
  }

  void _showDownloadLocationInfo(BuildContext context, String? path) {
    final String message = path != null
        ? 'تم حفظ الملفات في: \n$path\nيمكنك الوصول إليها من خلال تطبيق إدارة الملفات.'
        : 'تم حفظ الملفات في مجلد التحميل. يمكنك الوصول إليها من خلال تطبيق إدارة الملفات.';
    ErrorPopup.show(
      context: context,
      message: message,
      icon: Icons.info_outline,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
      iconColor: Colors.white,
      duration: const Duration(seconds: 7), // Longer duration for reading path
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerController playerNotifier =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);

    // Use ref.listen for side effects (showing dialogs/snackbars)
    ref.listen<MediaPlayerState>(mediaPlayerControllerProvider(mediaId),
        (MediaPlayerState? previous, MediaPlayerState current) {
      final bool wasDownloading = previous?.isDownloading ?? false;
      final bool isDownloading = current.isDownloading ?? false;
      final double currentProgress = current.downloadProgress ?? 0.0;

      // Handle successful download completion
      if (wasDownloading &&
          !isDownloading &&
          currentProgress >= 1.0 &&
          current.downloadedFilePath != null &&
          (current.errorMessage == null ||
              current.errorMessage!.contains('Download completed'))) {
        if (context.mounted) {
          _showSuccessPopupWithActions(context, ref, 'تم تحميل الملف بنجاح!',
              current.downloadedFilePath!);
          playerNotifier
              .clearErrorMessage(); // Clear message if it was a success one
        }
      }
      // Handle "file already downloaded" specifically
      else if (current.errorMessage != null &&
          current.errorMessage!.contains('File already downloaded') &&
          current.errorMessage != previous?.errorMessage) {
        if (context.mounted) {
          final String? filePath = current.downloadedFilePath ??
              current.errorMessage?.split('File already downloaded: ').last;

          if (filePath != null &&
              filePath.isNotEmpty &&
              !filePath.contains('File already downloaded')) {
            _showSuccessPopupWithActions(
                context, ref, 'الملف موجود بالفعل', filePath);
          } else {
            _showSuccessPopup(context, 'الملف موجود بالفعل في مجلد التحميل');
          }
          playerNotifier.clearErrorMessage();
        }
      }
      // Handle other errors (that are not success or already downloaded)
      else if (current.errorMessage != null &&
          current.errorMessage !=
              previous?.errorMessage && // Only if message is new
          !current.errorMessage!.contains('Download completed successfully') &&
          !current.errorMessage!.contains('File already downloaded')) {
        if (context.mounted) {
          ErrorPopup.show(context: context, message: current.errorMessage!);
          playerNotifier.clearErrorMessage();
        }
      }
    });

    final MediaPlayerState playerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final MediaUiModel? surah = playerState.mediaItem;

    if (surah == null) {
      return AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: <Widget>[
            Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ'),
          ],
        ),
        content: const Text('لا يمكن تحميل الملف الصوتي'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      );
    }

    final List<MediaOption> mediaOptions =
        playerState.mediaOptions ?? <MediaOption>[];
    final bool isDownloading = playerState.isDownloading ?? false;
    final double downloadProgress = playerState.downloadProgress ?? 0.0;

    // Determine title based on current state
    String dialogTitle = 'تحميل الملفات';
    if (isDownloading) {
      dialogTitle = 'جاري التحميل...';
    } else if (playerState.downloadedFilePath != null &&
        downloadProgress >= 1.0 &&
        (playerState.errorMessage == null ||
            playerState.errorMessage!.contains('Download completed'))) {
      dialogTitle = 'اكتمل التحميل';
    } else if (playerState.errorMessage != null &&
        playerState.errorMessage!.contains('File already downloaded')) {
      dialogTitle = 'الملف موجود';
    }

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: <Widget>[
          Icon(
              isDownloading
                  ? Icons.hourglass_empty_rounded
                  : (playerState.downloadedFilePath != null &&
                          downloadProgress >= 1.0)
                      ? Ionicons.checkmark_circle_outline
                      : Ionicons.download_outline,
              color: Theme.of(context).primaryColor,
              size: 24),
          const SizedBox(width: 12),
          Expanded(
              child: Text(dialogTitle,
                  style: const TextStyle(
                      fontSize: 20, fontWeight: FontWeight.bold))),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Ionicons.close_circle),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              if (isDownloading) ...<Widget>[
                Container(
                  /* ... your progress bar UI ... */
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: <Widget>[
                      Row(children: <Widget>[
                        SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                                strokeWidth: 2,
                                value:
                                    downloadProgress > 0 && downloadProgress < 1
                                        ? downloadProgress
                                        : null)),
                        const SizedBox(width: 12),
                        Expanded(
                            child: Text(
                                'جاري التحميل... ${(downloadProgress * 100).toInt()}%',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500))),
                      ]),
                      const SizedBox(height: 12),
                      LinearProgressIndicator(
                          value: (playerState.downloadProgress == null ||
                                  (playerState.downloadProgress == 0.0 &&
                                      isDownloading))
                              ? null
                              : downloadProgress,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor)),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ] else if (playerState.downloadedFilePath != null &&
                  (playerState.errorMessage == null ||
                      playerState.errorMessage!
                          .contains('Download completed') ||
                      playerState.errorMessage!
                          .contains('File already downloaded'))) ...<Widget>[
                // Content for "Download Complete" or "Already Downloaded"
                // This is handled by the popups now, so this section might show general info or just options.
                // For this example, we'll keep the download options available.
              ],

              // Download options always visible if not actively downloading
              if (!isDownloading) ...<Widget>[
                const Text('اختر الملف الذي تريد تحميله:',
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                const SizedBox(height: 16),
                if (surah.audioUrl != null &&
                    surah.audioUrl!.isNotEmpty) // Check if URL is not empty
                  _buildDownloadOption(
                    context: context,
                    icon: Ionicons.musical_note_outline,
                    title: 'الملف الصوتي الرئيسي',
                    subtitle: surah.fileName ?? surah.audioUrl!.split('/').last,
                    fileSize: surah.fileSize,
                    isDownloading:
                        isDownloading, // Redundant if already checked above, but for clarity
                    onTap: () => playerNotifier
                        .downloadMedia(), // Downloads primary audio
                  ),
                ...mediaOptions
                    .where((MediaOption option) =>
                        option.url != null &&
                        option
                            .url!.isNotEmpty) // Filter out options without URLs
                    .map((MediaOption option) => _buildDownloadOption(
                          context: context,
                          icon: _getIconForFormat(option.format),
                          title: _getTitleForFormat(option.format),
                          subtitle: option.fileName ??
                              _getDefaultFileName(option.format),
                          fileSize: option.fileSize,
                          isDownloading: isDownloading,
                          onTap: () => playerNotifier.downloadMedia(
                            specificUrl: option.url,
                            fileType: option.format.fileExtension,
                          ),
                        )),
              ],
            ],
          ),
        ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      actions: const <Widget>[
        // if (playerState.downloadedFilePath != null && !isDownloading)
        //   ElevatedButton.icon(
        //     onPressed: () =>
        //         _openFile(context, playerState.downloadedFilePath!),
        //     icon: const Icon(Icons.open_in_new),
        //     label: const Text('فتح آخر ملف تم تحميله'),
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: Colors.blue,
        //       foregroundColor: Colors.white,
        //     ),
        //   ),
        // if (playerState.downloadedFilePath != null && !isDownloading)
        //   const SizedBox(width: 8), // Spacer

        // ElevatedButton.icon(
        //   onPressed: () => _openDownloadFolder(context, ref),
        //   icon: const Icon(Icons.folder_open),
        //   label: const Text('فتح مجلد التحميل'),
        //   style: ElevatedButton.styleFrom(
        //     backgroundColor: Theme.of(context).primaryColor,
        //     foregroundColor: Colors.white,
        //   ),
        // ),
      ],
    );
  }

  // _buildDownloadOption, _getIconForFormat, etc. from your previous code
  // Ensure OptionFormat has a getter like `fileExtension`
  Widget _buildDownloadOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    int? fileSize, // Changed from String?
    required bool isDownloading,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .surfaceContainerHighest
            .withValues(alpha: 0.5),
        // border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Theme.of(context).primaryColor, size: 15),
        ),

        title: Text(title, style: Theme.of(context).textTheme.bodySmall),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
            if (fileSize != null)
              Padding(
                padding: const EdgeInsets.only(top: 2.0),
                child: Text(
                  'الحجم: ${_formatFileSize(fileSize)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
          ],
        ),
        trailing: isDownloading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 2.5))
            : Icon(Ionicons.download_outline,
                color: Theme.of(context).primaryColor, size: 20),
        onTap: isDownloading ? null : onTap, // Disable tap if downloading
        enabled: !isDownloading,
      ),
    );
  }

  IconData _getIconForFormat(OptionFormat format) {
    switch (format) {
      case OptionFormat.pdf:
        return Ionicons.document_text_outline;
      case OptionFormat.docx:
        return Ionicons.document_outline;
      case OptionFormat.audioMpeg:
        return Ionicons.musical_notes_outline; // Changed icon
      case OptionFormat.videoMp4:
      case OptionFormat.videoYoutube:
        return Ionicons.videocam_outline;
      case OptionFormat.imageJpeg:
        return Ionicons.image_outline;
      case OptionFormat.textPlain:
      case OptionFormat.html:
      case OptionFormat.article:
        return Ionicons.reader_outline; // Changed icon
      case OptionFormat.tweet:
        return Ionicons.logo_twitter;
      case OptionFormat.unknown:
        return Ionicons.document_attach_outline; // Changed icon
    }
  }

  String _getTitleForFormat(OptionFormat format) {
    // ... (your existing implementation is fine) ...
    switch (format) {
      case OptionFormat.pdf:
        return 'ملف PDF';
      case OptionFormat.docx:
        return 'ملف DOCX';
      case OptionFormat.audioMpeg:
        return 'ملف صوتي إضافي';
      case OptionFormat.videoMp4:
      case OptionFormat.videoYoutube:
        return 'ملف فيديو';
      case OptionFormat.imageJpeg:
        return 'صورة';
      case OptionFormat.textPlain:
        return 'ملف نصي';
      case OptionFormat.html:
        return 'صفحة ويب';
      case OptionFormat.article:
        return 'مقال';
      case OptionFormat.tweet:
        return 'تغريدة';
      case OptionFormat.unknown:
        return 'ملف متنوع';
    }
  }

  String _getDefaultFileName(OptionFormat format) {
    // ... (your existing implementation is fine, consider adding more specific defaults) ...
    switch (format) {
      case OptionFormat.pdf:
        return 'document.pdf';
      case OptionFormat.docx:
        return 'document.docx';
      case OptionFormat.audioMpeg:
        return 'audio_extra.mp3';
      case OptionFormat.videoMp4:
        return 'video.mp4';
      case OptionFormat.videoYoutube:
        return 'youtube_link.txt';
      case OptionFormat.imageJpeg:
        return 'image.jpg';
      case OptionFormat.textPlain:
        return 'text.txt';
      case OptionFormat.html:
        return 'page.html';
      case OptionFormat.article:
        return 'article.txt';
      case OptionFormat.tweet:
        return 'tweet_content.json';
      case OptionFormat.unknown:
        return 'downloaded_file';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes <= 0) {
      return '0 B';
    }
    const List<String> suffixes = <String>[
      'B',
      'KB',
      'MB',
      'GB',
      'TB',
      'PB',
      'EB',
      'ZB',
      'YB'
    ];
    // Calculate the index 'i' using log base 1024
    // log(bytes) / log(1024) will give the power to which 1024 must be raised to get bytes
    final int i = (log(bytes) / log(1024)).floor();
    // Ensure 'i' is within the bounds of the suffixes array
    final int index = i.clamp(0, suffixes.length - 1);
    // Calculate the value by dividing bytes by 1024 raised to the power of 'index'
    final double value = bytes / pow(1024, index); // Use pow from dart:math
    return '${value.toStringAsFixed(1)} ${suffixes[index]}';
  }
}

// Function to show the dialog (keep as is)
void showAudioDownloadDialog(BuildContext context, String mediaId) {
  showDialog(
    context: context,
    barrierDismissible: false, // Good, user must interact
    builder: (BuildContext dialogContext) => AudioDownloadDialog(
      // Use dialogContext
      mediaId: mediaId,
    ),
  );
}

// Ensure OptionFormat has fileExtension getter (add this to your opentions_format_enum.dart)
// extension OptionFormatExtension on OptionFormat {
//   String get fileExtension {
//     switch (this) {
//       case OptionFormat.pdf: return 'pdf';
//       case OptionFormat.docx: return 'docx';
//       case OptionFormat.audioMpeg: return 'mp3';
//       case OptionFormat.videoMp4: return 'mp4';
//       // ... add others
//       default: return 'bin';
//     }
//   }
// }
// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:ionicons/ionicons.dart';
// import 'package:open_file/open_file.dart';

// import '../../../../data/enums/opentions_format_enum.dart';
// import '../../../../data/models/media_item.dart';
// import '../../../../data/models/media_player_state.dart';
// import '../../../../data/models/media_ui_model.dart';
// import '../../../../utils/error_popup.dart';
// import '../../../media_player/application/media_player_controller.dart';
// import '../../../media_player/application/services/service_providers.dart';

// class AudioDownloadDialog extends ConsumerStatefulWidget {
//   const AudioDownloadDialog({
//     super.key,
//     required this.mediaId,
//   });

//   final String mediaId;

//   @override
//   ConsumerState<AudioDownloadDialog> createState() =>
//       _AudioDownloadDialogState();
// }

// class _AudioDownloadDialogState extends ConsumerState<AudioDownloadDialog> {
//   String? _lastErrorMessage;
//   String? _lastSuccessMessage;

//   @override
//   Widget build(BuildContext context) {
//     final MediaPlayerController playerNotifier =
//         ref.read(mediaPlayerControllerProvider(widget.mediaId).notifier);
//     final MediaPlayerState playerState =
//         ref.watch(mediaPlayerControllerProvider(widget.mediaId));

//     final MediaUiModel? surah = playerState.mediaItem;

//     if (surah == null) {
//       return AlertDialog(
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//         title: const Row(
//           children: <Widget>[
//             Icon(Icons.error_outline, color: Colors.red),
//             SizedBox(width: 8),
//             Text('خطأ'),
//           ],
//         ),
//         content: const Text('لا يمكن تحميل الملف الصوتي'),
//         actions: <Widget>[
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إغلاق'),
//           ),
//         ],
//       );
//     }

//     // Get media options from the player state
//     final List<MediaOption> mediaOptions =
//         playerState.mediaOptions ?? <MediaOption>[];

//     // Check if currently downloading
//     final bool isDownloading = playerState.isDownloading ?? false;
//     final double downloadProgress = playerState.downloadProgress ?? 0.0;

//     // Handle download completion success
//     if (!isDownloading &&
//         downloadProgress >= 1.0 &&
//         playerState.downloadedFilePath != null &&
//         _lastSuccessMessage != playerState.downloadedFilePath) {
//       _lastSuccessMessage = playerState.downloadedFilePath;

//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         if (mounted) {
//           _showSuccessPopupWithActions(
//             context,
//             'تم تحميل الملف بنجاح!',
//             playerState.downloadedFilePath!,
//           );
//         }
//       });
//     }

//     // Handle "file already downloaded" case
//     if (playerState.errorMessage != null &&
//         playerState.errorMessage!.contains('File already downloaded') &&
//         playerState.errorMessage != _lastErrorMessage) {
//       _lastErrorMessage = playerState.errorMessage;

//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         if (mounted) {
//           // Extract file path from error message if available
//           final String? filePath = playerState.downloadedFilePath;
//           if (filePath != null) {
//             _showSuccessPopupWithActions(
//               context,
//               'الملف موجود بالفعل في مجلد التحميل',
//               filePath,
//             );
//           } else {
//             _showSuccessPopup(context, 'الملف موجود بالفعل في مجلد التحميل');
//           }
//           playerNotifier.clearErrorMessage();
//         }
//       });
//     }

//     // Handle actual error messages
//     else if (playerState.errorMessage != null &&
//         playerState.errorMessage != _lastErrorMessage &&
//         !playerState.errorMessage!.contains('File already downloaded') &&
//         !playerState.errorMessage!
//             .contains('Download completed successfully')) {
//       _lastErrorMessage = playerState.errorMessage;

//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         if (mounted && playerState.errorMessage != null) {
//           ErrorPopup.show(
//             context: context,
//             message: playerState.errorMessage!,
//           );
//           playerNotifier.clearErrorMessage();
//         }
//       });
//     }

//     return AlertDialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       title: Row(
//         children: <Widget>[
//           Icon(
//             Ionicons.download_outline,
//             color: Theme.of(context).primaryColor,
//             size: 24,
//           ),
//           const SizedBox(width: 12),
//           const Expanded(
//             child: Text(
//               'تحميل الملفات',
//               style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//             ),
//           ),
//         ],
//       ),
//       content: SizedBox(
//         width: double.maxFinite,
//         child: SingleChildScrollView(
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: <Widget>[
//               // Show download progress if downloading
//               if (isDownloading) ...<Widget>[
//                 Container(
//                   padding: const EdgeInsets.all(16),
//                   decoration: BoxDecoration(
//                     color:
//                         Theme.of(context).primaryColor.withValues(alpha: 0.1),
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                   child: Column(
//                     children: <Widget>[
//                       Row(
//                         children: <Widget>[
//                           SizedBox(
//                             width: 20,
//                             height: 20,
//                             child: CircularProgressIndicator(
//                               strokeWidth: 2,
//                               value: downloadProgress,
//                             ),
//                           ),
//                           const SizedBox(width: 12),
//                           Expanded(
//                             child: Text(
//                               'جاري التحميل... ${(downloadProgress * 100).toInt()}%',
//                               style:
//                                   const TextStyle(fontWeight: FontWeight.w500),
//                             ),
//                           ),
//                         ],
//                       ),
//                       const SizedBox(height: 12),
//                       LinearProgressIndicator(
//                         value: downloadProgress,
//                         backgroundColor: Colors.grey.shade300,
//                         valueColor: AlwaysStoppedAnimation<Color>(
//                           Theme.of(context).primaryColor,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const SizedBox(height: 16),
//               ],

//               // Download options
//               const Text(
//                 'اختر الملف الذي تريد تحميله:',
//                 style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
//               ),
//               const SizedBox(height: 16),

//               // Audio file option
//               if (surah.audioUrl != null)
//                 _buildDownloadOption(
//                   context: context,
//                   icon: Ionicons.musical_note_outline,
//                   title: 'الملف الصوتي',
//                   subtitle: surah.fileName ?? 'audio.mp3',
//                   fileSize: surah.fileSize?.toString(),
//                   isDownloading: isDownloading,
//                   onTap: isDownloading
//                       ? null
//                       : () => playerNotifier.downloadMedia(),
//                 ),

//               // Media options (PDF, DOCX, etc.)
//               ...mediaOptions.map((MediaOption option) => _buildDownloadOption(
//                     context: context,
//                     icon: _getIconForFormat(option.format),
//                     title: _getTitleForFormat(option.format),
//                     subtitle:
//                         option.fileName ?? _getDefaultFileName(option.format),
//                     fileSize: option.fileSize?.toString(),
//                     isDownloading: isDownloading,
//                     onTap: isDownloading
//                         ? null
//                         : () => playerNotifier.downloadMedia(
//                               specificUrl: option.url,
//                               fileType: option.fileName ??
//                                   _getDefaultFileName(option.format),
//                             ),
//                   )),
//             ],
//           ),
//         ),
//       ),
//       actions: <Widget>[
//         TextButton(
//           onPressed: () => Navigator.of(context).pop(),
//           child: const Text('إغلاق'),
//         ),
//         if (playerState.downloadedFilePath != null && !isDownloading)
//           ElevatedButton.icon(
//             onPressed: () => _openFile(playerState.downloadedFilePath!),
//             icon: const Icon(Icons.open_in_new),
//             label: const Text('فتح آخر ملف'),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.blue,
//               foregroundColor: Colors.white,
//             ),
//           ),
//         ElevatedButton.icon(
//           onPressed: _openDownloadFolder,
//           icon: const Icon(Icons.folder_open),
//           label: const Text('فتح مجلد التحميل'),
//           style: ElevatedButton.styleFrom(
//             backgroundColor: Theme.of(context).primaryColor,
//             foregroundColor: Colors.white,
//           ),
//         ),
//       ],
//     );
//   }

//   void _showSuccessPopup(BuildContext context, String message) {
//     ErrorPopup.show(
//       context: context,
//       message: message,
//       icon: Icons.check_circle_outline,
//       backgroundColor: Colors.green,
//       textColor: Colors.white,
//       iconColor: Colors.white,
//     );
//   }

//   void _showSuccessPopupWithActions(
//       BuildContext context, String message, String filePath) {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (BuildContext context) => AlertDialog(
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//         title: const Row(
//           children: <Widget>[
//             Icon(
//               Icons.check_circle_outline,
//               color: Colors.green,
//               size: 28,
//             ),
//             SizedBox(width: 12),
//             Expanded(
//               child: Text(
//                 'تم بنجاح!',
//                 style: TextStyle(
//                   fontSize: 18,
//                   fontWeight: FontWeight.bold,
//                   color: Colors.green,
//                 ),
//               ),
//             ),
//           ],
//         ),
//         content: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: <Widget>[
//             Text(
//               message,
//               style: const TextStyle(fontSize: 16),
//             ),
//             const SizedBox(height: 16),
//             Container(
//               padding: const EdgeInsets.all(12),
//               decoration: BoxDecoration(
//                 color: Colors.blue.withValues(alpha: 0.1),
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               child: const Row(
//                 children: <Widget>[
//                   Icon(
//                     Icons.info_outline,
//                     color: Colors.blue,
//                     size: 20,
//                   ),
//                   SizedBox(width: 8),
//                   Expanded(
//                     child: Text(
//                       'سيتم فتح الملف تلقائياً خلال 3 ثوانٍ',
//                       style: TextStyle(
//                         fontSize: 12,
//                         color: Colors.blue,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 12),
//             const Text(
//               'أو اختر إجراءً:',
//               style: TextStyle(
//                 fontSize: 14,
//                 fontWeight: FontWeight.w500,
//                 color: Colors.grey,
//               ),
//             ),
//           ],
//         ),
//         actions: <Widget>[
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إغلاق'),
//           ),
//           ElevatedButton.icon(
//             onPressed: () {
//               Navigator.of(context).pop();
//               _openFile(filePath);
//             },
//             icon: const Icon(Icons.open_in_new),
//             label: const Text('فتح الآن'),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.blue,
//               foregroundColor: Colors.white,
//             ),
//           ),
//           ElevatedButton.icon(
//             onPressed: () {
//               Navigator.of(context).pop();
//               _openDownloadFolder();
//             },
//             icon: const Icon(Icons.folder_open),
//             label: const Text('فتح المجلد'),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: Colors.green,
//               foregroundColor: Colors.white,
//             ),
//           ),
//         ],
//       ),
//     );

//     // Auto-open the file after 3 seconds
//     final NavigatorState navigator = Navigator.of(context);
//     Future<void>.delayed(const Duration(seconds: 3), () {
//       if (mounted && navigator.canPop()) {
//         navigator.pop();
//         _openFile(filePath);
//       }
//     });
//   }

//   Future<void> _openFile(String filePath) async {
//     try {
//       final OpenResult result = await OpenFile.open(filePath);

//       if (result.type == ResultType.done) {
//         // File opened successfully
//         if (mounted) {
//           ErrorPopup.show(
//             context: context,
//             message: 'تم فتح الملف بنجاح',
//             icon: Icons.check_circle_outline,
//             backgroundColor: Colors.green,
//             textColor: Colors.white,
//             iconColor: Colors.white,
//             duration: const Duration(seconds: 2),
//           );
//         }
//       } else {
//         // Failed to open file
//         if (mounted) {
//           ErrorPopup.show(
//             context: context,
//             message: 'لا يمكن فتح الملف. ${result.message}',
//             backgroundColor: Colors.orange,
//             textColor: Colors.white,
//             iconColor: Colors.white,
//           );
//         }
//       }
//     } catch (e) {
//       if (mounted) {
//         ErrorPopup.show(
//           context: context,
//           message: 'خطأ في فتح الملف: $e',
//         );
//       }
//     }
//   }

//   Future<void> _openDownloadFolder() async {
//     try {
//       if (Platform.isAndroid) {
//         // For Android, use a specific intent to open the Downloads folder
//         await _openAndroidDownloadsFolder();
//       } else if (Platform.isIOS) {
//         // For iOS, show the Files app
//         await _openIOSFilesApp();
//       } else {
//         // For other platforms, try the direct path approach
//         final String downloadsPath =
//             await ref.read(mediaDownloadServiceProvider).getDownloadsPath();
//         await OpenFile.open(downloadsPath);
//       }
//     } catch (e) {
//       if (mounted) {
//         _showDownloadLocationInfo(null);
//       }
//     }
//   }

//   Future<void> _openAndroidDownloadsFolder() async {
//     try {
//       // Try different Android approaches to open the Downloads folder

//       // Method 1: Try to open Downloads folder with file manager intent
//       try {
//         await ref.read(interactionServiceProvider).openInBrowser(
//             'content://com.android.externalstorage.documents/document/primary%3ADownload');

//         if (mounted) {
//           ErrorPopup.show(
//             context: context,
//             message: 'تم فتح مجلد التحميل',
//             icon: Icons.check_circle_outline,
//             backgroundColor: Colors.green,
//             textColor: Colors.white,
//             iconColor: Colors.white,
//             duration: const Duration(seconds: 2),
//           );
//         }
//         return;
//       } catch (e) {
//         // Continue to next method
//       }

//       // Method 2: Try to open with file manager using Downloads URI
//       try {
//         await ref
//             .read(interactionServiceProvider)
//             .openInBrowser('content://downloads/my_downloads');

//         if (mounted) {
//           ErrorPopup.show(
//             context: context,
//             message: 'تم فتح مجلد التحميل',
//             icon: Icons.check_circle_outline,
//             backgroundColor: Colors.green,
//             textColor: Colors.white,
//             iconColor: Colors.white,
//             duration: const Duration(seconds: 2),
//           );
//         }
//         return;
//       } catch (e) {
//         // Continue to next method
//       }

//       // Method 3: Try to open generic file manager
//       try {
//         await ref.read(interactionServiceProvider).openInBrowser(
//             'content://com.android.documentsui/.DocumentsActivity');

//         if (mounted) {
//           ErrorPopup.show(
//             context: context,
//             message: 'تم فتح مدير الملفات',
//             icon: Icons.check_circle_outline,
//             backgroundColor: Colors.blue,
//             textColor: Colors.white,
//             iconColor: Colors.white,
//             duration: const Duration(seconds: 2),
//           );
//         }
//         return;
//       } catch (e) {
//         // All methods failed, show helpful info
//         if (mounted) {
//           _showDownloadLocationInfo(null);
//         }
//       }
//     } catch (e) {
//       if (mounted) {
//         _showDownloadLocationInfo(null);
//       }
//     }
//   }

//   Future<void> _openIOSFilesApp() async {
//     try {
//       // For iOS, try to open the Files app
//       await ref
//           .read(interactionServiceProvider)
//           .openInBrowser('shareddocuments://');

//       if (mounted) {
//         ErrorPopup.show(
//           context: context,
//           message: 'تم فتح تطبيق الملفات',
//           icon: Icons.check_circle_outline,
//           backgroundColor: Colors.green,
//           textColor: Colors.white,
//           iconColor: Colors.white,
//           duration: const Duration(seconds: 2),
//         );
//       }
//     } catch (e) {
//       if (mounted) {
//         _showDownloadLocationInfo(null);
//       }
//     }
//   }

//   void _showDownloadLocationInfo(String? path) {
//     final String message = path != null
//         ? 'تم حفظ الملفات في: $path\nيمكنك الوصول إليها من خلال تطبيق إدارة الملفات.'
//         : 'تم حفظ الملفات في مجلد التحميل. يمكنك الوصول إليها من خلال تطبيق إدارة الملفات.';

//     ErrorPopup.show(
//       context: context,
//       message: message,
//       icon: Icons.info_outline,
//       backgroundColor: Colors.blue,
//       textColor: Colors.white,
//       iconColor: Colors.white,
//       duration: const Duration(seconds: 5),
//     );
//   }

//   Widget _buildDownloadOption({
//     required BuildContext context,
//     required IconData icon,
//     required String title,
//     required String subtitle,
//     String? fileSize,
//     required bool isDownloading,
//     VoidCallback? onTap,
//   }) {
//     return Container(
//       margin: const EdgeInsets.only(bottom: 8),
//       decoration: BoxDecoration(
//         border: Border.all(color: Colors.grey.shade300),
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: ListTile(
//         leading: Container(
//           padding: const EdgeInsets.all(8),
//           decoration: BoxDecoration(
//             color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
//             borderRadius: BorderRadius.circular(8),
//           ),
//           child: Icon(
//             icon,
//             color: Theme.of(context).primaryColor,
//             size: 24,
//           ),
//         ),
//         title: Text(
//           title,
//           style: const TextStyle(fontWeight: FontWeight.w600),
//         ),
//         subtitle: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: <Widget>[
//             Text(subtitle),
//             if (fileSize != null)
//               Text(
//                 'الحجم: $fileSize',
//                 style: TextStyle(
//                   fontSize: 12,
//                   color: Colors.grey.shade600,
//                 ),
//               ),
//           ],
//         ),
//         trailing: isDownloading
//             ? const SizedBox(
//                 width: 20,
//                 height: 20,
//                 child: CircularProgressIndicator(strokeWidth: 2),
//               )
//             : Icon(
//                 Ionicons.download_outline,
//                 color: Theme.of(context).primaryColor,
//               ),
//         onTap: onTap,
//         enabled: !isDownloading,
//       ),
//     );
//   }

//   IconData _getIconForFormat(OptionFormat format) {
//     switch (format) {
//       case OptionFormat.pdf:
//         return Ionicons.document_text_outline;
//       case OptionFormat.docx:
//         return Ionicons.document_outline;
//       case OptionFormat.audioMpeg:
//         return Ionicons.musical_note_outline;
//       case OptionFormat.videoMp4:
//       case OptionFormat.videoYoutube:
//         return Ionicons.videocam_outline;
//       case OptionFormat.imageJpeg:
//         return Ionicons.image_outline;
//       case OptionFormat.textPlain:
//       case OptionFormat.html:
//       case OptionFormat.article:
//         return Ionicons.document_text_outline;
//       case OptionFormat.tweet:
//         return Ionicons.logo_twitter;
//       case OptionFormat.unknown:
//         return Ionicons.document_outline;
//     }
//   }

//   String _getTitleForFormat(OptionFormat format) {
//     switch (format) {
//       case OptionFormat.pdf:
//         return 'ملف PDF';
//       case OptionFormat.docx:
//         return 'ملف DOCX';
//       case OptionFormat.audioMpeg:
//         return 'ملف صوتي';
//       case OptionFormat.videoMp4:
//       case OptionFormat.videoYoutube:
//         return 'ملف فيديو';
//       case OptionFormat.imageJpeg:
//         return 'صورة';
//       case OptionFormat.textPlain:
//         return 'ملف نصي';
//       case OptionFormat.html:
//         return 'صفحة ويب';
//       case OptionFormat.article:
//         return 'مقال';
//       case OptionFormat.tweet:
//         return 'تغريدة';
//       case OptionFormat.unknown:
//         return 'ملف';
//     }
//   }

//   String _getDefaultFileName(OptionFormat format) {
//     switch (format) {
//       case OptionFormat.pdf:
//         return 'document.pdf';
//       case OptionFormat.docx:
//         return 'document.docx';
//       case OptionFormat.audioMpeg:
//         return 'audio.mp3';
//       case OptionFormat.videoMp4:
//         return 'video.mp4';
//       case OptionFormat.videoYoutube:
//         return 'video.mp4';
//       case OptionFormat.imageJpeg:
//         return 'image.jpg';
//       case OptionFormat.textPlain:
//         return 'text.txt';
//       case OptionFormat.html:
//         return 'page.html';
//       case OptionFormat.article:
//         return 'article.txt';
//       case OptionFormat.tweet:
//         return 'tweet.json';
//       case OptionFormat.unknown:
//         return 'file';
//     }
//   }
// }

// void showAudioDownloadDialog(BuildContext context, String mediaId) {
//   showDialog(
//     context: context,
//     barrierDismissible: false,
//     builder: (BuildContext context) => AudioDownloadDialog(
//       mediaId: mediaId,
//     ),
//   );
// }
