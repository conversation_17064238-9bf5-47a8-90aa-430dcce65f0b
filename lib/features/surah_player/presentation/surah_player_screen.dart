import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../common/widgets/favorite_button.dart';
import '../../../data/models/media_player_state.dart';
import '../../../features/user_data/presentation/progress_tracker.dart';
import '../../../routing/app_router.dart';
import '../../../utils/error_popup.dart';
import '../../media_player/application/media_player_controller.dart';
import './audio_view.dart';
import './reading_view.dart';

class SurahPlayerScreen extends ConsumerStatefulWidget {
  const SurahPlayerScreen({super.key, required this.surahId});
  final String surahId;

  @override
  ConsumerState<SurahPlayerScreen> createState() => _SurahPlayerScreenState();
}

class _SurahPlayerScreenState extends ConsumerState<SurahPlayerScreen> {
  bool _isBottomSheetShowing = false;
  bool _wasPlaying = false;
  String? _lastErrorMessage;

  @override
  Widget build(BuildContext context) {
    // Correctly access the provider by passing the surahId
    final MediaPlayerController playerNotifier =
        ref.read(mediaPlayerControllerProvider(widget.surahId).notifier);
    final MediaPlayerState playerState =
        ref.watch(mediaPlayerControllerProvider(widget.surahId));

    // Handle error messages with popup
    if (playerState.errorMessage != null &&
        playerState.errorMessage != _lastErrorMessage) {
      _lastErrorMessage = playerState.errorMessage;

      // Show error popup after the current build completes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && playerState.errorMessage != null) {
          ErrorPopup.show(
            context: context,
            message: playerState.errorMessage!,
          );

          // Clear the error message from state after showing popup
          playerNotifier.clearErrorMessage();
        }
      });
    }

    // Check if we need to show the reading view
    if (playerState.currentViewIndex == 1 && !_isBottomSheetShowing) {
      // Use a post-frame callback to avoid building during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _isBottomSheetShowing = true;
        // Store the current playing state
        _wasPlaying = playerState.isPlaying;

        // Pause audio if it's playing
        if (_wasPlaying) {
          playerNotifier.pause();
        }

        _showReadingViewBottomSheet(context).then((_) {
          if (!mounted) {
            return;
          }
          _isBottomSheetShowing = false;
          if (_wasPlaying) {
            Future<void>.delayed(const Duration(milliseconds: 100), () {
              // Shorter delay might be okay
              if (mounted) {
                playerNotifier.playMedia(); // Call MediaPlayerController's play
              }
            });
          }
        });
        playerNotifier.resetViewIndex();
      });
    }

    // Main content
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Ionicons.arrow_back_outline),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500)),
        centerTitle: true,
        actions: <Widget>[
          if (playerState.mediaItem != null)
            FavoriteButton(
              itemId: playerState.mediaItem!.id,
              itemType: playerState.mediaItem!.type,
            ),
          IconButton(
            icon: const Icon(Ionicons.share_outline),
            onPressed: playerNotifier.shareContent,
          ),
          IconButton(
            icon: const Icon(Ionicons.settings_outline),
            onPressed: () {
              // Handle settings navigation here

              context.push(SGRoute.settings.route);
            },
          ),
        ],
        elevation: 0.5,
        shadowColor: Colors.grey.withValues(alpha: 0.2),
      ),
      body: Consumer(
          builder: (BuildContext context, WidgetRef ref, Widget? child) {
        // Initial loading state (before.mediaItem is available)
        if (playerState.isLoading && playerState.mediaItem == null) {
          return const Center(child: CircularProgressIndicator());
        }

        // If.mediaItem is still null after loading and no error (should ideally not happen if logic is correct)
        if (playerState.mediaItem == null) {
          // This might indicate an issue in the notifier's logic if it's not an error state
          // or if the media item genuinely couldn't be found and wasn't reported as an error.
          return const Center(child: Text('تفاصيل الملف غير متوفرة.'));
        }
        return Stack(
          children: <Widget>[
            AudioView(mediaId: widget.surahId), // Pass mediaId
            if (playerState.mediaItem != null &&
                playerState.totalDuration > Duration.zero)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: ProgressTracker(
                  itemId: playerState.mediaItem!.id,
                  duration: playerState.totalDuration,
                  position: playerState.currentPosition,
                  onPositionChanged: (Duration position) {
                    playerNotifier.seekTo(position); // Assuming seekTo exists
                  },
                ),
              ),
          ],
        );
      }),
    );
  }

  Future<void> _showReadingViewBottomSheet(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return SafeArea(
          child: DraggableScrollableSheet(
            initialChildSize: 0.9,
            minChildSize: 0.5,
            maxChildSize: 0.95,
            builder: (BuildContext context, ScrollController scrollController) {
              return Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  children: <Widget>[
                    // Drag handle
                    Container(
                      margin: const EdgeInsets.only(top: 12, bottom: 8),
                      height: 4,
                      width: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    // Add progress tracker at the top of reading view
                    Consumer(
                      builder:
                          (BuildContext context, WidgetRef ref, Widget? child) {
                        final MediaPlayerState playerState = ref.watch(
                            mediaPlayerControllerProvider(widget.surahId));
                        final MediaPlayerController playerNotifier = ref.read(
                            mediaPlayerControllerProvider(widget.surahId)
                                .notifier);

                        if (playerState.mediaItem == null) {
                          return const SizedBox.shrink();
                        }

                        return ProgressTracker(
                          itemId: playerState.mediaItem!.id,
                          duration: playerState.totalDuration,
                          position: playerState.currentPosition,
                          onPositionChanged: (Duration position) {
                            playerNotifier.seekTo(position);
                          },
                        );
                      },
                    ),

                    Expanded(
                      child: ReadingView(
                          mediaId: widget.surahId,
                          scrollController: scrollController),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
