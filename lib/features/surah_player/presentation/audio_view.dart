import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart'; // Using Gap from your pubspec
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../common/universal_image.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_player_state.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../gen/assets.gen.dart';
import '../../media_player/application/media_player_controller.dart';
import '../../media_player/presentation/enhanced_video_player.dart';
import '../../media_player/presentation/widgets/media_control_bar.dart';
import './widgets/playback_controls.dart';

class AudioView extends HookConsumerWidget {
  const AudioView({
    super.key,
    required this.mediaId,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerController playerNotifier =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);
    final MediaPlayerState playerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final MediaUiModel? surah = playerState.mediaItem;

    final AnimationController swipeIconController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    final Animation<double> swipeIconAnimation =
        Tween<double>(begin: -5, end: 5).animate(
      CurvedAnimation(parent: swipeIconController, curve: Curves.easeInOut),
    );

    if (surah == null) {
      return const Center(child: Text('لم يتم تحميل الملف'));
    }

    final double screenHeight = MediaQuery.of(context).size.height;
    final double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      // Add Scaffold if this view takes the whole screen part managed by PageView
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: SingleChildScrollView(
          // Allows content to scroll if it overflows
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Column(
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: <Widget>[
                        Gap(screenHeight * 0.02),
                        Text(
                          surah.title,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  const Gap(20),
                  if (surah.type == MediaType.video)
                    EnhancedVideoPlayer(mediaId: mediaId),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: <Widget>[
                        if (surah.type == MediaType.audio) ...<Widget>[
                          Card(
                            elevation: 8.0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.0),
                            ),
                            clipBehavior: Clip.antiAlias,
                            child: UniversalImage(
                              path:
                                  surah.thumbnailUrl ?? Assets.img.drImage.path,
                              width: screenWidth * 0.4,
                              height: screenWidth * 0.4,
                            ),
                          ),
                          const Gap(24),
                          PlaybackControlsWidget(mediaId: mediaId),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              if (surah.type == MediaType.audio) Gap(screenHeight * 0.05),

              // Bottom Action Bar - Using the shared MediaControlBar
              MediaControlBar(mediaId: mediaId),

              Gap(screenHeight * 0.05),

              IconButton(
                onPressed: () {
                  playerNotifier.changeView(1);
                },
                icon: AnimatedBuilder(
                  animation: swipeIconAnimation,
                  builder: (BuildContext context, Widget? child) {
                    return Transform.translate(
                      offset: Offset(0, swipeIconAnimation.value),
                      child: child,
                    );
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const Icon(Ionicons.chevron_up_outline, size: 18),
                      const Gap(4),
                      Text('اسحب للقراءة',
                          style: Theme.of(context).textTheme.bodySmall),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Methods moved to shared MediaControlBar component
}
