// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// A provider for navigation functions
@ProviderFor(navigation)
const navigationProvider = NavigationProvider._();

/// A provider for navigation functions
final class NavigationProvider
    extends $FunctionalProvider<NavigationService, NavigationService>
    with $Provider<NavigationService> {
  /// A provider for navigation functions
  const NavigationProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'navigationProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$navigationHash();

  @$internal
  @override
  $ProviderElement<NavigationService> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  NavigationService create(Ref ref) {
    return navigation(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NavigationService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<NavigationService>(value),
    );
  }
}

String _$navigationHash() => r'250ce72cfb58856198368c8e131708623ea5bd36';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
