import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../data/models/user.dart';
import '../../../utils/context_extensions.dart';
import '../application/user_provider.dart';
import 'auth_form_field.dart';

class ChangePasswordPage extends HookConsumerWidget {
  const ChangePasswordPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use hooks to replace state variables and controllers
    final GlobalKey<FormState> formKey =
        useMemoized(() => GlobalKey<FormState>());
    final TextEditingController currentPasswordController =
        useTextEditingController();
    final TextEditingController newPasswordController =
        useTextEditingController();
    final TextEditingController confirmPasswordController =
        useTextEditingController();
    final ValueNotifier<bool> isLoading = useState(false);
    final ValueNotifier<bool> showCurrentPassword = useState(false);
    final ValueNotifier<bool> showNewPassword = useState(false);
    final ValueNotifier<bool> showConfirmPassword = useState(false);

    // Change password function
    Future<void> changePassword() async {
      if (!formKey.currentState!.validate()) {
        return;
      }

      isLoading.value = true;

      final bool success =
          await ref.read(userNotifierProvider.notifier).changePassword(
                currentPassword: currentPasswordController.text,
                newPassword: newPasswordController.text,
              );

      isLoading.value = false;

      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تغيير كلمة المرور بنجاح'),
          ),
        );
        context.pop();
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('فشل تغيير كلمة المرور. كلمة المرور الحالية غير صحيحة.'),
          ),
        );
      }
    }

    final AsyncValue<UserModel?> userState = ref.watch(userNotifierProvider);

    // Create a local function for building the change password form
    Widget buildChangePasswordForm() {
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                const SizedBox(height: 20),
                const Icon(
                  Icons.lock_outline,
                  size: 80,
                  color: Colors.grey,
                ),
                const SizedBox(height: 20),
                const Text(
                  'تغيير كلمة المرور',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'يرجى إدخال كلمة المرور الحالية وكلمة المرور الجديدة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 30),
                AuthFormField(
                  controller: currentPasswordController,
                  labelText: 'كلمة المرور الحالية',
                  hintText: 'أدخل كلمة المرور الحالية',
                  obscureText: !showCurrentPassword.value,
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      showCurrentPassword.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      showCurrentPassword.value = !showCurrentPassword.value;
                    },
                  ),
                  validator: (String? value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال كلمة المرور الحالية';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                AuthFormField(
                  controller: newPasswordController,
                  labelText: 'كلمة المرور الجديدة',
                  hintText: 'أدخل كلمة المرور الجديدة',
                  obscureText: !showNewPassword.value,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      showNewPassword.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      showNewPassword.value = !showNewPassword.value;
                    },
                  ),
                  validator: (String? value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال كلمة المرور الجديدة';
                    }
                    if (value.length < 6) {
                      return 'يجب أن تتكون كلمة المرور من 6 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                AuthFormField(
                  controller: confirmPasswordController,
                  labelText: 'تأكيد كلمة المرور الجديدة',
                  hintText: 'أعد إدخال كلمة المرور الجديدة',
                  obscureText: !showConfirmPassword.value,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      showConfirmPassword.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      showConfirmPassword.value = !showConfirmPassword.value;
                    },
                  ),
                  validator: (String? value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى تأكيد كلمة المرور الجديدة';
                    }
                    if (value != newPasswordController.text) {
                      return 'كلمات المرور غير متطابقة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: isLoading.value ? null : changePassword,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: context.colorScheme.primary,
                    foregroundColor: context.colorScheme.onPrimary,
                  ),
                  child: isLoading.value
                      ? const CircularProgressIndicator.adaptive()
                      : const Text(
                          'تغيير كلمة المرور',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () {
                    context.pop();
                  },
                  child: const Text('إلغاء'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تغيير كلمة المرور'),
      ),
      body: userState.when(
        data: (UserModel? user) {
          if (user == null) {
            return const Center(
              child: Text('لم يتم تسجيل الدخول'),
            );
          }
          return buildChangePasswordForm();
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }
}
