// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_info_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProfileInfoItem {
  IconData get icon;
  String get title;
  String get value;

  /// Create a copy of ProfileInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProfileInfoItemCopyWith<ProfileInfoItem> get copyWith =>
      _$ProfileInfoItemCopyWithImpl<ProfileInfoItem>(
          this as ProfileInfoItem, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProfileInfoItem &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, icon, title, value);

  @override
  String toString() {
    return 'ProfileInfoItem(icon: $icon, title: $title, value: $value)';
  }
}

/// @nodoc
abstract mixin class $ProfileInfoItemCopyWith<$Res> {
  factory $ProfileInfoItemCopyWith(
          ProfileInfoItem value, $Res Function(ProfileInfoItem) _then) =
      _$ProfileInfoItemCopyWithImpl;
  @useResult
  $Res call({IconData icon, String title, String value});
}

/// @nodoc
class _$ProfileInfoItemCopyWithImpl<$Res>
    implements $ProfileInfoItemCopyWith<$Res> {
  _$ProfileInfoItemCopyWithImpl(this._self, this._then);

  final ProfileInfoItem _self;
  final $Res Function(ProfileInfoItem) _then;

  /// Create a copy of ProfileInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = null,
    Object? title = null,
    Object? value = null,
  }) {
    return _then(_self.copyWith(
      icon: null == icon
          ? _self.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as IconData,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _ProfileInfoItem implements ProfileInfoItem {
  const _ProfileInfoItem(
      {required this.icon, required this.title, required this.value});

  @override
  final IconData icon;
  @override
  final String title;
  @override
  final String value;

  /// Create a copy of ProfileInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProfileInfoItemCopyWith<_ProfileInfoItem> get copyWith =>
      __$ProfileInfoItemCopyWithImpl<_ProfileInfoItem>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProfileInfoItem &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, icon, title, value);

  @override
  String toString() {
    return 'ProfileInfoItem(icon: $icon, title: $title, value: $value)';
  }
}

/// @nodoc
abstract mixin class _$ProfileInfoItemCopyWith<$Res>
    implements $ProfileInfoItemCopyWith<$Res> {
  factory _$ProfileInfoItemCopyWith(
          _ProfileInfoItem value, $Res Function(_ProfileInfoItem) _then) =
      __$ProfileInfoItemCopyWithImpl;
  @override
  @useResult
  $Res call({IconData icon, String title, String value});
}

/// @nodoc
class __$ProfileInfoItemCopyWithImpl<$Res>
    implements _$ProfileInfoItemCopyWith<$Res> {
  __$ProfileInfoItemCopyWithImpl(this._self, this._then);

  final _ProfileInfoItem _self;
  final $Res Function(_ProfileInfoItem) _then;

  /// Create a copy of ProfileInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? icon = null,
    Object? title = null,
    Object? value = null,
  }) {
    return _then(_ProfileInfoItem(
      icon: null == icon
          ? _self.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as IconData,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
