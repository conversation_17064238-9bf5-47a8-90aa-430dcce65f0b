import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../data/models/user.dart';
import '../../../utils/context_extensions.dart';
import '../application/user_provider.dart';
import 'auth_form_field.dart';

class EditProfilePage extends HookConsumerWidget {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use hooks to replace state variables and controllers
    final GlobalKey<FormState> formKey =
        useMemoized(() => GlobalKey<FormState>());
    final TextEditingController nameController = useTextEditingController();
    final TextEditingController emailController = useTextEditingController();
    final ValueNotifier<bool> isLoading = useState(false);

    // Watch user state
    final AsyncValue<UserModel?> userState = ref.watch(userNotifierProvider);

    // Initialize user data when available
    useEffect(() {
      if (userState.hasValue && userState.value != null) {
        final UserModel user = userState.value!;
        nameController.text = user.name;
        emailController.text = user.email;
      }
      return null;
    }, <Object?>[userState]);

    // Update profile function
    Future<void> updateProfile() async {
      if (!formKey.currentState!.validate()) {
        return;
      }

      isLoading.value = true;

      final bool success =
          await ref.read(userNotifierProvider.notifier).updateProfile(
                name: nameController.text.trim(),
                email: emailController.text.trim(),
              );

      isLoading.value = false;

      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الملف الشخصي بنجاح'),
          ),
        );
        context.pop();
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'فشل تحديث الملف الشخصي. قد يكون البريد الإلكتروني مستخدمًا بالفعل.'),
          ),
        );
      }
    }

    // Create a local function for building the edit form
    Widget buildEditForm() {
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                const SizedBox(height: 20),
                const CircleAvatar(
                  radius: 60,
                  backgroundColor: Colors.grey,
                  child: Icon(
                    Icons.person,
                    size: 80,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 30),
                AuthFormField(
                  controller: nameController,
                  labelText: 'الاسم',
                  hintText: 'أدخل اسمك الكامل',
                  prefixIcon: const Icon(Icons.person),
                  validator: (String? value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسمك';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                AuthFormField(
                  controller: emailController,
                  labelText: 'البريد الإلكتروني',
                  hintText: 'أدخل بريدك الإلكتروني',
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: const Icon(Icons.email),
                  validator: (String? value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال بريدك الإلكتروني';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return 'يرجى إدخال بريد إلكتروني صحيح';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: isLoading.value ? null : updateProfile,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: context.colorScheme.primary,
                    foregroundColor: context.colorScheme.onPrimary,
                  ),
                  child: isLoading.value
                      ? const CircularProgressIndicator.adaptive()
                      : const Text(
                          'حفظ التغييرات',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () {
                    context.pop();
                  },
                  child: const Text('إلغاء'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
      ),
      body: userState.when(
        data: (UserModel? user) {
          if (user == null) {
            return const Center(
              child: Text('لم يتم تسجيل الدخول'),
            );
          }
          return buildEditForm();
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }
}
