import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sqflite/sqflite.dart';

import '../../../data/models/user.dart';
import '../../../data/sqlite/sqlite_helper.dart';
import '../../../di/components/service_locator.dart';
import '../../user_data/application/user_data_providers.dart';

part 'user_provider.g.dart';

@Riverpod(keepAlive: true)
class UserNotifier extends _$UserNotifier {
  final SQLiteHelper _sqliteHelper = getIt<SQLiteHelper>();

  @override
  Future<UserModel?> build() async {
    return _sqliteHelper.getCurrentUser();
  }

  Future<bool> register({
    required String name,
    required String email,
    required String password,
  }) async {
    state = const AsyncValue<UserModel?>.loading();

    try {
      final UserModel? user = await _sqliteHelper.registerUser(
        name: name,
        email: email,
        password: password,
      );

      if (user != null) {
        // Transfer anonymous data to the new user account
        await ref
            .read(userDataNotifierProvider.notifier)
            .transferAnonymousData(user.id);

        state = AsyncValue<UserModel?>.data(user);
        return true;
      } else {
        state = const AsyncValue<UserModel?>.data(null);
        return false;
      }
    } catch (e) {
      state = AsyncValue<UserModel?>.error(e, StackTrace.current);
      return false;
    }
  }

  Future<bool> login({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue<UserModel?>.loading();

    try {
      final UserModel? user = await _sqliteHelper.loginUser(
        email: email,
        password: password,
      );

      if (user != null) {
        // Transfer anonymous data to the user account
        await ref
            .read(userDataNotifierProvider.notifier)
            .transferAnonymousData(user.id);

        state = AsyncValue<UserModel?>.data(user);
        return true;
      } else {
        state = const AsyncValue<UserModel?>.data(null);
        return false;
      }
    } catch (e) {
      state = AsyncValue<UserModel?>.error(e, StackTrace.current);
      return false;
    }
  }

  Future<void> logout() async {
    if (state.value != null) {
      await _sqliteHelper.logoutUser(state.value!.id);
      state = const AsyncValue<UserModel?>.data(null);

      // Refresh user data providers to show anonymous data
      ref.invalidate(favoritesNotifierProvider);
      ref.invalidate(progressNotifierProvider);
      ref.invalidate(historyNotifierProvider);
    }
  }

  Future<bool> updateProfile({
    required String name,
    String? email,
  }) async {
    if (state.value == null) {
      return false;
    }

    final bool success = await _sqliteHelper.updateUserProfile(
      userId: state.value!.id,
      name: name,
      email: email,
    );

    if (success) {
      // Refresh user data
      final UserModel? updatedUser =
          await _sqliteHelper.getUserById(state.value!.id);
      if (updatedUser != null) {
        state = AsyncValue<UserModel?>.data(updatedUser);
      }
    }

    return success;
  }

  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (state.value == null) {
      return false;
    }

    return _sqliteHelper.changePassword(
      userId: state.value!.id,
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
  }

  // Delete account and all associated data
  Future<bool> deleteAccount() async {
    if (state.value == null) {
      return false;
    }

    try {
      final String userId = state.value!.id;

      // Clear all user data
      await ref.read(userDataNotifierProvider.notifier).clearAllUserData();

      // Delete user from database
      final Database db = await _sqliteHelper.database;
      await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: <Object?>[userId],
      );

      // Update state
      state = const AsyncValue<UserModel?>.data(null);

      return true;
    } catch (e) {
      debugPrint('Error deleting account: $e');
      return false;
    }
  }
}

// Provider for checking if user is authenticated
@Riverpod(keepAlive: true)
bool isAuthenticated(Ref ref) {
  final AsyncValue<UserModel?> userState = ref.watch(userNotifierProvider);
  return userState.value != null;
}

// Provider for getting the current user ID (authenticated or anonymous)
@Riverpod(keepAlive: true)
String currentUserId(Ref ref) {
  final AsyncValue<UserModel?> userState = ref.watch(userNotifierProvider);
  return userState.hasValue && userState.value != null
      ? userState.value!.id
      : 'anonymous_user';
}
