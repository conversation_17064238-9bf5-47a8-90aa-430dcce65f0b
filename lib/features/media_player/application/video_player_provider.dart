// lib/features/media_player/application/video_player_provider.dart
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../data/enums/video_url_types_enum.dart';
import '../../../data/models/media_player_state.dart';
import './media_player_controller.dart';

part 'video_player_provider.freezed.dart';
part 'video_player_provider.g.dart';

@freezed
abstract class VideoPlayerState with _$VideoPlayerState {
  const factory VideoPlayerState({
    @Default(VideoPlayerType.unknown) VideoPlayerType videoType,
    String? videoUrl,
    String? youtubeId,
    @Default(false) bool isFullScreen,
    @Default(true) bool isLoading,
    String? errorMessage,
    YoutubePlayerController? youtubeController,
    WebViewController? webViewController,
  }) = _VideoPlayerState;
}

@riverpod
class VideoPlayerNotifier extends _$VideoPlayerNotifier {
  // Store controller reference outside of state to access during disposal
  YoutubePlayerController? _youtubeController;

  @override
  VideoPlayerState build() {
    ref.onDispose(() {
      // Clean up using the stored controller reference
      if (_youtubeController != null) {
        _youtubeController!.removeListener(_youtubePlayerListener);
        _youtubeController!.dispose();
        _youtubeController = null;
      }
      // No need to access state here
    });
    return const VideoPlayerState();
  }

  void setVideoUrl(String? url) {
    if (url == null || url.isEmpty) {
      if (ref.mounted) {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'لا يوجد رابط فيديو متاح',
        );
      }
      return;
    }
    if (ref.mounted) {
      state =
          state.copyWith(videoUrl: url, isLoading: true, errorMessage: null);
    }
    _determineVideoType(url);
  }

  void _determineVideoType(String url) {
    VideoPlayerType type = VideoPlayerType.unknown;
    String? newYoutubeId;

    if (url.toLowerCase().endsWith('.mp4') ||
        url.toLowerCase().endsWith('.webm') ||
        url.toLowerCase().endsWith('.mov') ||
        url.toLowerCase().endsWith('.avi')) {
      type = VideoPlayerType.direct;
    } else if (url.contains('youtube.com/watch?v=') ||
        url.contains('youtu.be/')) {
      try {
        newYoutubeId = YoutubePlayer.convertUrlToId(url);
        if (newYoutubeId != null) {
          type = VideoPlayerType.youtube;
        }
      } catch (e) {
        debugPrint('Error extracting YouTube ID: $e');
        if (ref.mounted) {
          state = state.copyWith(
              errorMessage: 'Invalid YouTube URL', isLoading: false);
        }
        return;
      }
    } else if (url.contains('facebook.com') || url.contains('fb.watch')) {
      type = VideoPlayerType.facebook;
    } else if (url.contains('tiktok.com')) {
      type = VideoPlayerType.tiktok;
    } else {
      // Fallback or attempt as direct if unknown but seems like a media URL
      type = VideoPlayerType.direct;
    }

    if (ref.mounted) {
      state = state.copyWith(
          videoType: type,
          youtubeId: newYoutubeId,
          isLoading: type == VideoPlayerType.youtube);
      if (type == VideoPlayerType.youtube && newYoutubeId != null) {
        _initializeYoutubePlayer(newYoutubeId);
      } else if (type == VideoPlayerType.facebook ||
          type == VideoPlayerType.tiktok) {
        initializeWebViewController();
        state = state.copyWith(
            isLoading: false); // WebView loading is handled by its own delegate
      } else if (type == VideoPlayerType.direct) {
        state = state.copyWith(
            isLoading:
                false); // Direct video, player widget will handle its loading
      }
    }
  }

  // Track if we should loop YouTube videos
  bool _shouldLoopYoutubeVideo = false;

  // Flag to prevent recursive listener calls during looping
  bool _isHandlingYoutubeLoop = false;

  void _initializeYoutubePlayer(String youtubeId) {
    // Clean up existing controller using our class field
    if (_youtubeController != null) {
      _youtubeController!.removeListener(_youtubePlayerListener);
      _youtubeController!.dispose();
      _youtubeController = null;
    }

    try {
      // Create new controller and store it in our class field
      final YoutubePlayerController controller = YoutubePlayerController(
        initialVideoId: youtubeId,
        flags: const YoutubePlayerFlags(
          autoPlay: false,
          captionLanguage: 'ar',
        ),
      )..addListener(_youtubePlayerListener);

      // Store reference in our class field for disposal
      _youtubeController = controller;

      if (ref.mounted) {
        state = state.copyWith(
          youtubeController: controller,
          isLoading:
              false, // Controller is created, actual video loading handled by player
          // Don't clear error message here, as it might be needed for debugging
          // but the UI will handle showing it appropriately
        );
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة مشغل يوتيوب: $e');
      if (ref.mounted) {
        state = state.copyWith(
          errorMessage: 'خطأ في تهيئة مشغل يوتيوب: $e',
          isLoading: false,
        );
      }
    }
  }

  // Toggle YouTube video looping
  void toggleYoutubeLoop() {
    _shouldLoopYoutubeVideo = !_shouldLoopYoutubeVideo;
    debugPrint('YouTube loop mode set to: $_shouldLoopYoutubeVideo');

    // If we're turning on loop mode and the video is already at the end,
    // restart it immediately
    if (_shouldLoopYoutubeVideo &&
        _youtubeController != null &&
        _youtubeController!.value.playerState == PlayerState.ended) {
      debugPrint('Video already ended, restarting immediately');
      restartYoutubeVideo();
    }
  }

  // Check if YouTube video should loop
  bool shouldLoopYoutubeVideo() {
    return _shouldLoopYoutubeVideo;
  }

  // Method to restart YouTube video safely
  void restartYoutubeVideo() {
    if (_youtubeController == null || _isHandlingYoutubeLoop) {
      return;
    }

    _isHandlingYoutubeLoop = true;

    try {
      // Get the current video ID
      final String videoId = _youtubeController!.metadata.videoId;

      debugPrint('Restarting YouTube video: $videoId');

      // Use a different approach - reload the video
      // This is more reliable than seeking to the beginning
      Future<void>.delayed(const Duration(milliseconds: 50), () {
        try {
          if (_youtubeController != null) {
            // Load the same video from the beginning
            _youtubeController!.load(videoId);

            // Short delay before playing
            Future<void>.delayed(const Duration(milliseconds: 300), () {
              if (_youtubeController != null) {
                _youtubeController!.play();
              }
            });
          }
        } catch (e) {
          debugPrint('Error reloading YouTube video: $e');
        } finally {
          _isHandlingYoutubeLoop = false;
        }
      });
    } catch (e) {
      _isHandlingYoutubeLoop = false;
      debugPrint('Error in restartYoutubeVideo: $e');
    }
  }

  void _youtubePlayerListener() {
    if (!ref.mounted) {
      return;
    }

    final YoutubePlayerController? controller = state.youtubeController;
    if (controller == null) {
      return;
    }

    // Store current state values locally
    final String? currentYoutubeId = state.youtubeId;
    final bool currentIsLoading = state.isLoading;
    final String? currentErrorMessage = state.errorMessage;

    final YoutubePlayerValue currentValue = controller.value;
    final PlayerState currentPlayerState = currentValue.playerState;
    final int error = currentValue.errorCode;

    // Check if video has ended and should loop
    if (currentPlayerState == PlayerState.ended &&
        _shouldLoopYoutubeVideo &&
        !_isHandlingYoutubeLoop) {
      debugPrint('YouTube video ended, looping...');

      // Use our safe restart method
      restartYoutubeVideo();
    }

    // Only update loading state based on player state
    final bool isCurrentlyLoading =
        currentPlayerState == PlayerState.buffering ||
            currentPlayerState == PlayerState.unknown;

    // Only set error message if there's a real error code
    // This prevents showing errors during normal initialization
    String? newErrorMessage;
    if (error != null && error != 0) {
      newErrorMessage = _youtubeErrorToMessage(error);
      debugPrint('YouTube error code: $error, message: $newErrorMessage');
    }

    if (ref.mounted) {
      // Only update state if something changed
      if (currentIsLoading != isCurrentlyLoading ||
          (newErrorMessage != null && newErrorMessage != currentErrorMessage)) {
        state = state.copyWith(
          isLoading: isCurrentlyLoading,
          // Only set error message if we have a new one
          errorMessage: newErrorMessage,
        );
      }

      // Update the MediaPlayerController state based on YouTube player state
      // This ensures the play/pause buttons in AudioView work correctly
      try {
        final bool isPlaying = currentPlayerState == PlayerState.playing;

        // Update all media controllers that might be using this YouTube video
        for (final String mediaId in _activeMediaIds) {
          try {
            // Get the MediaPlayerController for this mediaId
            final MediaPlayerControllerProvider mediaControllerProvider =
                mediaPlayerControllerProvider(mediaId);

            // Check if this provider exists in the container
            if (ref.exists(mediaControllerProvider)) {
              final MediaPlayerController mediaController =
                  ref.read(mediaControllerProvider.notifier);
              final MediaPlayerState mediaState =
                  ref.read(mediaControllerProvider);

              // Check if this controller is for our YouTube video
              if (mediaState.mediaItem?.videoUrl != null &&
                  currentYoutubeId != null &&
                  mediaState.mediaItem!.videoUrl!.contains(currentYoutubeId)) {
                // This is our YouTube video, update its state
                if (mediaState.isPlaying != isPlaying) {
                  mediaController.updatePlayingState(isPlaying);
                  debugPrint(
                      'Updated playing state for mediaId: $mediaId to: $isPlaying');
                }
              }
            }
          } catch (innerError) {
            // Skip this mediaId if there's an error
            debugPrint(
                'Error updating MediaPlayerController for mediaId: $mediaId: $innerError');
          }
        }
      } catch (e) {
        // Ignore errors in this experimental code
        debugPrint('Error updating MediaPlayerController state: $e');
      }
    }
  }

  // Keep track of active media IDs
  static final Set<String> _activeMediaIds = <String>{};

  // Register a media ID to be updated when YouTube player state changes
  void registerMediaId(String mediaId) {
    _activeMediaIds.add(mediaId);
  }

  // Unregister a media ID
  void unregisterMediaId(String mediaId) {
    _activeMediaIds.remove(mediaId);
  }

  void initializeWebViewController() {
    // Store current state values locally
    final String? videoUrl = state.videoUrl;
    final VideoPlayerType videoType = state.videoType;

    if (videoUrl == null) {
      if (ref.mounted) {
        state = state.copyWith(
            errorMessage: 'رابط الفيديو غير موجود لعرض الويب.',
            isLoading: false);
      }
      return;
    }

    final WebViewController controller = WebViewController();
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.black)
      ..loadRequest(Uri.parse(videoUrl))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            debugPrint('WebView: Page started loading: $url');
            if (ref.mounted) {
              setLoading(true);
            }
          },
          onPageFinished: (String url) {
            debugPrint('WebView: Page finished loading: $url');
            if (ref.mounted) {
              setLoading(false);

              // Get the current video type again as it might have changed
              final VideoPlayerType currentVideoType = state.videoType;

              if (currentVideoType == VideoPlayerType.facebook) {
                _injectFacebookVideoCSS(controller);
              } else if (currentVideoType == VideoPlayerType.tiktok) {
                _injectTikTokVideoCSS(controller);
              }
            }
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView error: ${error.description}');
            if (ref.mounted) {
              state = state.copyWith(
                  errorMessage: 'خطأ في عرض الويب: ${error.description}',
                  isLoading: false);
            }
          },
        ),
      );
    if (ref.mounted) {
      state = state.copyWith(
          webViewController: controller,
          isLoading: true); // isLoading true until onPageFinished
    }
  }

  void _injectFacebookVideoCSS(WebViewController webViewController) {
    const String css = '''
      .video-player { width: 100% !important; height: auto !important; }
      .video-container { width: 100% !important; }
    ''';
    webViewController
        .runJavaScript('var style = document.createElement("style"); '
            'style.textContent = `$css`; '
            'document.head.appendChild(style);');
  }

  void _injectTikTokVideoCSS(WebViewController webViewController) {
    const String css = '''
      .video-feed { width: 100% !important; height: auto !important; }
      .video-player { width: 100% !important; }
    ''';
    webViewController
        .runJavaScript('var style = document.createElement("style"); '
            'style.textContent = `$css`; '
            'document.head.appendChild(style);');
  }

  void setLoading(bool loading) {
    if (ref.mounted) {
      state = state.copyWith(isLoading: loading);
    }
  }

  void setError(String message) {
    if (ref.mounted) {
      state = state.copyWith(
        errorMessage: message,
        isLoading: false,
      );
    }
  }

  // cleanUp method removed - now handled directly in onDispose

  // Methods to control YouTube player
  void playYoutubeVideo() {
    final YoutubePlayerController? controller = state.youtubeController;
    if (controller != null) {
      controller.play();
    }
  }

  void pauseYoutubeVideo() {
    final YoutubePlayerController? controller = state.youtubeController;
    if (controller != null) {
      controller.pause();
    }
  }

  void seekYoutubeVideo(Duration position) {
    final YoutubePlayerController? controller = state.youtubeController;
    if (controller != null) {
      controller.seekTo(position);
    }
  }

  bool isYoutubeVideoPlaying() {
    final YoutubePlayerController? controller = state.youtubeController;
    if (controller != null) {
      return controller.value.playerState == PlayerState.playing;
    }
    return false;
  }

  Duration getYoutubeVideoDuration() {
    final YoutubePlayerController? controller = state.youtubeController;
    if (controller != null) {
      return Duration(seconds: controller.metadata.duration.inSeconds);
    }
    return Duration.zero;
  }

  Duration getYoutubeVideoPosition() {
    final YoutubePlayerController? controller = state.youtubeController;
    if (controller != null) {
      return Duration(seconds: controller.value.position.inSeconds);
    }
    return Duration.zero;
  }

  String _youtubeErrorToMessage(int errorCode) {
    // Error codes from youtube_player_flutter plugin source or typical IFrame API errors
    const int invalidParam = 2;
    const int html5Error = 5;
    const int videoNotFound = 100;
    const int notEmbeddable = 101; // Or 150
    const int notEmbeddable2 = 150;

    switch (errorCode) {
      case invalidParam:
        return 'معرف الفيديو غير صالح.';
      case html5Error:
        return 'حدث خطأ في مشغل HTML5.';
      case videoNotFound:
        return 'لم يتم العثور على الفيديو أو تم حذفه.';
      case notEmbeddable:
      case notEmbeddable2:
        return 'هذا الفيديو غير مسموح بتضمينه.';
      // Note: Network errors are often not reported by errorCode directly by youtube_player_flutter,
      // but rather through general PlayerState changes or connection issues.
      default:
        return 'حدث خطأ غير معروف (الرمز: $errorCode).';
    }
  }
}
