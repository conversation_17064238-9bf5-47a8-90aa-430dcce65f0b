// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_player_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(VideoPlayerNotifier)
const videoPlayerNotifierProvider = VideoPlayerNotifierProvider._();

final class VideoPlayerNotifierProvider
    extends $NotifierProvider<VideoPlayerNotifier, VideoPlayerState> {
  const VideoPlayerNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'videoPlayerNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$videoPlayerNotifierHash();

  @$internal
  @override
  VideoPlayerNotifier create() => VideoPlayerNotifier();

  @$internal
  @override
  $NotifierProviderElement<VideoPlayerNotifier, VideoPlayerState>
      $createElement($ProviderPointer pointer) =>
          $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(VideoPlayerState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<VideoPlayerState>(value),
    );
  }
}

String _$videoPlayerNotifierHash() =>
    r'25b0f7dabddec4284818f0d39522c28d9d777df8';

abstract class _$VideoPlayerNotifier extends $Notifier<VideoPlayerState> {
  VideoPlayerState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<VideoPlayerState>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<VideoPlayerState>, VideoPlayerState, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
