// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'video_player_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VideoPlayerState {
  VideoPlayerType get videoType;
  String? get videoUrl;
  String? get youtubeId;
  bool get isFullScreen;
  bool get isLoading;
  String? get errorMessage;
  YoutubePlayerController? get youtubeController;
  WebViewController? get webViewController;

  /// Create a copy of VideoPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VideoPlayerStateCopyWith<VideoPlayerState> get copyWith =>
      _$VideoPlayerStateCopyWithImpl<VideoPlayerState>(
          this as VideoPlayerState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VideoPlayerState &&
            (identical(other.videoType, videoType) ||
                other.videoType == videoType) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.youtubeId, youtubeId) ||
                other.youtubeId == youtubeId) &&
            (identical(other.isFullScreen, isFullScreen) ||
                other.isFullScreen == isFullScreen) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.youtubeController, youtubeController) ||
                other.youtubeController == youtubeController) &&
            (identical(other.webViewController, webViewController) ||
                other.webViewController == webViewController));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      videoType,
      videoUrl,
      youtubeId,
      isFullScreen,
      isLoading,
      errorMessage,
      youtubeController,
      webViewController);

  @override
  String toString() {
    return 'VideoPlayerState(videoType: $videoType, videoUrl: $videoUrl, youtubeId: $youtubeId, isFullScreen: $isFullScreen, isLoading: $isLoading, errorMessage: $errorMessage, youtubeController: $youtubeController, webViewController: $webViewController)';
  }
}

/// @nodoc
abstract mixin class $VideoPlayerStateCopyWith<$Res> {
  factory $VideoPlayerStateCopyWith(
          VideoPlayerState value, $Res Function(VideoPlayerState) _then) =
      _$VideoPlayerStateCopyWithImpl;
  @useResult
  $Res call(
      {VideoPlayerType videoType,
      String? videoUrl,
      String? youtubeId,
      bool isFullScreen,
      bool isLoading,
      String? errorMessage,
      YoutubePlayerController? youtubeController,
      WebViewController? webViewController});
}

/// @nodoc
class _$VideoPlayerStateCopyWithImpl<$Res>
    implements $VideoPlayerStateCopyWith<$Res> {
  _$VideoPlayerStateCopyWithImpl(this._self, this._then);

  final VideoPlayerState _self;
  final $Res Function(VideoPlayerState) _then;

  /// Create a copy of VideoPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? videoType = null,
    Object? videoUrl = freezed,
    Object? youtubeId = freezed,
    Object? isFullScreen = null,
    Object? isLoading = null,
    Object? errorMessage = freezed,
    Object? youtubeController = freezed,
    Object? webViewController = freezed,
  }) {
    return _then(_self.copyWith(
      videoType: null == videoType
          ? _self.videoType
          : videoType // ignore: cast_nullable_to_non_nullable
              as VideoPlayerType,
      videoUrl: freezed == videoUrl
          ? _self.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      youtubeId: freezed == youtubeId
          ? _self.youtubeId
          : youtubeId // ignore: cast_nullable_to_non_nullable
              as String?,
      isFullScreen: null == isFullScreen
          ? _self.isFullScreen
          : isFullScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      youtubeController: freezed == youtubeController
          ? _self.youtubeController
          : youtubeController // ignore: cast_nullable_to_non_nullable
              as YoutubePlayerController?,
      webViewController: freezed == webViewController
          ? _self.webViewController
          : webViewController // ignore: cast_nullable_to_non_nullable
              as WebViewController?,
    ));
  }
}

/// @nodoc

class _VideoPlayerState implements VideoPlayerState {
  const _VideoPlayerState(
      {this.videoType = VideoPlayerType.unknown,
      this.videoUrl,
      this.youtubeId,
      this.isFullScreen = false,
      this.isLoading = true,
      this.errorMessage,
      this.youtubeController,
      this.webViewController});

  @override
  @JsonKey()
  final VideoPlayerType videoType;
  @override
  final String? videoUrl;
  @override
  final String? youtubeId;
  @override
  @JsonKey()
  final bool isFullScreen;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? errorMessage;
  @override
  final YoutubePlayerController? youtubeController;
  @override
  final WebViewController? webViewController;

  /// Create a copy of VideoPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$VideoPlayerStateCopyWith<_VideoPlayerState> get copyWith =>
      __$VideoPlayerStateCopyWithImpl<_VideoPlayerState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _VideoPlayerState &&
            (identical(other.videoType, videoType) ||
                other.videoType == videoType) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.youtubeId, youtubeId) ||
                other.youtubeId == youtubeId) &&
            (identical(other.isFullScreen, isFullScreen) ||
                other.isFullScreen == isFullScreen) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.youtubeController, youtubeController) ||
                other.youtubeController == youtubeController) &&
            (identical(other.webViewController, webViewController) ||
                other.webViewController == webViewController));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      videoType,
      videoUrl,
      youtubeId,
      isFullScreen,
      isLoading,
      errorMessage,
      youtubeController,
      webViewController);

  @override
  String toString() {
    return 'VideoPlayerState(videoType: $videoType, videoUrl: $videoUrl, youtubeId: $youtubeId, isFullScreen: $isFullScreen, isLoading: $isLoading, errorMessage: $errorMessage, youtubeController: $youtubeController, webViewController: $webViewController)';
  }
}

/// @nodoc
abstract mixin class _$VideoPlayerStateCopyWith<$Res>
    implements $VideoPlayerStateCopyWith<$Res> {
  factory _$VideoPlayerStateCopyWith(
          _VideoPlayerState value, $Res Function(_VideoPlayerState) _then) =
      __$VideoPlayerStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {VideoPlayerType videoType,
      String? videoUrl,
      String? youtubeId,
      bool isFullScreen,
      bool isLoading,
      String? errorMessage,
      YoutubePlayerController? youtubeController,
      WebViewController? webViewController});
}

/// @nodoc
class __$VideoPlayerStateCopyWithImpl<$Res>
    implements _$VideoPlayerStateCopyWith<$Res> {
  __$VideoPlayerStateCopyWithImpl(this._self, this._then);

  final _VideoPlayerState _self;
  final $Res Function(_VideoPlayerState) _then;

  /// Create a copy of VideoPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? videoType = null,
    Object? videoUrl = freezed,
    Object? youtubeId = freezed,
    Object? isFullScreen = null,
    Object? isLoading = null,
    Object? errorMessage = freezed,
    Object? youtubeController = freezed,
    Object? webViewController = freezed,
  }) {
    return _then(_VideoPlayerState(
      videoType: null == videoType
          ? _self.videoType
          : videoType // ignore: cast_nullable_to_non_nullable
              as VideoPlayerType,
      videoUrl: freezed == videoUrl
          ? _self.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      youtubeId: freezed == youtubeId
          ? _self.youtubeId
          : youtubeId // ignore: cast_nullable_to_non_nullable
              as String?,
      isFullScreen: null == isFullScreen
          ? _self.isFullScreen
          : isFullScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      youtubeController: freezed == youtubeController
          ? _self.youtubeController
          : youtubeController // ignore: cast_nullable_to_non_nullable
              as YoutubePlayerController?,
      webViewController: freezed == webViewController
          ? _self.webViewController
          : webViewController // ignore: cast_nullable_to_non_nullable
              as WebViewController?,
    ));
  }
}

// dart format on
