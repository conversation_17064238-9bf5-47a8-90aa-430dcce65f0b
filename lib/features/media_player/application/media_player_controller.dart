// lib/features/media_player/application/media_player_controller.dart
import 'dart:async';
import 'dart:io'; // Keep for File type if used directly, though services should handle File ops

import 'package:chewie/chewie.dart';
import 'package:collection/collection.dart'; // For firstWhereOrNull
import 'package:flutter/foundation.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:just_audio/just_audio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:video_player/video_player.dart';

import '../../../data/enums/action_type_enum.dart';
import '../../../data/enums/media_type_enum.dart';
// import '../../../data/enums/opentions_format_enum.dart'; // opentions_format_enum.dart seems to be unused in this controller now
import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_player_state.dart';
import '../../../data/models/media_ui_model.dart';
import '../../user_data/application/user_data_providers.dart';
import 'services/interfaces/i_media_initialization_service.dart'; // For PlayerInstances
import 'services/interfaces/i_pdf_document_service.dart'; // For PdfLoadResult
import 'services/service_providers.dart';
import 'video_player_provider.dart'; // Import your service providers

part 'media_player_controller.g.dart';

@Riverpod(keepAlive: true)
class MediaPlayerController extends _$MediaPlayerController {
  AudioPlayer? _audioPlayer;
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  PDFViewController? _pdfViewController;

  @override
  MediaPlayerState build(String mediaId) {
    ref.onDispose(() {
      _disposeControllers();
    });
    final MediaPlayerState initialState = MediaPlayerState.initial();
    Future<void>.microtask(() => _loadMedia(mediaId));
    return initialState;
  }

  void _disposeControllers() {
    ref.read(mediaInitializationServiceProvider).disposePlayers(
        audioPlayer: _audioPlayer,
        videoController: _videoController,
        chewieController: _chewieController);
    _audioPlayer = null;
    _videoController = null;
    _chewieController = null;
    _pdfViewController = null;
    debugPrint(
        'MediaPlayerController: All media players disposed via service for $mediaId');
  }

  Future<void> _loadMedia(String mediaId) async {
    try {
      if (!ref.mounted) {
        return;
      }
      state = state.copyWith(
        isLoading: true,
        isPlaying: false,
        errorMessage: null,
        mediaItem: null,
        downloadedFilePath: null,
        mediaOptions: null,
        loopMode: LoopMode.off,
        currentPosition: Duration.zero,
        totalDuration: Duration.zero,
        currentViewIndex: 0,
      );

      final List<MediaUiModel> mediaItems =
          await ref.read(mediaUiListProvider.future);
      if (mediaItems.isEmpty) {
        throw Exception('No media items available');
      }
      final MediaUiModel mediaItem = mediaItems.firstWhere(
          (MediaUiModel item) => item.id == mediaId,
          orElse: () => throw Exception('Media item not found: $mediaId'));

      final String? primaryUrl = _getPrimaryUrl(mediaItem);
      String? localPath;
      if (primaryUrl != null && primaryUrl.isNotEmpty) {
        localPath = await ref
            .read(mediaDownloadServiceProvider)
            .getDownloadedFilePath(primaryUrl);
      }
      final Duration savedPosition =
          await _getSavedProgress(mediaId) ?? Duration.zero;

      if (!ref.mounted) {
        return;
      }
      state = state.copyWith(
        mediaItem: mediaItem,
        mediaOptions: mediaItem.options,
        isLoading: false,
        isPlaying: false,
        downloadedFilePath: localPath,
        currentPosition: savedPosition,
      );
      debugPrint(
          'MediaPlayerController: Media loaded: ${mediaItem.title}. Saved progress: $savedPosition');

      if (mediaItem.type.isAudioOrVideo) {
        // Check if it's a YouTube video
        final bool isYoutubeVideo = _isYoutubeUrl(mediaItem.videoUrl);

        if (mediaItem.type == MediaType.video && isYoutubeVideo) {
          // For YouTube videos, we don't initialize with the regular player
          // Instead, we'll use the EnhancedVideoPlayer with the YouTube player
          if (ref.mounted) {
            state = state.copyWith(isLoading: false);
            debugPrint(
                'مشغل الوسائط: تم اكتشاف فيديو يوتيوب، استخدام مشغل يوتيوب');
          }
        } else {
          // For regular audio/video, use the standard initialization
          try {
            PlayerInstances instances;
            if (localPath != null) {
              instances = await ref
                  .read(mediaInitializationServiceProvider)
                  .initializePlayerFromFile(
                    mediaItem,
                    localPath,
                    startPosition: savedPosition,
                    setupAudioListeners: _setupAudioListeners,
                    setupVideoListeners: _setupVideoListeners,
                  );
            } else {
              instances = await ref
                  .read(mediaInitializationServiceProvider)
                  .initializePlayer(mediaItem,
                      startPosition: savedPosition,
                      setupAudioListeners: _setupAudioListeners,
                      setupVideoListeners: _setupVideoListeners);
            }
            _audioPlayer = instances.audioPlayer;
            _videoController = instances.videoController;
            _chewieController = instances.chewieController;
            if (ref.mounted && instances.totalDuration != null) {
              state = state.copyWith(
                  totalDuration: instances.totalDuration!, isLoading: false);
            } else if (ref.mounted) {
              state = state.copyWith(isLoading: false);
            }
          } catch (e) {
            debugPrint('مشغل الوسائط: خطأ في تهيئة المشغل: $e');
            if (ref.mounted) {
              state = state.copyWith(
                errorMessage: 'خطأ في تهيئة المشغل: $e',
                isLoading: false,
              );
            }
          }
        }
      } else if (mediaItem.type.isDocument) {
        state = state.copyWith(
            isDocumentLoaded: false, currentPage: 0, totalPages: 0);
        await loadDocument();
      }
      // Initialize other types like HTML, Tweet if necessary
      if (mediaItem.type == MediaType.html || mediaItem.articleText != null) {
        if (!ref.mounted) {
          return;
        }
        state =
            state.copyWith(articleScrollPosition: 0.0, articleFontSize: 16.0);
      } else if (mediaItem.type == MediaType.tweet ||
          (mediaItem.type == MediaType.text &&
              mediaItem.category == MediaCategory.tweetCategory) ||
          mediaItem.tweetContent != null) {
        if (!ref.mounted) {
          return;
        }
        final bool isFav = await ref.read(isFavoriteProvider(mediaItem.id)
            .future); // Check if this makes sense for tweets
        state = state.copyWith(
          isLiked:
              isFav, // `isLiked` in state should reflect tweet's specific like status if different from general favorite
          likeCount: mediaItem.metadata?.likeCount ?? 0,
          retweetCount: mediaItem.metadata?.retweetCount ?? 0,
        );
      }

      if (ref.mounted) {
        await ref
            .read(historyNotifierProvider.notifier)
            .addToHistory(itemId: mediaId, actionType: ActionType.view);
      }
    } catch (e, s) {
      debugPrint('MediaPlayerController: Error in _loadMedia: $e\n$s');
      if (ref.mounted) {
        state = state.copyWith(
            isLoading: false, errorMessage: 'Failed to load media: $e');
      }
    }
  }

  void _setupAudioListeners(AudioPlayer player,
      {Duration startPosition = Duration.zero}) {
    player.playerStateStream.listen(
      (PlayerState playerStateValue) {
        if (!ref.mounted) {
          return;
        }
        final bool isPlaying = playerStateValue.playing;
        final ProcessingState processingState =
            playerStateValue.processingState;

        state = state.copyWith(
          isPlaying: isPlaying,
          isBuffering: processingState == ProcessingState.buffering ||
              processingState == ProcessingState.loading,
        );

        if (processingState == ProcessingState.completed) {
          _saveCurrentProgress();
          if (state.loopMode == LoopMode.one ||
              state.loopMode == LoopMode.all) {
            player.seek(Duration.zero);
            player.play();
          } else {
            player.seek(Duration.zero);
            // player.pause(); // Already handled by playerState.playing
            state = state.copyWith(
                isPlaying: false,
                currentPosition: player.duration ?? state.totalDuration);
          }
        }
      },
      onError: (Object e, StackTrace s) {
        if (!ref.mounted) {
          return;
        }
        debugPrint('AudioPlayer stream error in _setupAudioListeners: $e\n$s');
        state = state.copyWith(
          isLoading: false, // May not be relevant here, but ensure consistency
          errorMessage: 'Audio player error: $e',
        );
      },
    );

    player.positionStream.listen(
      (Duration position) {
        if (ref.mounted) {
          state = state.copyWith(currentPosition: position);
        }
      },
      onError: (Object e, StackTrace s) {
        debugPrint('AudioPlayer position stream error: $e\n$s');
      },
    );

    player.durationStream.listen(
      (Duration? duration) {
        if (ref.mounted && duration != null) {
          state = state.copyWith(totalDuration: duration);
          // If startPosition was beyond the actual duration, reset or adjust
          if (state.currentPosition > duration) {
            player.seek(Duration.zero); // Or seek to duration itself
            state = state.copyWith(currentPosition: Duration.zero);
          }
        }
      },
      onError: (Object e, StackTrace s) {
        debugPrint('AudioPlayer duration stream error: $e\n$s');
      },
    );
  }

  void _setupVideoListeners(VideoPlayerController controller) {
    controller.addListener(() {
      if (!ref.mounted || !controller.value.isInitialized) {
        return;
      }
      state = state.copyWith(
        currentPosition: controller.value.position,
        isPlaying: controller.value.isPlaying,
        isBuffering: controller.value.isBuffering,
        totalDuration: controller.value.duration,
      );
      if (controller.value.isInitialized &&
          controller.value.position >= controller.value.duration &&
          !controller.value.isPlaying) {
        _saveCurrentProgress();
        if (state.loopMode == LoopMode.one || state.loopMode == LoopMode.all) {
          // Assuming loopMode can apply to video
          controller.seekTo(Duration.zero);
          controller.play();
        } else {
          state = state.copyWith(
              isPlaying: false, currentPosition: controller.value.duration);
        }
      }
    });
  }

  Future<void> playMedia() async {
    if (!ref.mounted || state.mediaItem == null || state.isLoading) {
      return;
    }
    try {
      // Check if it's a YouTube video
      final bool isYoutubeVideo = _isYoutubeUrl(state.mediaItem!.videoUrl);

      if (isYoutubeVideo && state.mediaItem!.type == MediaType.video) {
        // Use the YouTube player controller
        final VideoPlayerNotifier videoNotifier =
            ref.read(videoPlayerNotifierProvider.notifier);
        videoNotifier.playYoutubeVideo();

        if (ref.mounted) {
          state = state.copyWith(isPlaying: true, errorMessage: null);
          await ref.read(historyNotifierProvider.notifier).addToHistory(
              itemId: state.mediaItem!.id, actionType: ActionType.play);
        }
        return;
      }

      // For non-YouTube media
      if (state.mediaItem!.type.isAudioOrVideo &&
          (_audioPlayer == null && _videoController == null)) {
        debugPrint(
            'Players not ready, attempting to re-initialize before play for ${state.mediaItem!.id}');
        await _loadMedia(state.mediaItem!.id);
        if (!ref.mounted ||
            (_audioPlayer == null && _videoController == null)) {
          state = state.copyWith(
              errorMessage: 'Player could not be initialized for playback.');
          return;
        }
      }

      await ref
          .read(mediaPlaybackServiceProvider)
          .play(_audioPlayer, _videoController);
      if (ref.mounted) {
        state = state.copyWith(isPlaying: true, errorMessage: null);
        await ref.read(historyNotifierProvider.notifier).addToHistory(
            itemId: state.mediaItem!.id, actionType: ActionType.play);
      }
    } catch (e, s) {
      debugPrint('Error in playMedia (controller): $e\n$s');
      if (ref.mounted) {
        state = state.copyWith(errorMessage: 'Playback error: $e');
      }
    }
  }

  Future<void> pause() async {
    if (!ref.mounted) {
      return;
    }
    try {
      // Check if it's a YouTube video
      if (state.mediaItem != null) {
        final bool isYoutubeVideo = _isYoutubeUrl(state.mediaItem!.videoUrl);

        if (isYoutubeVideo && state.mediaItem!.type == MediaType.video) {
          // Use the YouTube player controller
          final VideoPlayerNotifier videoNotifier =
              ref.read(videoPlayerNotifierProvider.notifier);
          videoNotifier.pauseYoutubeVideo();

          if (ref.mounted) {
            state = state.copyWith(isPlaying: false);
            await _saveCurrentProgress();
            await ref.read(historyNotifierProvider.notifier).addToHistory(
                itemId: state.mediaItem!.id, actionType: ActionType.pause);
          }
          return;
        }
      }

      // For non-YouTube media
      await ref
          .read(mediaPlaybackServiceProvider)
          .pause(_audioPlayer, _videoController);
      if (ref.mounted) {
        state = state.copyWith(isPlaying: false);
        await _saveCurrentProgress();
        if (state.mediaItem != null) {
          await ref.read(historyNotifierProvider.notifier).addToHistory(
              itemId: state.mediaItem!.id, actionType: ActionType.pause);
        }
      }
    } catch (e, s) {
      debugPrint('Error in pause (controller): $e\n$s');
    }
  }

  void seekTo(Duration position) {
    if (!ref.mounted || state.mediaItem == null) {
      return;
    }
    Duration newPosition = position;
    if (position < Duration.zero) {
      newPosition = Duration.zero;
    } else if (state.totalDuration > Duration.zero &&
        position > state.totalDuration) {
      newPosition = state.totalDuration;
    }

    // Check if it's a YouTube video
    final bool isYoutubeVideo = _isYoutubeUrl(state.mediaItem!.videoUrl);

    if (isYoutubeVideo && state.mediaItem!.type == MediaType.video) {
      // Use the YouTube player controller
      final VideoPlayerNotifier videoNotifier =
          ref.read(videoPlayerNotifierProvider.notifier);
      videoNotifier.seekYoutubeVideo(newPosition);

      if (ref.mounted) {
        state = state.copyWith(currentPosition: newPosition);
        _saveCurrentProgress();
        ref.read(historyNotifierProvider.notifier).addToHistory(
            itemId: state.mediaItem!.id, actionType: ActionType.seek);
      }
      return;
    }

    // For non-YouTube media
    ref
        .read(mediaPlaybackServiceProvider)
        .seekTo(_audioPlayer, _videoController, newPosition);
    if (ref.mounted) {
      state = state.copyWith(currentPosition: newPosition);
      _saveCurrentProgress();
      if (state.mediaItem != null) {
        ref.read(historyNotifierProvider.notifier).addToHistory(
            itemId: state.mediaItem!.id, actionType: ActionType.seek);
      }
    }
  }

  Future<void> seekForward(
      {Duration amount = const Duration(seconds: 15)}) async {
    if (!ref.mounted ||
        state.mediaItem == null ||
        state.totalDuration <= Duration.zero) {
      return;
    }
    Duration newPosition = state.currentPosition + amount;
    if (newPosition > state.totalDuration) {
      newPosition = state.totalDuration;
    }
    seekTo(newPosition);
  }

  Future<void> seekBackward(
      {Duration amount = const Duration(seconds: 15)}) async {
    if (!ref.mounted || state.mediaItem == null) {
      return;
    }
    Duration newPosition = state.currentPosition - amount;
    if (newPosition < Duration.zero) {
      newPosition = Duration.zero;
    }
    seekTo(newPosition);
  }

  Future<void> toggleLoopMode() async {
    if (!ref.mounted || _audioPlayer == null) {
      // LoopMode service is for AudioPlayer
      return;
    }
    final LoopMode currentLoopMode =
        _audioPlayer!.loopMode; // Directly access player's current loop mode
    final LoopMode nextLoopMode = (currentLoopMode == LoopMode.off)
        ? LoopMode.one
        : (currentLoopMode == LoopMode.one ? LoopMode.all : LoopMode.off);
    await ref
        .read(mediaPlaybackServiceProvider)
        .setLoopMode(_audioPlayer, nextLoopMode);
    if (ref.mounted) {
      state = state.copyWith(loopMode: nextLoopMode);
    }
  }

  // Set a specific loop mode
  Future<void> setLoopMode(LoopMode loopMode) async {
    if (_audioPlayer == null) {
      // If there's no audio player, just update the state
      if (ref.mounted) {
        state = state.copyWith(loopMode: loopMode);
      }
      return;
    }

    // Set the loop mode on the audio player
    await ref
        .read(mediaPlaybackServiceProvider)
        .setLoopMode(_audioPlayer, loopMode);

    // Update the state
    if (ref.mounted) {
      state = state.copyWith(loopMode: loopMode);
    }
  }

  // Method to update the playing state from outside (e.g., from YouTube player)
  void updatePlayingState(bool isPlaying) {
    if (ref.mounted) {
      state = state.copyWith(isPlaying: isPlaying);
    }
  }

  Future<void> changeView(int index) async {
    if (!ref.mounted || state.mediaItem == null) {
      return;
    }
    if (state.currentViewIndex == index) {
      return;
    }
    final bool wasPlayingBeforeViewChange = state.isPlaying;
    state = state.copyWith(currentViewIndex: index);

    if (index == 1 && wasPlayingBeforeViewChange) {
      // Assuming index 1 is reading view
      await pause(); // Pause via playback service
    }
  }

  void resetViewIndex() {
    if (!ref.mounted) {
      return;
    }
    state = state.copyWith(currentViewIndex: 0);
  }

  Future<void> toggleFavorite() async {
    if (!ref.mounted || state.mediaItem == null) {
      return;
    }
    final MediaUiModel currentMediaItem = state.mediaItem!;
    try {
      final bool isNowFavorited = await ref
          .read(favoritesNotifierProvider.notifier)
          .toggleFavorite(
              itemId: currentMediaItem.id, type: currentMediaItem.type);
      ref.invalidate(isFavoriteProvider(currentMediaItem.id));
      ref.invalidate(favoritesNotifierProvider);
      if (ref.mounted) {
        state = state.copyWith(
            isLiked: isNowFavorited); // Update local state if needed for UI
        await ref.read(historyNotifierProvider.notifier).addToHistory(
            itemId: currentMediaItem.id,
            actionType:
                isNowFavorited ? ActionType.favorite : ActionType.unfavorite);
      }
    } catch (e, s) {
      debugPrint('MediaPlayerController: Error toggling favorite: $e\n$s');
      if (ref.mounted) {
        state = state.copyWith(
            errorMessage: 'Failed to update favorite status: $e');
      }
    }
  }

  Future<void> shareContent() async {
    if (!ref.mounted || state.mediaItem == null) {
      return;
    }
    try {
      await ref.read(interactionServiceProvider).shareContent(state.mediaItem!);
      if (ref.mounted && state.mediaItem != null) {
        await ref.read(historyNotifierProvider.notifier).addToHistory(
            itemId: state.mediaItem!.id, actionType: ActionType.share);
      }
    } catch (e) {
      if (ref.mounted) {
        state = state.copyWith(errorMessage: 'Failed to share: $e');
      }
    }
  }

  Future<void> _saveCurrentProgress() async {
    if (!ref.mounted ||
        state.mediaItem == null ||
        state.currentPosition.inSeconds < 1) {
      return;
    }
    try {
      await ref.read(progressNotifierProvider.notifier).saveProgress(
          itemId: state.mediaItem!.id,
          positionSeconds: state.currentPosition.inSeconds.toDouble());
      debugPrint(
          'MediaPlayerController: Progress saved at ${state.currentPosition} for ${state.mediaItem!.id}');
    } catch (e, s) {
      debugPrint('MediaPlayerController: Error saving progress: $e\n$s');
    }
  }

  String? _getPrimaryUrl(MediaUiModel mediaItem) {
    switch (mediaItem.type) {
      case MediaType.audio:
        return mediaItem.audioUrl;
      case MediaType.video:
        return mediaItem.videoUrl;
      case MediaType.pdf:
      case MediaType.document:
        return mediaItem.documentUrl;
      default:
        return null;
    }
  }

  /// Checks if a URL is a YouTube URL
  bool _isYoutubeUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }

    return url.contains('youtube.com') ||
        url.contains('youtu.be') ||
        url.contains('youtube-nocookie.com');
  }

  Future<Duration?> _getSavedProgress(String mediaId) async {
    final double? savedProgressSeconds =
        await ref.read(itemProgressProvider(mediaId).future);
    if (savedProgressSeconds != null && savedProgressSeconds > 0) {
      return Duration(seconds: savedProgressSeconds.round());
    }
    return null;
  }

  // --- PDF / Document Methods ---
  Future<void> loadDocument() async {
    if (!ref.mounted ||
        state.mediaItem == null ||
        state.mediaItem!.documentUrl == null) {
      if (ref.mounted) {
        state = state.copyWith(
            documentLoadError: 'No document URL.', isDocumentLoaded: false);
      }
      return;
    }
    if (ref.mounted) {
      state = state.copyWith(isLoading: true, documentLoadError: null);
    }

    final PdfLoadResult result = await ref
        .read(pdfDocumentServiceProvider)
        .loadDocument(state.mediaItem!.documentUrl!,
            onDownloadProgress: (double progress) {
      if (ref.mounted) {
        state = state.copyWith(downloadProgress: progress, isDownloading: true);
      }
    });

    if (ref.mounted) {
      if (result.isSuccess && result.filePath != null) {
        state = state.copyWith(
            isLoading: false,
            isDocumentLoaded: true,
            downloadedFilePath: result.filePath,
            isDownloading: false,
            isPlaying: true, // For PDF, isPlaying means viewer active
            currentPage: 0, // PDFView is 0-indexed
            totalPages: result.totalPages ??
                0 // Use totalPages from result if available
            );
      } else {
        state = state.copyWith(
            isLoading: false,
            isDocumentLoaded: false,
            documentLoadError: result.error ?? 'Failed to load document',
            isDownloading: false);
      }
    }
  }

  void setPdfPageCount(int? pages) {
    if (pages != null && ref.mounted) {
      state = state.copyWith(totalPages: pages);
    }
  }

  void setCurrentPage(int page) {
    // For PDF
    if (ref.mounted) {
      state = state.copyWith(currentPage: page);
    }
  }

  void setPdfViewController(PDFViewController controller) {
    // Called by PDFView widget
    _pdfViewController = controller;
  }

  void setPdfZoom(double zoom) {
    if (ref.mounted) {
      state = state.copyWith(pdfZoom: zoom);
    }
  }

  void setDocumentLoadError(String errorMessage) {
    if (ref.mounted) {
      state = state.copyWith(
          documentLoadError: errorMessage,
          isLoading: false,
          isDocumentLoaded: false);
    }
  }

  Future<void> autoPlayPdf() async {
    if (!ref.mounted ||
        state.mediaItem == null ||
        state.mediaItem!.documentUrl == null) {
      return;
    }
    await loadDocument();
    if (ref.mounted &&
        (state.isDocumentLoaded ?? false) &&
        state.documentLoadError == null) {
      state = state.copyWith(isPlaying: true);
    }
  }

  // --- Tweet Methods ---
  void retweet() {
    if (!ref.mounted || state.mediaItem?.type != MediaType.tweet) {
      return;
    }
    state = state.copyWith(retweetCount: (state.retweetCount ?? 0) + 1);
    // TODO: API call via interactionService if needed
  }

  void toggleTweetLike() {
    if (!ref.mounted || state.mediaItem?.type != MediaType.tweet) {
      return;
    }
    final bool currentIsLiked = state.isLiked ?? false;
    final int currentLikeCount = state.likeCount ?? 0;
    state = state.copyWith(
        isLiked: !currentIsLiked,
        likeCount: !currentIsLiked
            ? currentLikeCount + 1
            : (currentLikeCount > 0 ? currentLikeCount - 1 : 0));
    // TODO: API call via interactionService if needed
  }

  // --- Other General Methods ---
  void setVolume(double volume) {
    if (!ref.mounted) {
      return;
    }
    ref
        .read(mediaPlaybackServiceProvider)
        .setVolume(_audioPlayer, _videoController, volume);
    if (ref.mounted) {
      state = state.copyWith(volume: volume);
    }
  }

  void setPlaybackSpeed(double speed) {
    if (!ref.mounted) {
      return;
    }
    ref
        .read(mediaPlaybackServiceProvider)
        .setSpeed(_audioPlayer, _videoController, speed);
    if (ref.mounted) {
      state = state.copyWith(playbackSpeed: speed);
    }
  }

  Future<void> downloadMedia({String? specificUrl, String? fileType}) async {
    if (!ref.mounted || state.mediaItem == null) {
      state = state.copyWith(errorMessage: 'No media item for download.');
      return;
    }
    state = state.copyWith(
        isDownloading: true, downloadProgress: 0.0, errorMessage: null);

    final String urlToDownload =
        specificUrl ?? _getPrimaryUrl(state.mediaItem!) ?? '';
    if (urlToDownload.isEmpty) {
      if (ref.mounted) {
        state = state.copyWith(
            isDownloading: false, errorMessage: 'No URL to download.');
      }
      return;
    }
    // Create a clean, readable filename based on the media title
    final String effectiveFileName = _generateCleanFileName(
      title: state.mediaItem!.title,
      url: urlToDownload,
      fileType: fileType,
    );

    final ({
      String? error,
      File? fileObject,
      bool isSuccess,
      String? path
    }) result = await ref.read(mediaDownloadServiceProvider).downloadFile(
        urlToDownload, effectiveFileName, onProgress: (double progress) {
      if (ref.mounted) {
        state = state.copyWith(downloadProgress: progress);
      }
    });

    if (ref.mounted) {
      if (result.isSuccess) {
        state = state.copyWith(
            isDownloading: false,
            downloadProgress: 1.0,
            downloadedFilePath: result.path);
        // errorMessage:
        //     result.error ?? 'Download completed: $effectiveFileName');
        if (state.mediaItem != null) {
          await ref.read(historyNotifierProvider.notifier).addToHistory(
              itemId: state.mediaItem!.id, actionType: ActionType.download);
        }
      } else {
        state = state.copyWith(
            isDownloading: false,
            errorMessage: 'Download failed: ${result.error}');
      }
    }
  }

  Future<void> openInBrowser(String url) async {
    if (!ref.mounted) {
      return;
    }
    try {
      await ref.read(interactionServiceProvider).openInBrowser(url);
    } catch (e) {
      if (ref.mounted) {
        state = state.copyWith(errorMessage: 'Could not launch URL: $e');
      }
    }
  }

  Future<void> playNextAudio() async {
    // Basic placeholder
    if (ref.mounted) {
      state = state.copyWith(
          errorMessage: 'Next audio feature not yet implemented.');
    }
    // TODO: Implement logic to find the next audio in the current list/context
    // and then call _loadMedia with the next mediaId.
    // Example:
    // final currentList = await ref.read(mediaUiListProvider.future);
    // final currentIndex = currentList.indexWhere((item) => item.id == state.mediaItem?.id);
    // if (currentIndex != -1 && currentIndex < currentList.length - 1) {
    //   final nextMediaItem = currentList[currentIndex + 1];
    //   if (nextMediaItem.type == MediaType.audio) { // Or any playable type
    //     _loadMedia(nextMediaItem.id);
    //   }
    // }
  }

  /// Clear the error message from state
  void clearErrorMessage() {
    if (ref.mounted) {
      state = state.copyWith(errorMessage: null);
    }
  }

  /// Clear the document error from state
  void clearDocumentError() {
    if (ref.mounted) {
      state = state.copyWith(documentLoadError: null);
    }
  }

  /// Generate a clean, readable filename based on the media title and URL
  String _generateCleanFileName({
    required String title,
    required String url,
    String? fileType,
  }) {
    // Clean the title to make it filesystem-safe
    String cleanTitle = title
        .trim()
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '') // Remove invalid characters
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .replaceAll(' ', '_'); // Replace spaces with underscores

    // Limit title length to avoid filesystem issues
    if (cleanTitle.length > 50) {
      cleanTitle = cleanTitle.substring(0, 50);
    }

    // Get file extension from URL or fileType
    String extension = '';

    if (fileType != null && fileType.isNotEmpty) {
      // Use provided fileType
      extension = fileType.startsWith('.') ? fileType : '.$fileType';
    } else {
      // Extract extension from URL
      final String urlFileName = url.split('/').last.split('?').first;
      final int dotIndex = urlFileName.lastIndexOf('.');
      if (dotIndex != -1 && dotIndex < urlFileName.length - 1) {
        extension = urlFileName.substring(dotIndex);
      }
    }

    // If no extension found, try to determine from URL or use default
    if (extension.isEmpty) {
      if (url.contains('pdf')) {
        extension = '.pdf';
      } else if (url.contains('docx')) {
        extension = '.docx';
      } else if (url.contains('mp3') || url.contains('audio')) {
        extension = '.mp3';
      } else if (url.contains('mp4') || url.contains('video')) {
        extension = '.mp4';
      } else {
        extension = '.file'; // Default extension
      }
    }

    return '$cleanTitle$extension';
  }

  // Getters (optional, if UI widgets directly need these for advanced control)
  AudioPlayer? get audioPlayerInstance => _audioPlayer;
  ChewieController? get chewieControllerInstance => _chewieController;
}

// Keep mediaItemDetailsProvider
@riverpod
MediaUiModel? mediaItemDetails(Ref ref, String mediaId) {
  final AsyncValue<List<MediaUiModel>> asyncMediaItems =
      ref.watch(mediaUiListProvider);

  return asyncMediaItems.when(
    data: (List<MediaUiModel> items) {
      return items.firstWhereOrNull((MediaUiModel item) => item.id == mediaId);
    },
    loading: () => null,
    error: (Object err, StackTrace stack) {
      debugPrint('Error in mediaItemDetailsProvider: $err');
      return null;
    },
  );
}
