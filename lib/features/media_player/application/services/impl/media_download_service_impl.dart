import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path_helper;
import 'package:path_provider/path_provider.dart';

import '../interfaces/i_media_download_service.dart';

class MediaDownloadServiceImpl implements IMediaDownloadService {
  MediaDownloadServiceImpl({Dio? dio}) : _dio = dio ?? Dio() {
    if (dio == null) {
      // Only add interceptor if we created the Dio instance
      _dio.interceptors.add(RetryInterceptor(dio: _dio, logPrint: debugPrint));
    }
  }
  final Dio _dio;

  @override
  Future<String> getDownloadsPath() async {
    try {
      Directory? directory;
      if (Platform.isAndroid) {
        // Use app-specific external storage directory for Android
        // This avoids scoped storage issues on Android 10+ (API 29+)
        directory = await getExternalStorageDirectory();
        if (directory != null) {
          // Create a Downloads subfolder within the app's external storage
          final String downloadPath =
              path_helper.join(directory.path, 'Downloads');
          final Directory downloadDir = Directory(downloadPath);
          if (!downloadDir.existsSync()) {
            await downloadDir.create(recursive: true);
          }
          return downloadPath;
        }
        // Fallback to app documents directory if external storage is not available
        directory = await getApplicationDocumentsDirectory();
        final String downloadPath =
            path_helper.join(directory.path, 'Downloads');
        final Directory downloadDir = Directory(downloadPath);
        if (!downloadDir.existsSync()) {
          await downloadDir.create(recursive: true);
        }
        return downloadPath;
      } else if (Platform.isIOS) {
        // For iOS, use app documents directory with Downloads subfolder
        directory = await getApplicationDocumentsDirectory();
        final String downloadPath =
            path_helper.join(directory.path, 'Downloads');
        final Directory downloadDir = Directory(downloadPath);
        if (!downloadDir.existsSync()) {
          await downloadDir.create(recursive: true);
        }
        return downloadPath;
      }

      // Fallback for other platforms
      directory ??= await getApplicationDocumentsDirectory();
      final String downloadPath = path_helper.join(directory.path, 'Downloads');
      final Directory downloadDir = Directory(downloadPath);
      if (!downloadDir.existsSync()) {
        await downloadDir.create(recursive: true);
      }
      return downloadPath;
    } catch (e) {
      debugPrint('Error getting downloads path: $e');
      // Final fallback to app documents directory
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String downloadPath = path_helper.join(appDocDir.path, 'Downloads');
      final Directory downloadDir = Directory(downloadPath);
      if (!downloadDir.existsSync()) {
        await downloadDir.create(recursive: true);
      }
      return downloadPath;
    }
  }

  @override
  Future<String?> getDownloadedFilePath(String url) async {
    // ... (Your getDownloadedFilePath logic) ...
    try {
      final String downloadPath = await getDownloadsPath();
      final String fileName = url.split('/').last;
      final String filePath = path_helper.join(downloadPath, fileName);
      final File file = File(filePath);
      if (file.existsSync()) {
        return filePath;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting downloaded file path: $e');
      return null;
    }
  }

  @override
  Future<({bool isSuccess, String? path, String? error, File? fileObject})>
      downloadFile(
    String url,
    String effectiveFileName, {
    void Function(double progress)? onProgress,
  }) async {
    try {
      final String downloadPath = await getDownloadsPath();
      final String filePath = path_helper.join(downloadPath, effectiveFileName);
      final File file = File(filePath);

      if (file.existsSync()) {
        // Use await
        return (
          isSuccess: true,
          path: filePath,
          error: 'File already downloaded',
          fileObject: file
        );
      }

      await _dio.download(
        url,
        filePath,
        onReceiveProgress: (int received, int total) {
          if (total != -1 && onProgress != null) {
            onProgress(received / total);
          }
        },
      );
      return (isSuccess: true, path: filePath, error: null, fileObject: file);
    } catch (e) {
      debugPrint('Download error in service: $e');
      return (
        isSuccess: false,
        path: null,
        error: e.toString(),
        fileObject: null
      );
    }
  }
}
