import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';

import '../../../../../data/enums/media_type_enum.dart';
import '../../../../../data/models/media_ui_model.dart';
import '../interfaces/i_media_initialization_service.dart';

class MediaInitializationServiceImpl implements IMediaInitializationService {
  @override
  Future<PlayerInstances> initializePlayer(
    MediaUiModel mediaItem, {
    Duration startPosition = Duration.zero,
    required void Function(AudioPlayer player, {Duration startPosition})
        setupAudioListeners,
    required void Function(VideoPlayerController controller)
        setupVideoListeners,
  }) async {
    AudioPlayer? audioPlayer;
    VideoPlayerController? videoController;
    ChewieController? chewieController;
    Duration? totalDuration;

    final String? primaryUrl = _getPrimaryUrlFromMediaItem(mediaItem);
    if (primaryUrl == null || primaryUrl.isEmpty) {
      throw Exception(
          'No primary URL available for media item: ${mediaItem.title}');
    }
    final String secureUrl = primaryUrl.startsWith('http:')
        ? primaryUrl.replaceFirst('http:', 'https:')
        : primaryUrl;

    try {
      if (mediaItem.type == MediaType.audio) {
        audioPlayer = AudioPlayer();
        setupAudioListeners(audioPlayer, startPosition: startPosition);
        await audioPlayer.setUrl(secureUrl, headers: <String, String>{
          'User-Agent': 'Mozilla/5.0',
          'Accept': '*/*'
        });
        await audioPlayer.load();
        totalDuration = audioPlayer.duration;
      } else if (mediaItem.type == MediaType.video) {
        videoController = VideoPlayerController.networkUrl(Uri.parse(secureUrl),
            httpHeaders: <String, String>{
              'User-Agent': 'Mozilla/5.0',
              'Accept': '*/*'
            });
        await videoController.initialize();
        totalDuration = videoController.value.duration;
        chewieController = ChewieController(
          videoPlayerController: videoController,
          aspectRatio: videoController.value.aspectRatio,
          progressIndicatorDelay:
              Platform.isAndroid ? const Duration(days: 1) : null,
          startAt:
              (startPosition < totalDuration && startPosition > Duration.zero)
                  ? startPosition
                  : null,
          placeholder: const Center(child: CircularProgressIndicator()),
          errorBuilder: (BuildContext context, String errorMessage) => Center(
            child: Text(errorMessage),
          ),
        );
        setupVideoListeners(videoController);
      }
      return PlayerInstances(
          audioPlayer: audioPlayer,
          videoController: videoController,
          chewieController: chewieController,
          totalDuration: totalDuration);
    } catch (e, s) {
      debugPrint('Error initializing player from URL in service: $e\n$s');
      // Dispose if partially initialized
      audioPlayer?.dispose();
      videoController?.dispose();
      chewieController?.dispose();
      rethrow;
    }
  }

  @override
  Future<PlayerInstances> initializePlayerFromFile(
    MediaUiModel mediaItem,
    String filePath, {
    Duration startPosition = Duration.zero,
    required void Function(AudioPlayer player, {Duration startPosition})
        setupAudioListeners,
    required void Function(VideoPlayerController controller)
        setupVideoListeners,
  }) async {
    AudioPlayer? audioPlayer;
    VideoPlayerController? videoController;
    ChewieController? chewieController;
    Duration? totalDuration;

    try {
      if (mediaItem.type == MediaType.audio) {
        audioPlayer = AudioPlayer();
        setupAudioListeners(audioPlayer, startPosition: startPosition);
        await audioPlayer.setFilePath(filePath);
        await audioPlayer.load();
        totalDuration = audioPlayer.duration;
      } else if (mediaItem.type == MediaType.video) {
        videoController = VideoPlayerController.file(File(filePath));
        await videoController.initialize();
        totalDuration = videoController.value.duration;
        chewieController = ChewieController(
          videoPlayerController: videoController,
          aspectRatio: videoController.value.aspectRatio,
          startAt:
              (startPosition < totalDuration && startPosition > Duration.zero)
                  ? startPosition
                  : null,
        );
        setupVideoListeners(videoController);
      }
      return PlayerInstances(
        audioPlayer: audioPlayer,
        videoController: videoController,
        chewieController: chewieController,
        totalDuration: totalDuration,
      );
    } catch (e, s) {
      debugPrint('Error initializing player from file in service: $e\n$s');
      audioPlayer?.dispose();
      videoController?.dispose();
      chewieController?.dispose();
      rethrow;
    }
  }

  @override
  void disposePlayers({
    AudioPlayer? audioPlayer,
    VideoPlayerController? videoController,
    ChewieController? chewieController,
  }) {
    audioPlayer?.dispose();
    videoController?.dispose();
    chewieController?.dispose();
  }

  String? _getPrimaryUrlFromMediaItem(MediaUiModel mediaItem) {
    switch (mediaItem.type) {
      case MediaType.audio:
        return mediaItem.audioUrl;
      case MediaType.video:
        return mediaItem.videoUrl;
      default:
        return null;
    }
  }
}
