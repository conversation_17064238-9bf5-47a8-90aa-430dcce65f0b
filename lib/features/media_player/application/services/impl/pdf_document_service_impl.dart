import 'dart:io';

import 'package:flutter/foundation.dart';

import '../interfaces/i_media_download_service.dart'; // To use for downloading
import '../interfaces/i_pdf_document_service.dart';

class PdfDocumentServiceImpl implements IPdfDocumentService {
  PdfDocumentServiceImpl(this._downloadService);
  final IMediaDownloadService _downloadService;

  @override
  Future<PdfLoadResult> loadDocument(
    String documentUrl, {
    void Function(double progress)? onDownloadProgress,
  }) async {
    try {
      final String fileName = documentUrl.split('/').last;
      final ({
        String? error,
        File? fileObject,
        bool isSuccess,
        String? path
      }) downloadResult = await _downloadService.downloadFile(
        documentUrl,
        fileName,
        onProgress: onDownloadProgress,
      );

      if (downloadResult.isSuccess && downloadResult.path != null) {
        // For PDFView, page count is usually determined by the widget after loading the path.
        // This service primarily ensures the file is available.
        return PdfLoadResult(filePath: downloadResult.path, isSuccess: true);
      } else {
        return PdfLoadResult(
            error: downloadResult.error ?? 'Failed to download PDF document.');
      }
    } catch (e) {
      debugPrint('Error loading PDF document in service: $e');
      return PdfLoadResult(error: e.toString());
    }
  }
}
