import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';

import '../interfaces/i_media_playback_service.dart';

class MediaPlaybackServiceImpl implements IMediaPlaybackService {
  @override
  Future<void> play(
      AudioPlayer? audioPlayer, VideoPlayerController? videoController) async {
    if (audioPlayer != null) {
      await audioPlayer.play();
    }
    if (videoController != null) {
      await videoController.play();
    }
  }

  @override
  Future<void> pause(
      AudioPlayer? audioPlayer, VideoPlayerController? videoController) async {
    if (audioPlayer != null) {
      await audioPlayer.pause();
    }
    if (videoController != null) {
      await videoController.pause();
    }
  }

  @override
  Future<void> seekTo(AudioPlayer? audioPlayer,
      VideoPlayerController? videoController, Duration position) async {
    if (audioPlayer != null) {
      await audioPlayer.seek(position);
    }
    if (videoController != null) {
      await videoController.seekTo(position);
    }
  }

  @override
  Future<void> setVolume(AudioPlayer? audioPlayer,
      VideoPlayerController? videoController, double volume) async {
    if (audioPlayer != null)
      await audioPlayer.setVolume(volume.clamp(0.0, 1.0));
    if (videoController != null)
      await videoController.setVolume(volume.clamp(0.0, 1.0));
  }

  @override
  Future<void> setSpeed(AudioPlayer? audioPlayer,
      VideoPlayerController? videoController, double speed) async {
    if (audioPlayer != null) {
      await audioPlayer.setSpeed(speed);
    }
    if (videoController != null) {
      await videoController.setPlaybackSpeed(speed);
    }
  }

  @override
  Future<void> setLoopMode(AudioPlayer? audioPlayer, LoopMode loopMode) async {
    await audioPlayer?.setLoopMode(loopMode);
  }
}
