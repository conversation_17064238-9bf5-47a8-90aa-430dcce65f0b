import 'package:flutter/foundation.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../data/models/media_ui_model.dart';
import '../interfaces/i_interaction_service.dart';

class InteractionServiceImpl implements IInteractionService {
  @override
  Future<void> shareContent(MediaUiModel mediaItem) async {
    try {
      final StringBuffer shareText = StringBuffer();
      shareText.writeln(mediaItem.title);
      if (mediaItem.description != null && mediaItem.description!.isNotEmpty) {
        shareText.writeln('\n${mediaItem.description}');
      }
      // Add relevant URLs if needed
      shareText.writeln('\nShared from Dr. Alfarih App.'); // Customize
      await SharePlus.instance.share(
          ShareParams(text: shareText.toString(), subject: mediaItem.title));
    } catch (e) {
      debugPrint('Share error in service: $e');
      rethrow; // Or handle more gracefully
    }
  }

  @override
  Future<void> openInBrowser(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $url in service');
      throw 'Could not launch $url';
    }
  }
}
