import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';

abstract class IMediaPlaybackService {
  Future<void> play(
      AudioPlayer? audioPlayer, VideoPlayerController? videoController);
  Future<void> pause(
      AudioPlayer? audioPlayer, VideoPlayerController? videoController);
  Future<void> seekTo(AudioPlayer? audioPlayer,
      VideoPlayerController? videoController, Duration position);
  Future<void> setVolume(AudioPlayer? audioPlayer,
      VideoPlayerController? videoController, double volume);
  Future<void> setSpeed(AudioPlayer? audioPlayer,
      VideoPlayerController? videoController, double speed);
  Future<void> setLoopMode(
      AudioPlayer? audioPlayer, LoopMode loopMode); // Primarily for audio
}
