import 'package:chewie/chewie.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';

import '../../../../../data/models/media_ui_model.dart'; // Adjust import path

// Helper class to bundle player instances
class PlayerInstances {
  PlayerInstances({
    this.audioPlayer,
    this.videoController,
    this.chewieController,
    this.totalDuration,
  });
  final AudioPlayer? audioPlayer;
  final VideoPlayerController? videoController;
  final ChewieController? chewieController;
  final Duration? totalDuration;
}

abstract class IMediaInitializationService {
  Future<PlayerInstances> initializePlayer(
    MediaUiModel mediaItem, {
    Duration startPosition = Duration.zero,
    required void Function(AudioPlayer player, {Duration startPosition})
        setupAudioListeners,
    required void Function(VideoPlayerController controller)
        setupVideoListeners,
  });

  Future<PlayerInstances> initializePlayerFromFile(
    MediaUiModel mediaItem,
    String filePath, {
    Duration startPosition = Duration.zero,
    required void Function(AudioPlayer player, {Duration startPosition})
        setupAudioListeners,
    required void Function(VideoPlayerController controller)
        setupVideoListeners,
  });

  void disposePlayers({
    AudioPlayer? audioPlayer,
    VideoPlayerController? videoController,
    ChewieController? chewieController,
  });
}
