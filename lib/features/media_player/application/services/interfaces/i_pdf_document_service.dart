// Helper class for PDF load result
class PdfLoadResult {
  PdfLoadResult(
      {this.filePath, this.totalPages, this.isSuccess = false, this.error});
  final String? filePath;
  final int? totalPages; // PDFView might provide this async
  final bool isSuccess;
  final String? error;
}

abstract class IPdfDocumentService {
  Future<PdfLoadResult> loadDocument(
    String documentUrl, {
    void Function(double progress)? onDownloadProgress,
  });
  // PDFViewController interactions are often handled by the PDFView widget itself.
  // The controller might just update state based on callbacks from the widget.
}
