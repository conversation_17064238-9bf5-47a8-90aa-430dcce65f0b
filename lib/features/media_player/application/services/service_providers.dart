import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'impl/interaction_service_impl.dart';
import 'impl/media_download_service_impl.dart';
import 'impl/media_initialization_service_impl.dart';
import 'impl/media_playback_service_impl.dart';
import 'impl/pdf_document_service_impl.dart';
import 'interfaces/i_interaction_service.dart';
import 'interfaces/i_media_download_service.dart';
import 'interfaces/i_media_initialization_service.dart';
import 'interfaces/i_media_playback_service.dart';
import 'interfaces/i_pdf_document_service.dart';

part 'service_providers.g.dart'; // You'll need to run build_runner for this

@riverpod
IMediaDownloadService mediaDownloadService(Ref ref) {
  return MediaDownloadServiceImpl(); // Can inject Dio if needed: MediaDownloadServiceImpl(dio: ref.watch(dioProvider))
}

@riverpod
IMediaInitializationService mediaInitializationService(Ref ref) {
  return MediaInitializationServiceImpl();
}

@riverpod
IMediaPlaybackService mediaPlaybackService(Ref ref) {
  return MediaPlaybackServiceImpl();
}

@riverpod
IPdfDocumentService pdfDocumentService(Ref ref) {
  // PdfDocumentServiceImpl depends on IMediaDownloadService
  return PdfDocumentServiceImpl(ref.watch(mediaDownloadServiceProvider));
}

@riverpod
IInteractionService interactionService(Ref ref) {
  return InteractionServiceImpl();
}
