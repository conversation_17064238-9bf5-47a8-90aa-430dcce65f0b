import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:just_audio/just_audio.dart' hide PlayerState;
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../../data/models/media_player_state.dart';
import '../../../surah_player/presentation/widgets/audio_download_dialog.dart';
import '../../application/media_player_controller.dart';
import '../../application/video_player_provider.dart';

/// A shared control bar for both audio and video players
class MediaControlBar extends ConsumerWidget {
  const MediaControlBar({
    super.key,
    required this.mediaId,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerController playerNotifier =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);
    final MediaPlayerState playerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));

    // Check if this is a YouTube video
    final bool isYoutubeVideo = playerState.mediaItem?.videoUrl != null &&
        (playerState.mediaItem!.videoUrl!.contains('youtube.com') ||
            playerState.mediaItem!.videoUrl!.contains('youtu.be'));

    // Register this mediaId with the VideoPlayerNotifier if it's a YouTube video
    if (isYoutubeVideo) {
      // This ensures the VideoPlayerNotifier knows about this mediaId
      ref.read(videoPlayerNotifierProvider.notifier).registerMediaId(mediaId);
    }

    // Watch the VideoPlayerState to get YouTube-specific state
    final VideoPlayerState videoPlayerState =
        ref.watch(videoPlayerNotifierProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: <Widget>[
          _actionButton(context, Ionicons.speedometer_outline, 'السرعة', () {
            _showSpeedDialog(context, playerNotifier, ref);
          }),
          _actionButton(context, Ionicons.timer_outline, 'المؤقت', () {
            _showSleepTimerDialog(context, playerNotifier, ref);
          }),
          _actionButton(
            context,
            playerState.loopMode == LoopMode.one
                ? Ionicons.repeat_outline
                : playerState.loopMode == LoopMode.all
                    ? Ionicons.repeat_outline
                    : Ionicons.repeat_outline,
            'تكرار',
            () {
              if (isYoutubeVideo &&
                  videoPlayerState.youtubeController != null) {
                // Toggle YouTube looping using our custom implementation
                ref
                    .read(videoPlayerNotifierProvider.notifier)
                    .toggleYoutubeLoop();

                // Update the MediaPlayerController state to reflect the change in loop mode
                // This ensures the UI shows the correct loop state
                final bool isLooping = ref
                    .read(videoPlayerNotifierProvider.notifier)
                    .shouldLoopYoutubeVideo();

                // Toggle between LoopMode.off and LoopMode.one to match our YouTube looping state
                if (isLooping && playerState.loopMode == LoopMode.off) {
                  playerNotifier.setLoopMode(LoopMode.one);
                } else if (!isLooping && playerState.loopMode != LoopMode.off) {
                  playerNotifier.setLoopMode(LoopMode.off);
                }

                // Show a message to the user
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        isLooping ? 'تم تفعيل التكرار' : 'تم إلغاء التكرار'),
                    duration: const Duration(seconds: 1),
                  ),
                );
              } else {
                // For regular audio/video, use the MediaPlayerController
                playerNotifier.toggleLoopMode();
              }
            },
            color: playerState.loopMode != LoopMode.off ||
                    (isYoutubeVideo &&
                        ref
                            .read(videoPlayerNotifierProvider.notifier)
                            .shouldLoopYoutubeVideo())
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          _actionButton(context, Ionicons.library_outline, 'المكتبة', () {
            showAudioDownloadDialog(context, mediaId);
          }),
        ],
      ),
    );
  }

  Widget _actionButton(
      BuildContext context, IconData icon, String label, VoidCallback onPressed,
      {Color? color}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        IconButton(
          icon: Icon(icon,
              color: color ?? Theme.of(context).colorScheme.onSurfaceVariant),
          onPressed: onPressed,
          iconSize: 24,
        ),
        Text(label,
            style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).colorScheme.onSurfaceVariant)),
      ],
    );
  }

  void _showSpeedDialog(BuildContext context,
      MediaPlayerController playerNotifier, WidgetRef ref) {
    final List<double> speeds = <double>[0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

    // Get the MediaPlayerState using ref.read
    final MediaPlayerState playerState =
        ref.read(mediaPlayerControllerProvider(mediaId));
    final bool isYoutubeVideo = playerState.mediaItem?.videoUrl != null &&
        (playerState.mediaItem!.videoUrl!.contains('youtube.com') ||
            playerState.mediaItem!.videoUrl!.contains('youtu.be'));

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('اختر سرعة التشغيل'),
          content: SizedBox(
            width: double.minPositive,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: speeds.length,
              itemBuilder: (BuildContext context, int index) {
                final double speed = speeds[index];
                return ListTile(
                  title: Text('${speed}x'),
                  onTap: () {
                    if (isYoutubeVideo) {
                      // For YouTube videos, we need to use the YouTube player controller
                      // Get the YouTube controller from the VideoPlayerState
                      final YoutubePlayerController? controller = ref
                          .read(videoPlayerNotifierProvider)
                          .youtubeController;

                      if (controller != null) {
                        // YouTube only supports specific playback rates
                        final double youtubeSpeed = speed;
                        controller.setPlaybackRate(youtubeSpeed);
                      }
                    } else {
                      // For regular audio/video, use the MediaPlayerController
                      playerNotifier.setPlaybackSpeed(speed);
                    }
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _showSleepTimerDialog(BuildContext context,
      MediaPlayerController playerNotifier, WidgetRef ref) {
    final List<int> timerOptions = <int>[1, 10, 15, 30, 45, 60, 90];

    // Get the MediaPlayerState using ref.read
    final MediaPlayerState playerState =
        ref.read(mediaPlayerControllerProvider(mediaId));
    final bool isYoutubeVideo = playerState.mediaItem?.videoUrl != null &&
        (playerState.mediaItem!.videoUrl!.contains('youtube.com') ||
            playerState.mediaItem!.videoUrl!.contains('youtu.be'));

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('مؤقت النوم'),
          content: SizedBox(
            width: double.minPositive,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount:
                  timerOptions.length + 1, // +1 for "Cancel timer" option
              itemBuilder: (BuildContext context, int index) {
                if (index == timerOptions.length) {
                  // Last item is "Cancel timer"
                  return ListTile(
                    title: const Text('إلغاء المؤقت'),
                    onTap: () {
                      // Show a message that timer is canceled
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إلغاء المؤقت'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                      Navigator.of(context).pop();
                    },
                  );
                }

                final int minutes = timerOptions[index];
                return ListTile(
                  title: Text('$minutes دقيقة'),
                  onTap: () {
                    // Store the mediaId for later use
                    final String currentMediaId = mediaId;

                    // Store the current WidgetRef for later use
                    final WidgetRef currentRef = ref;

                    // Set a timer to stop playback after the selected duration
                    Future<void>.delayed(Duration(minutes: minutes), () {
                      try {
                        if (isYoutubeVideo) {
                          // For YouTube videos, use the VideoPlayerNotifier
                          try {
                            // Try to pause the YouTube video
                            currentRef
                                .read(videoPlayerNotifierProvider.notifier)
                                .pauseYoutubeVideo();

                            // Also update the MediaPlayerController state
                            try {
                              currentRef
                                  .read(mediaPlayerControllerProvider(
                                          currentMediaId)
                                      .notifier)
                                  .updatePlayingState(false);
                            } catch (e) {
                              debugPrint(
                                  'Could not update MediaPlayerController: $e');
                            }
                          } catch (e) {
                            debugPrint('Could not pause YouTube video: $e');
                          }
                        } else {
                          // For regular audio/video, use the MediaPlayerController
                          try {
                            currentRef
                                .read(mediaPlayerControllerProvider(
                                        currentMediaId)
                                    .notifier)
                                .pause();
                          } catch (e) {
                            debugPrint(
                                'Could not pause MediaPlayerController: $e');
                          }
                        }
                      } catch (e) {
                        debugPrint('Error in sleep timer: $e');
                      }
                    });

                    // Show a confirmation message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم ضبط المؤقت لـ $minutes دقيقة'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}
