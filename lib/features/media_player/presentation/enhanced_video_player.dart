// lib/features/media_player/presentation/enhanced_video_player.dart
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../data/enums/video_url_types_enum.dart';
import '../../../data/models/media_player_state.dart'; // For MediaPlayerState
import '../../../utils/context_extensions.dart';
import '../../home/<USER>/media_card_components.dart'; // For MediaThumbnail
import '../application/media_player_controller.dart'; // For MediaPlayerController
import '../application/video_player_provider.dart'; // For VideoPlayerState, VideoPlayerNotifier
import './widgets/media_control_bar.dart';

class EnhancedVideoPlayer extends HookConsumerWidget {
  const EnhancedVideoPlayer({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final IsMounted mounted =
        useIsMounted(); // Hook to check if widget is mounted

    // Initialize player function
    Future<void> initializePlayer() async {
      if (!mounted()) {
        return;
      }

      // Ensure mediaItem is loaded in MediaPlayerController first
      // This might involve watching mediaPlayerControllerProvider(mediaId)
      // and waiting for mediaItem to be non-null.
      final MediaPlayerState mediaState =
          ref.read(mediaPlayerControllerProvider(mediaId));
      if (mediaState.mediaItem == null) {
        if (mediaState.isLoading) {
          debugPrint(
              'مشغل الفيديو المحسن: انتظار تحميل عنصر الوسائط لـ $mediaId');
        } else if (mediaState.errorMessage != null) {
          if (mounted()) {
            // Good practice to check mounted before async ref interaction
            ref.read(videoPlayerNotifierProvider.notifier).setError(
                'فشل في تحميل تفاصيل الوسائط: ${mediaState.errorMessage}');
          }
        } else {
          if (mounted()) {
            ref.read(videoPlayerNotifierProvider.notifier).setError(
                'لم يتم العثور على عنصر الوسائط لـ $mediaId لتهيئة الفيديو.');
          }
        }
        return;
      }

      final String? videoUrl = mediaState.mediaItem!.videoUrl;
      if (!mounted()) {
        // Good check
        return;
      }

      final VideoPlayerNotifier videoNotifier =
          ref.read(videoPlayerNotifierProvider.notifier);
      videoNotifier.setVideoUrl(videoUrl);
      videoNotifier.registerMediaId(mediaId);
    }

    useEffect(() {
      // Get the notifier instance when the effect is set up
      final VideoPlayerNotifier videoNotifier =
          ref.read(videoPlayerNotifierProvider.notifier);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted()) {
          // Check if still mounted before initializing
          initializePlayer();
        }
      });

      // Return the cleanup function
      return () {
        // This cleanup function now uses the 'videoNotifier' instance captured earlier.
        // It does not call 'ref.read' or 'ref.watch' during dispose.
        // Also, ensure the widget is still considered "active" in some sense, or the notifier method itself is safe to call.
        // If videoNotifier might also be disposed, this could still be an issue, but the immediate "ref" error is avoided.
        // Given VideoPlayerNotifier is autoDispose, it might be disposed if no longer listened to.
        // However, the unregisterMediaId operates on a static list in your current code.
        videoNotifier.unregisterMediaId(mediaId);
      };
    }, <Object?>[
      mediaId
    ]); // Only 'mediaId' as a dependency for the effect itself.

    final VideoPlayerState videoPlayerState =
        ref.watch(videoPlayerNotifierProvider);

    if (videoPlayerState.isLoading &&
        videoPlayerState.youtubeController == null &&
        videoPlayerState.webViewController == null) {
      return const Center(child: CircularProgressIndicator());
    }

    // Only show error if we don't have a controller yet or if it's not a YouTube video
    // This prevents showing errors during YouTube initialization when audio might still work
    if (videoPlayerState.errorMessage != null &&
        (videoPlayerState.videoType != VideoPlayerType.youtube ||
            videoPlayerState.youtubeController == null)) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'خطأ: ${videoPlayerState.errorMessage}',
            textAlign: TextAlign.center,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ),
      );
    }

    switch (videoPlayerState.videoType) {
      case VideoPlayerType.direct:
        return _buildDirectVideoPlayer(context, ref);
      case VideoPlayerType.youtube:
        return _buildYoutubePlayer(
            context,
            ref,
            videoPlayerState,
            ref.read(
                videoPlayerNotifierProvider.notifier)); // Pass the notifier
      case VideoPlayerType.facebook:
      case VideoPlayerType.tiktok:
        return _buildWebViewPlayer(context, ref, videoPlayerState);
      case VideoPlayerType.unknown:
        return Center(
          child: Text(
            videoPlayerState.videoUrl == null ||
                    videoPlayerState.videoUrl!.isEmpty
                ? 'لم يتم توفير رابط الفيديو.'
                : 'نوع فيديو أو رابط غير مدعوم: ${videoPlayerState.videoUrl}',
            textAlign: TextAlign.center,
          ),
        );
    }
  }

  Widget _buildDirectVideoPlayer(BuildContext context, WidgetRef ref) {
    final ThemeData customTheme = context.theme;
    final ChewieController? chewieController = ref
        .watch(mediaPlayerControllerProvider(mediaId).notifier)
        .chewieControllerInstance;
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final String? videoThumbnailUrl = mediaPlayerState.mediaItem?.thumbnailUrl;
    final Size size = MediaQuery.sizeOf(context);

    if (chewieController == null ||
        !chewieController.videoPlayerController.value.isInitialized) {
      return Center(
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            if (videoThumbnailUrl != null)
              MediaThumbnail(
                thumbnailUrl: videoThumbnailUrl,
                fallbackIcon: Icons.video_library,
                width: size.width,
                height: 200, // Fixed height for placeholder
              ),
            const CircularProgressIndicator(),
          ],
        ),
      );
    }

    return Column(
      children: <Widget>[
        Container(
          color: Colors.black,
          child: AspectRatio(
            aspectRatio:
                chewieController.videoPlayerController.value.aspectRatio,
            child: Theme(
              data: customTheme.copyWith(platform: TargetPlatform.iOS),
              child: Chewie(controller: chewieController),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildYoutubePlayer(BuildContext context, WidgetRef ref,
      VideoPlayerState videoPlayerState, VideoPlayerNotifier videoNotifier) {
    final YoutubePlayerController? youtubeController =
        videoPlayerState.youtubeController;
    final String? youtubeId = videoPlayerState.youtubeId;

    if (youtubeController == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return YoutubePlayerBuilder(
      player: YoutubePlayer(
        controller: youtubeController,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Theme.of(context).colorScheme.primary,
        progressColors: ProgressBarColors(
          playedColor: Theme.of(context).colorScheme.primary,
          handleColor: Theme.of(context).colorScheme.secondary,
          bufferedColor:
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
          backgroundColor: Colors.grey.shade700,
        ),
        onReady: () {
          debugPrint(
              'مشغل الفيديو المحسن: مشغل يوتيوب جاهز للمعرف: $youtubeId');
        },
        onEnded: (YoutubeMetaData metaData) {
          debugPrint(
              'مشغل الفيديو المحسن: انتهى فيديو يوتيوب: ${metaData.title}');
          // Potentially notify MediaPlayerController to handle 'ended' state
        },
      ),
      builder: (BuildContext context, Widget player) {
        return Material(
          // Added Material for theming context of dialogs
          color: Colors.black,
          child: Column(
            mainAxisAlignment:
                MainAxisAlignment.center, // Center player vertically
            children: <Widget>[
              AspectRatio(
                // Constrain aspect ratio for the player
                aspectRatio: 16 / 9, // Common video aspect ratio
                child: player,
              ),
              // Add the shared control bar
            ],
          ),
        );
      },
    );
  }

  Widget _buildYoutubeCustomControls(
    BuildContext context,
    YoutubePlayerController youtubeController,
    String? youtubeId,
    WidgetRef ref,
  ) {
    return ValueListenableBuilder<YoutubePlayerValue>(
      valueListenable: youtubeController,
      builder: (BuildContext context, YoutubePlayerValue value, Widget? child) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
          color: Colors.black.withValues(alpha: 0.85),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.replay_10, color: Colors.white),
                    onPressed: () {
                      final Duration newPosition =
                          value.position - const Duration(seconds: 10);
                      youtubeController.seekTo(newPosition < Duration.zero
                          ? Duration.zero
                          : newPosition);
                    },
                    tooltip: 'إرجاع 10 ثوان',
                  ),
                  IconButton(
                    icon: Icon(
                      value.isPlaying
                          ? Icons.pause_circle_filled_outlined
                          : Icons.play_circle_filled_outlined,
                      color: Colors.white,
                      size: 40,
                    ),
                    onPressed: () => value.isPlaying
                        ? youtubeController.pause()
                        : youtubeController.play(),
                    tooltip: value.isPlaying ? 'إيقاف مؤقت' : 'تشغيل',
                  ),
                  IconButton(
                    icon: const Icon(Icons.forward_10, color: Colors.white),
                    onPressed: () {
                      final Duration newPosition =
                          value.position + const Duration(seconds: 10);
                      final Duration duration =
                          youtubeController.metadata.duration;
                      youtubeController.seekTo(
                          newPosition > duration ? duration : newPosition);
                    },
                    tooltip: 'تقديم 10 ثوان',
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  _buildSmallControlButton(
                    context,
                    icon: Icons.speed,
                    label: '${value.playbackRate.toStringAsFixed(1)}x',
                    tooltip: 'سرعة التشغيل',
                    onPressed: () => _showYoutubePlaybackSpeedDialog(
                        context, youtubeController),
                  ),
                  _buildSmallControlButton(
                    context,
                    icon: Icons.hd,
                    label: value.playbackQuality ?? 'تلقائي',
                    tooltip: 'جودة الفيديو',
                    onPressed: () =>
                        _showYoutubeQualityDialog(context, youtubeController),
                  ),
                  // _buildSmallControlButton(
                  //   context,
                  //   icon: value.captionLanguage == 'off'
                  //       ? Icons.closed_caption_disabled_outlined
                  //       : Icons.closed_caption_outlined,
                  //   label: value.captionLanguage == 'off'
                  //       ? 'الترجمة متوقفة'
                  //       : 'الترجمة ${value.captionLanguage.toUpperCase()}',
                  //   tooltip: 'الترجمة',
                  //   onPressed: () => youtubeController.toggleCaption(),
                  // ),
                  _buildSmallControlButton(
                    context,
                    icon: value.isFullScreen
                        ? Icons.fullscreen_exit
                        : Icons.fullscreen,
                    label: 'الشاشة',
                    tooltip: 'ملء الشاشة',
                    onPressed: () => youtubeController.toggleFullScreenMode(),
                  ),
                ],
              ),
              if (youtubeId != null)
                Padding(
                  padding: const EdgeInsets.only(top: 4.0),
                  child: TextButton.icon(
                    style: TextButton.styleFrom(padding: EdgeInsets.zero),
                    icon: const Icon(Icons.open_in_new,
                        color: Colors.redAccent, size: 16),
                    label: const Text('فتح في يوتيوب',
                        style: TextStyle(color: Colors.white70, fontSize: 11)),
                    onPressed: () async {
                      final Uri appUri =
                          Uri.parse('youtube://watch?v=$youtubeId');
                      final Uri webUri = Uri.parse(
                          'https://www.youtube.com/watch?v='); // Fallback URL
                      try {
                        if (await canLaunchUrl(appUri)) {
                          await launchUrl(appUri);
                        } else if (await canLaunchUrl(webUri)) {
                          await launchUrl(webUri);
                        } else {
                          throw 'لا يمكن فتح $youtubeId';
                        }
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('لا يمكن فتح يوتيوب: $e')),
                          );
                        }
                      }
                    },
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSmallControlButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      // Added Expanded to help with spacing
      child: Tooltip(
        message: tooltip,
        child: TextButton(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          onPressed: onPressed,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Icon(icon, color: Colors.white, size: 18),
              const SizedBox(height: 2),
              Text(
                label,
                style: const TextStyle(color: Colors.white, fontSize: 10),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showYoutubePlaybackSpeedDialog(
      BuildContext context, YoutubePlayerController controller) {
    final List<double> speeds = <double>[
      0.25,
      0.5,
      0.75,
      1.0,
      1.25,
      1.5,
      1.75,
      2.0
    ];
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('اختر سرعة التشغيل'),
          contentPadding: const EdgeInsets.symmetric(vertical: 8.0),
          content: SizedBox(
            width: double.minPositive,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: speeds.length,
              itemBuilder: (BuildContext context, int index) {
                final double speed = speeds[index];
                return ListTile(
                  title: Text('${speed}x'),
                  onTap: () {
                    controller.setPlaybackRate(speed);
                    Navigator.of(dialogContext).pop();
                  },
                  selected: controller.value.playbackRate == speed,
                  dense: true,
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: const Text('إلغاء'))
          ],
        );
      },
    );
  }

  void _showYoutubeQualityDialog(
      BuildContext context, YoutubePlayerController controller) {
    // The youtube_player_flutter plugin handles quality automatically (adaptive).
    // Manually setting specific qualities like "hd720" isn't directly supported via a simple method.
    // The `forceHD` flag on initialization is one way to influence it.
    // This dialog is more for informing the user or if the plugin adds more granular control in the future.
    final List<String> availableQualities =
        controller.value.playbackQuality != null
            ? <String>[
                controller.value.playbackQuality!,
                'auto'
              ] // Show current and auto
            : <String>[
                'auto',
                'hd720',
                'medium',
                'small'
              ]; // Default list if current is null

    final Map<String, String> qualityLabels = <String, String>{
      'auto': 'تلقائي',
      'hd1080': '1080p عالي الدقة',
      'hd720': '720p عالي الدقة',
      'large': '480p',
      'medium': '360p',
      'small': '240p',
      // Add more if the plugin returns other specific strings for playbackQuality
    };

    // Filter out duplicate qualities that might arise if currentQuality is 'auto'
    final List<String> distinctQualities = availableQualities.toSet().toList();

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('اختر جودة الفيديو'),
          contentPadding: const EdgeInsets.symmetric(vertical: 8.0),
          content: SizedBox(
            width: double.minPositive,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: distinctQualities.length,
              itemBuilder: (BuildContext context, int index) {
                final String quality = distinctQualities[index];
                return ListTile(
                  title: Text(qualityLabels[quality] ?? quality.toUpperCase()),
                  onTap: () {
                    // Currently, direct quality setting is not robustly supported by the plugin's public API
                    // beyond initial flags. This is more illustrative.
                    // If 'auto', it means let YouTube decide.
                    if (quality != 'auto') {
                      // Attempting to force HD might involve re-initializing with forceHD:true
                      // or hoping the player picks up a hint if such API existed.
                      debugPrint(
                          'اختار المستخدم الجودة: $quality (ملاحظة: يستخدم البرنامج المساعد بشكل أساسي الجودة التكيفية أو علامة ForceHD الأولية)');
                    }
                    Navigator.of(dialogContext).pop();
                  },
                  selected: controller.value.playbackQuality == quality,
                  dense: true,
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: const Text('إلغاء'))
          ],
        );
      },
    );
  }

  Widget _buildWebViewPlayer(
      BuildContext context, WidgetRef ref, VideoPlayerState videoPlayerState) {
    if (videoPlayerState.videoUrl == null) {
      return const Center(child: Text('لا يوجد رابط فيديو متاح لعرض الويب.'));
    }
    if (videoPlayerState.webViewController == null) {
      // Initialize if null, this might happen if setVideoUrl was called but WebView init hasn't completed
      // or if the provider was reset.
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (ref
                .read(videoPlayerNotifierProvider.notifier)
                .state
                .webViewController ==
            null) {
          ref
              .read(videoPlayerNotifierProvider.notifier)
              .initializeWebViewController();
        }
      });
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      color: Colors.black,
      child: Column(
        children: <Widget>[
          Container(
            // Platform indicator
            padding:
                const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0),
            color: _getPlatformColor(videoPlayerState.videoType)
                .withValues(alpha: 0.8),
            child: Row(
              children: <Widget>[
                Icon(_getPlatformIcon(videoPlayerState.videoType),
                    color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text(_getPlatformName(videoPlayerState.videoType),
                    style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12)),
              ],
            ),
          ),
          Expanded(
            child: Stack(
              children: <Widget>[
                WebViewWidget(controller: videoPlayerState.webViewController!),
                if (videoPlayerState
                    .isLoading) // This isLoading is for WebView page load
                  const Center(child: CircularProgressIndicator()),
              ],
            ),
          ),
          Container(
              // Enhanced controls for WebView
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
              color: Colors.black.withValues(alpha: 0.85),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: <Widget>[
                      IconButton(
                          icon: const Icon(Icons.arrow_back_ios,
                              color: Colors.white, size: 20),
                          onPressed: () =>
                              videoPlayerState.webViewController?.goBack(),
                          tooltip: 'رجوع'),
                      IconButton(
                          icon: const Icon(Icons.refresh,
                              color: Colors.white, size: 20),
                          onPressed: () =>
                              videoPlayerState.webViewController?.reload(),
                          tooltip: 'إعادة تحميل'),
                      IconButton(
                          icon: const Icon(Icons.open_in_browser,
                              color: Colors.white, size: 20),
                          onPressed: () {
                            final MediaPlayerController mediaPlayerCtrl =
                                ref.read(mediaPlayerControllerProvider(mediaId)
                                    .notifier);
                            mediaPlayerCtrl
                                .openInBrowser(videoPlayerState.videoUrl!);
                          },
                          tooltip: 'فتح في المتصفح'),
                      IconButton(
                          icon: const Icon(Icons.arrow_forward_ios,
                              color: Colors.white, size: 20),
                          onPressed: () =>
                              videoPlayerState.webViewController?.goForward(),
                          tooltip: 'تقدم'),
                    ],
                  ),
                  if (videoPlayerState.videoUrl != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(videoPlayerState.videoUrl!,
                          style: const TextStyle(
                              color: Colors.white70, fontSize: 10),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis),
                    ),
                ],
              )),
          // Add the shared control bar
          MediaControlBar(mediaId: mediaId),
        ],
      ),
    );
  }

  Color _getPlatformColor(VideoPlayerType videoType) {
    switch (videoType) {
      case VideoPlayerType.facebook:
        return const Color(0xFF1877F2);
      case VideoPlayerType.tiktok:
        return const Color(0xFF000000);
      default:
        return Colors.grey;
    }
  }

  IconData _getPlatformIcon(VideoPlayerType videoType) {
    switch (videoType) {
      case VideoPlayerType.facebook:
        return Icons.facebook;
      case VideoPlayerType.tiktok:
        return Icons.music_note; // Or a more specific TikTok icon if available
      default:
        return Icons.public;
    }
  }

  String _getPlatformName(VideoPlayerType videoType) {
    switch (videoType) {
      case VideoPlayerType.facebook:
        return 'فيديو فيسبوك';
      case VideoPlayerType.tiktok:
        return 'فيديو تيك توك';
      default:
        return 'فيديو ويب';
    }
  }
}
