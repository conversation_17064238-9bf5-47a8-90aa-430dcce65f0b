import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/media_player_state.dart';
import '../../../utils/error_popup.dart';
import '../application/media_player_controller.dart';
import 'audio_player_widget.dart';
import 'document_viewer_widget.dart';
import 'tweet_widget.dart';
import 'video_player_widget.dart';

class MediaPlayerWidget extends ConsumerStatefulWidget {
  const MediaPlayerWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  ConsumerState<MediaPlayerWidget> createState() => _MediaPlayerWidgetState();
}

class _MediaPlayerWidgetState extends ConsumerState<MediaPlayerWidget> {
  String? _lastErrorMessage;

  @override
  Widget build(BuildContext context) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(widget.mediaId));

    // Handle error messages with popup
    if (mediaPlayerState.errorMessage != null &&
        mediaPlayerState.errorMessage != _lastErrorMessage) {
      _lastErrorMessage = mediaPlayerState.errorMessage;

      // Show error popup after the current build completes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && mediaPlayerState.errorMessage != null) {
          ErrorPopup.show(
            context: context,
            message: mediaPlayerState.errorMessage!,
          );

          // Clear the error message from state after showing popup
          ref
              .read(mediaPlayerControllerProvider(widget.mediaId).notifier)
              .clearErrorMessage();
        }
      });
    }

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.mediaItem == null) {
      return const Center(
        child: Text('No media item found'),
      );
    }

    // Determine which widget to show based on media type
    final MediaType mediaType = mediaPlayerState.mediaItem!.type;

    // Check for specific content types
    if (mediaType == MediaType.audio ||
        mediaPlayerState.mediaItem!.audioUrl != null) {
      return AudioPlayerWidget(
        mediaId: mediaPlayerState.mediaItem!.id,
        thumbnailUrl: mediaPlayerState.mediaItem!.thumbnailUrl,
        isPlaying: mediaPlayerState.isPlaying,
        audioPlayer: ref
            .read(mediaPlayerControllerProvider(widget.mediaId).notifier)
            .audioPlayerInstance,
      );
    } else if (mediaType == MediaType.video ||
        mediaPlayerState.mediaItem!.videoUrl != null) {
      return VideoPlayerWidget(
        chewieController: ref
            .read(mediaPlayerControllerProvider(widget.mediaId).notifier)
            .chewieControllerInstance,
      );
    } else if (mediaType == MediaType.pdf ||
        mediaType == MediaType.document ||
        mediaPlayerState.mediaItem!.documentUrl != null) {
      return DocumentViewerWidget(mediaId: widget.mediaId);
    } else if (mediaType == MediaType.tweet ||
        mediaPlayerState.mediaItem!.tweetContent != null) {
      return TweetWidget(mediaId: widget.mediaId);
    }

    // Fallback for unknown media types
    return Center(
      child: Text(
        'Unsupported media type: $mediaType',
        style: Theme.of(context).textTheme.titleLarge,
      ),
    );
  }
}
