import 'package:flutter/material.dart';

import '../../../utils/format_utils.dart';

class MediaControls extends StatelessWidget {
  const MediaControls({
    required this.isPlaying,
    required this.currentPosition,
    required this.totalDuration,
    required this.volume,
    required this.playbackSpeed,
    required this.onPlayPause,
    required this.onSeek,
    required this.onVolumeChanged,
    required this.onSpeedChanged,
    this.onNextAudio,
    this.hasDescription = false,
    this.hasNextAudio = false,
    super.key,
  });

  final bool isPlaying;
  final Duration currentPosition;
  final Duration totalDuration;
  final double volume;
  final double playbackSpeed;
  final VoidCallback onPlayPause;
  final Function(Duration) onSeek;
  final Function(double) onVolumeChanged;
  final Function(double) onSpeedChanged;

  final VoidCallback? onNextAudio;
  final bool hasDescription;
  final bool hasNextAudio;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // Seek bar
          Slider(
            value: currentPosition.inMilliseconds.toDouble(),
            max: totalDuration.inMilliseconds.toDouble(),
            onChanged: (double value) {
              onSeek(Duration(milliseconds: value.toInt()));
            },
          ),

          // Position and duration text
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(formatDuration(currentPosition)),
                Text(formatDuration(totalDuration)),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Playback controls
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Expanded(
                  child: IconButton(
                    icon: const Icon(Icons.replay_10),
                    onPressed: () {
                      final Duration newPosition =
                          currentPosition - const Duration(seconds: 10);
                      onSeek(
                          newPosition.isNegative ? Duration.zero : newPosition);
                    },
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: IconButton(
                    icon: Icon(
                      isPlaying
                          ? Icons.pause_circle_filled
                          : Icons.play_circle_filled,
                      size: 48,
                    ),
                    onPressed: onPlayPause,
                  ),
                ),
                Expanded(
                  child: IconButton(
                    icon: const Icon(Icons.forward_10),
                    onPressed: () {
                      final Duration newPosition =
                          currentPosition + const Duration(seconds: 10);
                      onSeek(newPosition > totalDuration
                          ? totalDuration
                          : newPosition);
                    },
                  ),
                ),
                if (hasNextAudio && onNextAudio != null)
                  Expanded(
                    child: IconButton(
                      icon: const Icon(Icons.skip_next),
                      onPressed: onNextAudio,
                      tooltip: 'Next Audio',
                    ),
                  ),
              ],
            ),
          ),

          // Additional controls (captions, etc.)
          // if (hasDescription && onShowCaptions != null)
          //   Padding(
          //     padding: const EdgeInsets.only(top: 8.0),
          //     child: ElevatedButton.icon(
          //       icon: const Icon(Icons.closed_caption),
          //       label: const Text('عرض التعليقات'),
          //       onPressed: onShowCaptions,
          //       style: ElevatedButton.styleFrom(
          //         backgroundColor:
          //             Theme.of(context).colorScheme.secondaryContainer,
          //         foregroundColor:
          //             Theme.of(context).colorScheme.onSecondaryContainer,
          //       ),
          //     ),
          //   ),

          const SizedBox(height: 8),

          // Volume and speed controls
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: <Widget>[
                const SizedBox(width: 8),
                const Icon(Icons.volume_up, size: 20),
                Expanded(
                  child: Slider(
                    value: volume,
                    onChanged: onVolumeChanged,
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<double>(
                  initialValue: playbackSpeed,
                  tooltip: 'سرعة التشغيل',
                  onSelected: onSpeedChanged,
                  itemBuilder: (
                    BuildContext context,
                  ) =>
                      <PopupMenuEntry<double>>[
                    const PopupMenuItem<double>(
                        value: 0.5, child: Text('0.5x')),
                    const PopupMenuItem<double>(
                        value: 0.75, child: Text('0.75x')),
                    const PopupMenuItem<double>(
                        value: 1.0, child: Text('1.0x')),
                    const PopupMenuItem<double>(
                        value: 1.25, child: Text('1.25x')),
                    const PopupMenuItem<double>(
                        value: 1.5, child: Text('1.5x')),
                    const PopupMenuItem<double>(
                        value: 2.0, child: Text('2.0x')),
                  ],
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    child: Text('${playbackSpeed}x'),
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
