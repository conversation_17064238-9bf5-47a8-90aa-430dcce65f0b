import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/media_player_state.dart';
import '../../tweets/presentation/widgets/tweet_card.dart';
import '../application/media_player_controller.dart';

class TweetWidget extends ConsumerWidget {
  const TweetWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final MediaPlayerController controller =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.mediaItem == null) {
      return const Center(
        child: Text('No tweet content available'),
      );
    }

    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: TweetCard(
          tweet: mediaPlayerState.mediaItem!,
          onTap: () {
            // Navigate to tweet details
            context.push('/tweet/${mediaPlayerState.mediaItem!.id}');
          },
        ),
      ),
    );
  }
}
