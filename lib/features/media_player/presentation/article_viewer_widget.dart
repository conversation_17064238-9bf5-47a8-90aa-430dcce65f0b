import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/universal_image.dart';
import '../../../data/models/media_player_state.dart';
import '../../../gen/fonts.gen.dart';
import '../application/media_player_controller.dart';

class ArticleViewerWidget extends ConsumerStatefulWidget {
  const ArticleViewerWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  ConsumerState<ArticleViewerWidget> createState() =>
      _ArticleViewerWidgetState();
}

class _ArticleViewerWidgetState extends ConsumerState<ArticleViewerWidget> {
  double _fontSize = 16.0;
  bool _nightMode = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(widget.mediaId));

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.mediaItem?.articleText == null &&
        mediaPlayerState.mediaItem?.metadata?.html == null) {
      return const Center(
        child: Text('No article content available'),
      );
    }

    // Get the HTML content from either articleText or metadata.html
    final String htmlContent = mediaPlayerState.mediaItem?.articleText ??
        mediaPlayerState.mediaItem?.metadata?.html ??
        '<p>No content available</p>';

    return Scaffold(
      appBar: AppBar(
        title: Text(mediaPlayerState.mediaItem?.title ?? 'Article'),
        actions: <Widget>[
          // Font size
          IconButton(
            icon: const Icon(Icons.format_size),
            onPressed: _showFontSizeDialog,
            tooltip: 'Font Size',
          ),
          // Night mode toggle
          IconButton(
            icon: Icon(_nightMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              setState(() {
                _nightMode = !_nightMode;
              });
            },
            tooltip: _nightMode ? 'Light Mode' : 'Night Mode',
          ),
          // Share
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => ref
                .read(mediaPlayerControllerProvider(widget.mediaId).notifier)
                .shareContent(),
            tooltip: 'Share',
          ),
        ],
      ),
      body: Container(
        color: _nightMode ? Colors.black87 : Colors.white,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Display thumbnail if available
              if (mediaPlayerState.mediaItem?.thumbnailUrl != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Center(
                    child: UniversalImage(
                      path: mediaPlayerState.mediaItem!.thumbnailUrl!,
                    ),
                  ),
                ),

              // Article title
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text(
                  mediaPlayerState.mediaItem?.title ?? 'Article',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: _nightMode ? Colors.white : Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),

              // Article content with HTML rendering
              Html(
                data: htmlContent,
                style: <String, Style>{
                  'body': Style(
                    fontSize: FontSize(_fontSize),
                    color: _nightMode ? Colors.white : Colors.black,
                  ),
                  'h1': Style(
                    fontSize: FontSize(_fontSize * 1.5),
                    color: _nightMode ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                  'h2': Style(
                    fontSize: FontSize(_fontSize * 1.3),
                    color: _nightMode ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                  'p': Style(
                    fontSize: FontSize(_fontSize),
                    color: _nightMode ? Colors.white : Colors.black,
                    lineHeight: const LineHeight(1.5),
                  ),
                  'a': Style(
                    color: _nightMode ? Colors.lightBlue : Colors.blue,
                    textDecoration: TextDecoration.underline,
                  ),
                  'li': Style(
                    fontSize: FontSize(_fontSize),
                    color: _nightMode ? Colors.white : Colors.black,
                  ),
                  'ul': Style(
                    margin: Margins.only(right: 20),
                  ),
                  'ol': Style(
                    margin: Margins.only(right: 20),
                  ),
                  'blockquote': Style(
                    backgroundColor:
                        _nightMode ? Colors.grey[800] : Colors.grey[200],
                    padding: HtmlPaddings.all(8),
                    margin: Margins.symmetric(vertical: 8),
                    border: Border(
                      right: BorderSide(
                        color:
                            _nightMode ? Colors.grey[600]! : Colors.grey[400]!,
                        width: 4.0,
                      ),
                    ),
                  ),
                  'code': Style(
                    backgroundColor:
                        _nightMode ? Colors.grey[800] : Colors.grey[200],
                    fontFamily: FontFamily.cairo,
                    padding: HtmlPaddings.all(4.0),
                  ),
                  'pre': Style(
                    backgroundColor:
                        _nightMode ? Colors.grey[800] : Colors.grey[200],
                    padding: HtmlPaddings.all(8.0),
                    margin: Margins.symmetric(vertical: 8.0),
                    fontFamily: FontFamily.cairo,
                    whiteSpace: WhiteSpace.pre,
                  ),
                  'img': Style(
                    margin: Margins.symmetric(vertical: 8.0),
                  ),
                  'table': Style(
                    border: Border.all(color: Colors.grey),
                  ),
                  'th': Style(
                    backgroundColor:
                        _nightMode ? Colors.grey[800] : Colors.grey[200],
                    padding: HtmlPaddings.all(8.0),
                    border: Border.all(color: Colors.grey),
                  ),
                  'td': Style(
                    padding: HtmlPaddings.all(8.0),
                    border: Border.all(color: Colors.grey),
                  ),
                  // Add specific styles for the fg-white class
                  '.fg-white': Style(
                    color: _nightMode ? Colors.white : Colors.black,
                  ),
                },
                onLinkTap: (String? url, _, __) {
                  if (url != null) {
                    _launchUrl(url);
                  }
                },
                extensions: <HtmlExtension>[
                  OnImageTapExtension(
                    onImageTap: (String? src, Map<String, String> imgAttributes,
                        dynamic element) {
                      if (src != null) {
                        _showImageDialog(src);
                      }
                    },
                  ),
                ],
              ),

              // Add some space at the bottom
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        },
        tooltip: 'Scroll to top',
        child: const Icon(Icons.arrow_upward),
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Adjust Font Size'),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    'Sample Text',
                    style: TextStyle(fontSize: _fontSize),
                  ),
                  const SizedBox(height: 20),
                  Slider(
                    value: _fontSize,
                    min: 12.0,
                    max: 24.0,
                    divisions: 12,
                    label: _fontSize.round().toString(),
                    onChanged: (double value) {
                      setState(() {
                        _fontSize = value;
                      });
                    },
                  ),
                ],
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إالغاء'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  // Update the font size in the main widget
                });
                Navigator.of(context).pop();
              },
              child: const Text('Apply'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.inAppWebView)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $url')),
        );
      }
    }
  }

  void _showImageDialog(String url) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: UniversalImage(
                  path: url,
                ),
              ),
              OverflowBar(
                children: <Widget>[
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Close'),
                  ),
                  TextButton(
                    onPressed: () {
                      _launchUrl(url);
                    },
                    child: const Text('Open in Browser'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
