import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/media_player_state.dart';
import '../../../utils/error_popup.dart';
import '../application/media_player_controller.dart';

class DocumentViewerWidget extends ConsumerStatefulWidget {
  const DocumentViewerWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  ConsumerState<DocumentViewerWidget> createState() =>
      _DocumentViewerWidgetState();
}

class _DocumentViewerWidgetState extends ConsumerState<DocumentViewerWidget> {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();
  int? _pages = 0;
  int? _currentPage = 0;

  bool _nightMode = false;
  double _currentZoom = 1.0;
  bool _showBookmarks = false;
  final List<int> _bookmarkedPages = <int>[];
  String? _lastDocumentError;

  @override
  Widget build(BuildContext context) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(widget.mediaId));

    // Handle document load errors with popup
    if (mediaPlayerState.documentLoadError != null &&
        mediaPlayerState.documentLoadError != _lastDocumentError) {
      _lastDocumentError = mediaPlayerState.documentLoadError;

      // Show error popup after the current build completes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && mediaPlayerState.documentLoadError != null) {
          ErrorPopup.show(
            context: context,
            message: mediaPlayerState.documentLoadError!,
          );

          // Clear the document error from state after showing popup
          ref
              .read(mediaPlayerControllerProvider(widget.mediaId).notifier)
              .clearDocumentError();
        }
      });
    }

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.mediaItem?.documentUrl == null) {
      return const Center(
        child: Text('المستند غير متوفر'),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(mediaPlayerState.mediaItem?.title ?? 'PDF Viewer'),
        actions: <Widget>[
          // Night mode toggle
          IconButton(
            icon: Icon(_nightMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              setState(() {
                _nightMode = !_nightMode;
              });
            },
            tooltip: _nightMode ? 'الوضع النهاري' : 'الوضع الليلي',
          ),
          // Bookmarks
          IconButton(
            icon: const Icon(Icons.bookmark),
            onPressed: () {
              setState(() {
                _showBookmarks = !_showBookmarks;
              });
            },
            tooltip: 'العلامات المرجعية',
          ),
          // More options
          PopupMenuButton<String>(
            onSelected: (String value) {
              switch (value) {
                case 'download':
                  ref
                      .read(mediaPlayerControllerProvider(widget.mediaId)
                          .notifier)
                      .downloadMedia();
                  break;
                case 'share':
                  ref
                      .read(mediaPlayerControllerProvider(widget.mediaId)
                          .notifier)
                      .shareContent();
                  break;
                case 'print':
                  _showPrintDialog();
                  break;
                case 'search':
                  _showSearchDialog();
                  break;
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'download',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('تحميل'),
                ),
              ),
              const PopupMenuItem<String>(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('مشاركة'),
                ),
              ),
              const PopupMenuItem<String>(
                value: 'print',
                child: ListTile(
                  leading: Icon(Icons.print),
                  title: Text('طباعة'),
                ),
              ),
              const PopupMenuItem<String>(
                value: 'search',
                child: ListTile(
                  leading: Icon(Icons.search),
                  title: Text('البحث في المستند'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: <Widget>[
          // Bookmarks panel (conditionally shown)
          if (_showBookmarks)
            Container(
              height: 60,
              color: Theme.of(context).colorScheme.surface,
              child: _bookmarkedPages.isEmpty
                  ? const Center(child: Text('لا توجد علامات مرجعية بعد'))
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _bookmarkedPages.length,
                      itemBuilder: (BuildContext context, int index) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: ElevatedButton(
                            onPressed: () {
                              final PDFViewController? pdfController =
                                  _controller.isCompleted
                                      ? _controller.future.then(
                                          (PDFViewController value) =>
                                              value) as PDFViewController?
                                      : null;
                              if (pdfController != null) {
                                pdfController.setPage(_bookmarkedPages[index]);
                              }
                            },
                            child: Text('صفحة ${_bookmarkedPages[index] + 1}'),
                          ),
                        );
                      },
                    ),
            ),

          // Document viewer
          Expanded(
            child: Stack(
              children: <Widget>[
                PDFView(
                  filePath: mediaPlayerState.mediaItem!.documentUrl,
                  swipeHorizontal: true,
                  defaultPage: _currentPage ?? 0,
                  fitPolicy: FitPolicy.BOTH,
                  backgroundColor:
                      _nightMode ? Colors.black87 : Colors.grey[200]!,
                  onRender: (int? pages) {
                    setState(() {
                      _pages = pages;
                    });
                    // Update the state in the controller
                    ref
                        .read(mediaPlayerControllerProvider(widget.mediaId)
                            .notifier)
                        .setPdfPageCount(pages);
                  },
                  onError: (dynamic error) {
                    debugPrint('PDF Error: $error');
                  },
                  onPageError: (int? page, dynamic error) {
                    debugPrint('PDF Page $page Error: $error');
                  },
                  onViewCreated: (PDFViewController pdfViewController) {
                    _controller.complete(pdfViewController);
                  },
                  onPageChanged: (int? page, int? total) {
                    setState(() {
                      _currentPage = page;
                    });
                    // Update the state in the controller
                    if (page != null) {
                      ref
                          .read(mediaPlayerControllerProvider(widget.mediaId)
                              .notifier)
                          .setCurrentPage(page + 1);
                    }
                  },
                ),

                // Floating action buttons for quick actions
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      // Bookmark current page
                      FloatingActionButton(
                        heroTag: 'bookmark_btn',
                        mini: true,
                        onPressed: () {
                          if (_currentPage != null) {
                            setState(() {
                              if (_bookmarkedPages.contains(_currentPage)) {
                                _bookmarkedPages.remove(_currentPage);
                              } else {
                                _bookmarkedPages.add(_currentPage!);
                              }
                            });
                          }
                        },
                        child: Icon(
                          _currentPage != null &&
                                  _bookmarkedPages.contains(_currentPage)
                              ? Icons.bookmark
                              : Icons.bookmark_border,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Zoom in
                      FloatingActionButton(
                        heroTag: 'zoom_in_btn',
                        mini: true,
                        onPressed: () {
                          if (_currentZoom < 3.0) {
                            setState(() {
                              _currentZoom += 0.25;
                            });
                            ref
                                .read(mediaPlayerControllerProvider(
                                        widget.mediaId)
                                    .notifier)
                                .setPdfZoom(_currentZoom);
                          }
                        },
                        child: const Icon(Icons.zoom_in),
                      ),
                      const SizedBox(height: 8),
                      // Zoom out
                      FloatingActionButton(
                        heroTag: 'zoom_out_btn',
                        mini: true,
                        onPressed: () {
                          if (_currentZoom > 0.5) {
                            setState(() {
                              _currentZoom -= 0.25;
                            });
                            ref
                                .read(mediaPlayerControllerProvider(
                                        widget.mediaId)
                                    .notifier)
                                .setPdfZoom(_currentZoom);
                          }
                        },
                        child: const Icon(Icons.zoom_out),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Bottom controls
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                // Page navigation
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    IconButton(
                      icon: const Icon(Icons.first_page),
                      onPressed: () {
                        final PDFViewController? pdfController = _controller
                                .isCompleted
                            ? _controller.future
                                    .then((PDFViewController value) => value)
                                as PDFViewController?
                            : null;
                        if (pdfController != null) {
                          pdfController.setPage(0);
                        }
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () {
                        final PDFViewController? pdfController = _controller
                                .isCompleted
                            ? _controller.future
                                    .then((PDFViewController value) => value)
                                as PDFViewController?
                            : null;
                        if (pdfController != null &&
                            _currentPage != null &&
                            _currentPage! > 0) {
                          pdfController.setPage(_currentPage! - 1);
                        }
                      },
                    ),
                    Expanded(
                      child: Text(
                        'Page ${(_currentPage ?? 0) + 1} of ${_pages ?? 0}',
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_forward),
                      onPressed: () {
                        final PDFViewController? pdfController = _controller
                                .isCompleted
                            ? _controller.future
                                    .then((PDFViewController value) => value)
                                as PDFViewController?
                            : null;
                        if (pdfController != null &&
                            _currentPage != null &&
                            _pages != null &&
                            _currentPage! < _pages! - 1) {
                          pdfController.setPage(_currentPage! + 1);
                        }
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.last_page),
                      onPressed: () {
                        final PDFViewController? pdfController = _controller
                                .isCompleted
                            ? _controller.future
                                    .then((PDFViewController value) => value)
                                as PDFViewController?
                            : null;
                        if (pdfController != null && _pages != null) {
                          pdfController.setPage(_pages! - 1);
                        }
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Page slider
                if (_pages != null && _pages! > 0)
                  Slider(
                    value: (_currentPage ?? 0).toDouble(),
                    max: (_pages! - 1).toDouble(),
                    divisions: _pages,
                    label: 'Page ${(_currentPage ?? 0) + 1}',
                    onChanged: (double value) {
                      final PDFViewController? pdfController =
                          _controller.isCompleted
                              ? _controller.future
                                      .then((PDFViewController value) => value)
                                  as PDFViewController?
                              : null;
                      if (pdfController != null) {
                        pdfController.setPage(value.toInt());
                      }
                    },
                  ),

                const SizedBox(height: 8),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    ElevatedButton.icon(
                      icon: const Icon(Icons.download),
                      label: const Text('Download'),
                      onPressed: () => ref
                          .read(mediaPlayerControllerProvider(widget.mediaId)
                              .notifier)
                          .downloadMedia(),
                    ),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.share),
                      label: const Text('Share'),
                      onPressed: () => ref
                          .read(mediaPlayerControllerProvider(widget.mediaId)
                              .notifier)
                          .shareContent(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showPrintDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Print Document'),
          content: const Text(
              'Printing functionality would be implemented here. This would connect to native printing capabilities.'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showSearchDialog() {
    final TextEditingController searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Search in Document'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'Enter search term',
              prefixIcon: Icon(Icons.search),
            ),
            autofocus: true,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إالغاء'),
            ),
            TextButton(
              onPressed: () {
                // Implement search functionality
                // This would require a more advanced PDF library with text extraction
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Searching for: ${searchController.text}'),
                  ),
                );
              },
              child: const Text('Search'),
            ),
          ],
        );
      },
    );
  }
}
