import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';

class VideoPlayerWidget extends StatelessWidget {
  const VideoPlayerWidget({
    this.chewieController,
    super.key,
  });

  final ChewieController? chewieController;

  @override
  Widget build(BuildContext context) {
    if (chewieController == null) {
      return const Center(
        child: Text('الفيديو غير متوفر'),
      );
    }

    return Container(
      color: Colors.black,
      child: Column(
        children: <Widget>[
          Expanded(
            child: Center(
              child: AspectRatio(
                aspectRatio:
                    chewieController!.videoPlayerController.value.aspectRatio,
                child: Chewie(
                  controller: chewieController!,
                ),
              ),
            ),
          ),
          // Additional video information or custom controls can be added here
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            color: Colors.black.withValues(alpha: 0.7),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                // These are additional controls that complement Ch<PERSON>ie's built-in controls
                IconButton(
                  icon: const Icon(Icons.fullscreen, color: Colors.white),
                  onPressed: () {
                    chewieController!.enterFullScreen();
                  },
                  tooltip: 'ملء الشاشة',
                ),
                IconButton(
                  icon: const Icon(Icons.speed, color: Colors.white),
                  onPressed: () {
                    // Show speed selection dialog
                    _showPlaybackSpeedDialog(context);
                  },
                  tooltip: 'سرعة التشغيل',
                ),
                IconButton(
                  icon: const Icon(Icons.hd, color: Colors.white),
                  onPressed: () {
                    // This is a placeholder for quality selection
                    // Implement quality selection if your video source supports it
                  },
                  tooltip: 'الجودة',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showPlaybackSpeedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('اختر سرعة التشغيل'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              _buildSpeedButton(context, 0.5),
              _buildSpeedButton(context, 0.75),
              _buildSpeedButton(context, 1.0),
              _buildSpeedButton(context, 1.25),
              _buildSpeedButton(context, 1.5),
              _buildSpeedButton(context, 2.0),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSpeedButton(BuildContext context, double speed) {
    return ListTile(
      title: Text('${speed}x'),
      onTap: () {
        chewieController!.videoPlayerController.setPlaybackSpeed(speed);
        Navigator.of(context).pop();
      },
    );
  }
}
