import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../data/enums/media_type_enum.dart';
import '../../../data/enums/opentions_format_enum.dart';
import '../../../data/models/media_item.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../utils/format_utils.dart';
import '../application/media_player_controller.dart';

class MediaInfoPanel extends ConsumerWidget {
  const MediaInfoPanel({
    required this.mediaItem,
    super.key,
  });

  final MediaUiModel mediaItem;

  /// Translates media type to Arabic
  String _getArabicTypeName(MediaType type) {
    switch (type) {
      case MediaType.audio:
        return 'صوت';
      case MediaType.video:
        return 'فيديو';
      case MediaType.tweet:
        return 'تغريدة';
      case MediaType.text:
        return 'كتاب';
      case MediaType.html:
        return 'مقالة';
      case MediaType.pdf:
        return 'ملف PDF';
      case MediaType.document:
        return 'مستند';
      case MediaType.unknown:
        return 'غير معروف';
    }
  }

  /// Translates media category to Arabic
  String _getArabicCategoryName(MediaCategory category) {
    switch (category) {
      case MediaCategory.lessons:
        return 'دروس';
      case MediaCategory.sermons:
        return 'خطب';
      case MediaCategory.lectures:
        return 'محاضرات';
      case MediaCategory.radio:
        return 'راديو';
      case MediaCategory.youtubeVideos:
        return 'فيديوهات يوتيوب';
      case MediaCategory.books:
        return 'كتب';
      case MediaCategory.htmlArticles:
        return 'مقالات';
      case MediaCategory.recentTweets:
        return 'تغريدات حديثة';
      case MediaCategory.photos:
        return 'صور';
      case MediaCategory.general:
        return 'عام';
      case MediaCategory.tweetCategory:
        return 'تغريدات';
      case MediaCategory.unknown:
        return 'غير معروف';
    }
  }

  /// Translates option format to Arabic
  String _getArabicFormatName(OptionFormat format) {
    switch (format) {
      case OptionFormat.audioMpeg:
        return 'ملف صوتي';
      case OptionFormat.videoMp4:
      case OptionFormat.videoYoutube:
        return 'فيديو';
      case OptionFormat.textPlain:
        return 'نص';
      case OptionFormat.html:
      case OptionFormat.article:
        return 'مقالة';
      case OptionFormat.pdf:
        return 'ملف PDF';
      case OptionFormat.docx:
        return 'مستند Word';
      case OptionFormat.tweet:
        return 'تغريدة';
      case OptionFormat.imageJpeg:
        return 'صورة';
      case OptionFormat.unknown:
        return 'غير معروف';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    try {
      final MediaPlayerController mediaPlayerController =
          ref.watch(mediaPlayerControllerProvider(mediaItem.id).notifier);

      return Container(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Title section
              Text(
                mediaItem.title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              if (mediaItem.description != null &&
                  mediaItem.description!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    mediaItem.description!,
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                ),
              const Divider(height: 24),

              // Media details section
              Column(
                children: <Widget>[
                  _buildInfoRow(
                    context,
                    'النوع',
                    _getArabicTypeName(mediaItem.type),
                    Icons.category,
                  ),
                  _buildInfoRow(
                    context,
                    'التصنيف',
                    _getArabicCategoryName(mediaItem.category),
                    Icons.folder,
                  ),
                  if (mediaItem.durationSeconds != null)
                    _buildInfoRow(
                      context,
                      'المدة',
                      formatDurationFromSeconds(mediaItem.durationSeconds!),
                      Icons.timer,
                    ),
                  if (mediaItem.metadata?.additionalInfo?['createdAt'] != null)
                    _buildInfoRow(
                      context,
                      'تاريخ النشر',
                      formatDateString(mediaItem
                          .metadata!.additionalInfo!['createdAt'] as String),
                      Icons.calendar_today,
                    ),
                ],
              ),

              const Divider(height: 24),

              // Available options section
              Text(
                'الخيارات المتاحة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 12),

              // Display available options as buttons
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _buildOptionButtons(context, mediaPlayerController),
              ),

              // Source information
            ],
          ),
        ),
      );
    } catch (e) {
      // If there's an error rendering the panel, show a fallback UI
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.error_outline, color: Colors.red),
              const SizedBox(height: 8),
              Text(
                'حدث خطأ أثناء عرض معلومات الوسائط: $e',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  List<Widget> _buildOptionButtons(
      BuildContext context, MediaPlayerController controller) {
    final List<Widget> buttons = <Widget>[];

    // Check if the media item has options
    if (mediaItem.options != null && mediaItem.options!.isNotEmpty) {
      for (final MediaOption option in mediaItem.options!) {
        // Skip null or empty URLs
        if (option.url == null || option.url!.isEmpty) {
          continue;
        }

        // Determine button icon and color based on format
        IconData icon;
        Color color;
        String label = option.description ?? 'خيار';

        // Use the enum directly instead of string comparison when possible
        if (option.format.isAudio || option.format == OptionFormat.audioMpeg) {
          icon = Icons.audiotrack;
          color = Colors.blue.withValues();
          label = option.description ?? 'ملف صوتي';
        } else if (option.format.isVideo ||
            option.format == OptionFormat.videoMp4 ||
            option.format == OptionFormat.videoYoutube) {
          icon = Icons.videocam;
          color = Colors.red;
          label = option.description ?? 'فيديو';
        } else if (option.format.isText ||
            option.format == OptionFormat.textPlain ||
            option.format == OptionFormat.html) {
          icon = Icons.description;
          color = Colors.green;
          label = option.description ?? 'نص';
        } else if (option.format.isDocument ||
            option.format == OptionFormat.pdf ||
            option.format == OptionFormat.docx) {
          icon = Icons.insert_drive_file;
          color = Colors.orange;
          label = option.description ?? 'مستند';
        } else if (option.format.isSocialMedia ||
            option.format == OptionFormat.tweet) {
          icon = Icons.chat;
          color = Colors.lightBlue;
          label = option.description ?? 'تغريدة';
        } else if (option.format.isImage ||
            option.format == OptionFormat.imageJpeg) {
          icon = Icons.image;
          color = Colors.amber;
          label = option.description ?? 'صورة';
        } else {
          icon = Icons.link;
          color = Colors.purple;
          // If we have a description, use it, otherwise use the Arabic format name
          label = option.description ?? _getArabicFormatName(option.format);
        }

        // Create button for this option
        buttons.add(
          ElevatedButton.icon(
            icon: Icon(icon, size: 18),
            label: Text(label),
            style: ElevatedButton.styleFrom(
              // backgroundColor: color.withValues(alpha: 0.1),
              foregroundColor: color,
            ),
            onPressed: () async {
              // Handle button press based on format
              if (option.url != null && option.url!.isNotEmpty) {
                final Uri uri = Uri.parse(option.url!);
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri);
                } else {
                  // Show error message
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('لا يمكن فتح الرابط: ${option.url}'),
                      ),
                    );
                  }
                }
              }
            },
          ),
        );
      }
    }

    // Add download button if no option buttons were added
    if (buttons.isEmpty) {
      buttons.add(
        ElevatedButton.icon(
          icon: const Icon(Icons.download, size: 18),
          label: const Text('تحميل'),
          onPressed: () => controller.downloadMedia(),
        ),
      );
    }

    return buttons;
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
