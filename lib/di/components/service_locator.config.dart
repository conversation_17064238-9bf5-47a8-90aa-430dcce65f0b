// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:get_storage/get_storage.dart' as _i792;
import 'package:injectable/injectable.dart' as _i526;

import '../../data/getstore/get_store_helper.dart' as _i478;
import '../../data/repository/media_repository.dart' as _i169;
import '../../data/repository/user_data_repository.dart' as _i454;
import '../../data/services/media_loader_service.dart' as _i916;
import '../../data/services/secure_storage_service.dart' as _i120;
import '../../data/services/user_data_service.dart' as _i67;
import '../../data/sqlite/sqlite_helper.dart' as _i369;
import '../../routing/app_router.dart' as _i605;
import '../module/network_module.dart' as _i1000;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final networkModule = _$NetworkModule();
    await gh.factoryAsync<_i792.GetStorage>(
      () => networkModule.provideGetStorage(),
      preResolve: true,
    );
    gh.factory<_i369.SQLiteHelper>(() => _i369.SQLiteHelper());
    gh.factory<_i916.MediaLoaderService>(() => _i916.MediaLoaderService());
    gh.singleton<_i605.SGGoRouter>(() => _i605.SGGoRouter());
    gh.lazySingleton<_i120.SecureStorageService>(
        () => networkModule.provideSecureStorageService());
    gh.lazySingleton<_i67.UserDataService>(
        () => _i67.UserDataService(gh<_i369.SQLiteHelper>()));
    gh.factory<_i454.UserDataRepository>(
        () => _i454.UserDataRepository(gh<_i67.UserDataService>()));
    gh.factory<_i478.GetStoreHelper>(() => _i478.GetStoreHelper(
          gh<_i792.GetStorage>(),
          gh<_i120.SecureStorageService>(),
        ));
    await gh.factoryAsync<_i361.Dio>(
      () => networkModule.provideDio(gh<_i478.GetStoreHelper>()),
      preResolve: true,
    );
    gh.factory<_i169.MediaRepository>(
        () => _i169.MediaRepository(gh<_i916.MediaLoaderService>()));
    return this;
  }
}

class _$NetworkModule extends _i1000.NetworkModule {}
