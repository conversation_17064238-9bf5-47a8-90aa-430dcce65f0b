/// Utility functions for formatting values
///
/// This file contains utility functions for formatting values like durations,
/// dates, etc. that are used throughout the application.
import 'package:intl/intl.dart';

/// Formats a Duration into a string representation (MM:SS or HH:MM:SS)
///
/// If the duration has hours, the format will be HH:MM:SS
/// Otherwise, the format will be MM:SS
String formatDuration(Duration duration) {
  final int hours = duration.inHours;
  final String minutes =
      duration.inMinutes.remainder(60).toString().padLeft(2, '0');
  final String seconds =
      duration.inSeconds.remainder(60).toString().padLeft(2, '0');

  if (hours > 0) {
    return '$hours:$minutes:$seconds';
  } else {
    return '$minutes:$seconds';
  }
}

/// Formats seconds (int) into a duration string (MM:SS or HH:MM:SS)
///
/// This is a convenience method that converts seconds to a Duration
/// and then formats it using [formatDuration]
String formatDurationFromSeconds(int seconds) {
  final Duration duration = Duration(seconds: seconds);
  return formatDuration(duration);
}

/// Formats seconds (double) into a duration string (MM:SS or HH:MM:SS)
///
/// This is a convenience method that converts seconds to a Duration
/// and then formats it using [formatDuration]
String formatDurationFromDoubleSeconds(double seconds) {
  final Duration duration = Duration(seconds: seconds.round());
  return formatDuration(duration);
}

/// Formats a DateTime into a simple date string (DD/MM/YYYY)
///
/// Example: 15/4/2023
String formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year}';
}

/// Formats a DateTime into a date and time string (YYYY/MM/DD HH:MM)
///
/// Example: 2023/4/15 14:05
String formatDateTime(DateTime dateTime) {
  return '${dateTime.year}/${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
}

/// Formats a date string into a readable date using DateFormat
///
/// This function parses a date string and formats it using DateFormat.yMMMd()
/// If parsing fails, it returns the original string
String formatDateString(String dateString) {
  try {
    final DateTime date = DateTime.parse(dateString);
    return DateFormat.yMMMd().format(date);
  } catch (e) {
    return dateString;
  }
}
