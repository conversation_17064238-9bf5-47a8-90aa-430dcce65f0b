import 'package:flutter/material.dart';

/// Extension for BuildContext to get theme, textTheme and colorScheme
extension BuildContextExtensions on BuildContext {
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => theme.textTheme;
  ColorScheme get colorScheme => theme.colorScheme;

  /// Check if the app is in RTL mode
  bool get isRTL => Directionality.of(this) == TextDirection.rtl;

  /// Get the appropriate edge insets based on text direction
  EdgeInsets get rtlAwarePadding => isRTL
      ? const EdgeInsets.only(right: 16.0, left: 8.0)
      : const EdgeInsets.only(left: 16.0, right: 8.0);
}
