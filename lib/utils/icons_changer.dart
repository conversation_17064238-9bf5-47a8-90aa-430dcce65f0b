import 'package:flutter/material.dart';

import '../data/enums/media_type_enum.dart';

IconData getIconForType(MediaType type) {
  switch (type) {
    case MediaType.video:
      return Icons.video_library;
    case MediaType.audio:
      return Icons.audiotrack;
    case MediaType.pdf:
    case MediaType.document:
      return Icons.insert_drive_file;
    case MediaType.text:
      return Icons.article;
    case MediaType.tweet:
      return Icons.chat;
    case MediaType.html:
      return Icons.article;
    case MediaType.unknown:
      return Icons.insert_drive_file;
  }
}

IconData getIconForCategory(MediaCategory category) {
  switch (category) {
    case MediaCategory.lessons:
      return Icons.school;
    case MediaCategory.sermons:
      return Icons.record_voice_over;
    case MediaCategory.lectures:
      return Icons.mic;
    case MediaCategory.radio:
      return Icons.radio;
    case MediaCategory.books:
      return Icons.book;
    case MediaCategory.htmlArticles:
      return Icons.article;
    case MediaCategory.photos:
      return Icons.photo_library;
    case MediaCategory.youtubeVideos:
      return Icons.video_library;
    case MediaCategory.unknown:
      return Icons.video_library;
    case MediaCategory.recentTweets:
      return Icons.post_add;
    case MediaCategory.general:
      return Icons.post_add;
    case MediaCategory.tweetCategory:
      return Icons.post_add;
  }
}
