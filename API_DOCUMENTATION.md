# Audio API Documentation

## Overview

This API provides endpoints for retrieving audio content organized by categories. The API follows RESTful conventions and returns clean JSON responses without wrapper fields.

## Base URL

```
http://your-domain.com/api/v1
```

## Authentication

Currently, no authentication is required for these endpoints.

## Endpoints

### Get Audio Records by Category

Retrieves all audio records grouped by their categories with pagination support for both categories and audio records within each category.

**Endpoint:** `GET /api/v1/audios/by-category`

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `paginated` | boolean | No | `false` | Whether to paginate the categories |
| `per_page` | integer | No | `15` | Number of categories per page (1-100, only when paginated=true) |
| `items_per_page` | integer | No | `10` | Number of audio records per category (1-50) |
| `items_page` | integer | No | `1` | Page number for audio records within categories (min: 1) |

### Get Audio Records for Specific Category

Retrieves audio records for a specific category with pagination.

**Endpoint:** `GET /api/v1/audios/category/{categoryId}`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `categoryId` | integer | Yes | The ID of the category to retrieve audio records for |

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `per_page` | integer | No | `10` | Number of audio records per page (1-50) |
| `page` | integer | No | `1` | Page number (min: 1) |

#### Response Structure

##### Success Response (200 OK)

**Get Audio Records by Category - Non-paginated:**
```json
{
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Islamic Lectures",
        "status": true,
        "category_type": "audio",
        "category_type_label": "الصوتيات",
        "total_audio_count": 2,
        "audio_records": [
          {
            "id": 1,
            "title": "Introduction to Islam",
            "description": "A comprehensive introduction to Islamic principles",
            "audio_file": "audio/intro_islam.mp3",
            "audio_url": "http://your-domain.com/storage/audio/intro_islam.mp3",
            "category_id": 1,
            "created_at": "2025-06-02T14:49:04.000000Z",
            "updated_at": "2025-06-02T14:49:04.000000Z"
          }
        ],
        "audio_pagination": {
          "current_page": 1,
          "per_page": 10,
          "total": 2,
          "has_more": false
        },
        "created_at": "2025-06-02T14:47:51.000000Z",
        "updated_at": "2025-06-02T14:47:51.000000Z"
      }
    ],
    "total_categories": 2,
    "total_audio_records": 3
  }
}
```

**Get Audio Records by Category - Paginated Categories:**
```json
{
  "data": {
    "categories": [...],
    "total_categories": 1,
    "total_audio_records": 2
  },
  "pagination": {
    "current_page": 1,
    "last_page": 2,
    "per_page": 1,
    "total": 2,
    "from": 1,
    "to": 1
  }
}
```

**Get Audio Records for Specific Category:**
```json
{
  "data": {
    "category": {
      "id": 1,
      "name": "Islamic Lectures",
      "status": true,
      "category_type": "audio",
      "category_type_label": "الصوتيات",
      "created_at": "2025-06-02T14:47:51.000000Z",
      "updated_at": "2025-06-02T14:47:51.000000Z"
    },
    "audio_records": [
      {
        "id": 1,
        "title": "Introduction to Islam",
        "description": "A comprehensive introduction to Islamic principles",
        "audio_file": "audio/intro_islam.mp3",
        "audio_url": "http://your-domain.com/storage/audio/intro_islam.mp3",
        "category_id": 1,
        "created_at": "2025-06-02T14:49:04.000000Z",
        "updated_at": "2025-06-02T14:49:04.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "last_page": 1,
      "per_page": 10,
      "total": 1,
      "from": 1,
      "to": 1,
      "has_more_pages": false
    },
    "meta": {
      "api_version": "v1",
      "generated_at": "2025-06-02T15:09:50.045663Z"
    }
  }
}
```

#### Error Responses

##### Validation Error (422 Unprocessable Entity)
```json
{
  "errors": {
    "per_page": ["The per page field must not be greater than 100."],
    "items_per_page": ["The items per page field must not be greater than 50."]
  }
}
```

##### Not Found Error (404 Not Found)
```json
{
  "error": "Category not found or not accessible"
}
```

##### Server Error (500 Internal Server Error)
```json
{
  "error": "Internal server error"
}
```

## Data Models

### Category Object

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique category identifier |
| `name` | string | Category name |
| `status` | boolean | Whether the category is active |
| `category_type` | string | Type of category (always "audio" for this endpoint) |
| `category_type_label` | string | Localized label for category type |
| `total_audio_count` | integer | Total number of audio records in this category |
| `audio_records` | array | Array of audio objects belonging to this category (paginated) |
| `audio_pagination` | object | Pagination info for audio records within this category |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

### Audio Object

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique audio identifier |
| `title` | string | Audio title |
| `description` | string\|null | Audio description |
| `audio_file` | string | Relative path to audio file |
| `audio_url` | string\|null | Full URL to audio file |
| `category_id` | integer | ID of the category this audio belongs to |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

## Usage Examples

### Basic Request - Get All Categories with Audio
```bash
curl -X GET "http://your-domain.com/api/v1/audios/by-category" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### Paginated Categories Request
```bash
curl -X GET "http://your-domain.com/api/v1/audios/by-category?paginated=1&per_page=5" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### Audio Pagination within Categories
```bash
curl -X GET "http://your-domain.com/api/v1/audios/by-category?items_per_page=5&items_page=1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### Combined Pagination (Categories + Audio)
```bash
curl -X GET "http://your-domain.com/api/v1/audios/by-category?paginated=1&per_page=3&items_per_page=5&items_page=1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### Get Audio for Specific Category
```bash
curl -X GET "http://your-domain.com/api/v1/audios/category/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### Get Audio for Specific Category with Pagination
```bash
curl -X GET "http://your-domain.com/api/v1/audios/category/1?per_page=5&page=2" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

## Notes

- Only active categories (status = true) are returned
- Only categories with category_type = "audio" are included
- Audio records within each category are ordered by creation date (newest first)
- Categories are ordered alphabetically by name
- The `audio_url` field provides a full URL for direct file access
- Empty categories (categories with no audio records) are still included in the response
- All timestamps are in UTC and follow ISO 8601 format
- **Audio pagination within categories**: Use `items_per_page` and `items_page` to control how many audio records are returned per category
- **Dual pagination support**: You can paginate both categories and audio records within each category simultaneously
- **Performance optimization**: Audio pagination helps reduce response size when categories have many audio records
- **Clean response structure**: No wrapper fields like 'success' or 'message' - only essential data
- **Consistent error format**: Validation errors return only 'errors' field, server errors return only 'error' field
- **Generic pagination parameters**: Uses `items_per_page` and `items_page` for consistency across all API resources

## Error Handling

The API implements comprehensive error handling:

- **Validation errors** return 422 status with only 'errors' field containing detailed field-level error messages
- **Not found errors** return 404 status with only 'error' field containing descriptive message
- **Server errors** return 500 status with only 'error' field containing appropriate error messages
- **Debug mode** controls whether detailed error messages are exposed in production

## Testing

The API includes comprehensive test coverage for all endpoints and features:

- Basic functionality tests for both endpoints
- Pagination tests for categories and audio records
- Validation error tests for all parameters
- Error handling tests (404, 422, 500)
- Response structure validation tests

Run tests with:

```bash
php artisan test tests/Feature/Api/AudioControllerTest.php
```

**Test Coverage:**
- 10 test methods
- 131 assertions
- Tests for both `/by-category` and `/category/{id}` endpoints
- Audio pagination within categories
- Error scenarios and edge cases
