<?php

use App\Http\Controllers\Api\AudioController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\ContentController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// API v1 routes
Route::prefix('v1')->group(function () {
    // Category endpoints
    Route::get('/categories', [CategoryController::class, 'getCategories']);
    Route::get('/categories/{category}/contents', [ContentController::class, 'getCategoryContents']);
});
