# Content API Documentation

## Overview

This API provides endpoints for retrieving content items associated with categories through the polymorphic Content model. The API follows RESTful conventions and returns clean JSON responses without wrapper fields, maintaining consistency with existing API patterns.

## Base URL

```
http://your-domain.com/api/v1
```

## Authentication

Currently, no authentication is required for these endpoints.

## Endpoints

### Get Content Items by Category

Retrieves all content items belonging to a specific category, including their associated contentable models (Audio, Video, Book).

**Endpoint:** `GET /api/v1/categories/{category}/contents`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `category` | integer | Yes | The ID of the category to retrieve content for |

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `per_page` | integer | No | 15 | Number of content items per page (1-100) |
| `page` | integer | No | 1 | Page number for pagination (minimum 1) |

#### Response Format

**Success Response (200 OK):**

```json
{
  "data": {
    "category": {
      "id": 1,
      "name": "Islamic Lectures",
      "status": true,
      "parent_id": null,
      "parent_name": null,
      "total_contents_count": 25,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "contents": [
      {
        "id": 1,
        "title": "Introduction to Islam",
        "description": "A comprehensive introduction to Islamic principles",
        "status": true,
        "content_type": "Audio",
        "contentable_type": "App\\Models\\Audio",
        "contentable_id": 1,
        "category_id": 1,
        "category": {
          "id": 1,
          "name": "Islamic Lectures",
          "status": true,
          "parent_id": null
        },
        "contentable": {
          "id": 1,
          "title": "Introduction to Islam Audio",
          "description": "Audio lecture on Islamic principles",
          "category_id": 1,
          "audio_file": "lectures/intro-islam.mp3",
          "audio_url": "http://your-domain.com/storage/lectures/intro-islam.mp3",
          "created_at": "2024-01-01T00:00:00.000000Z",
          "updated_at": "2024-01-01T00:00:00.000000Z"
        },
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 15,
      "total": 25,
      "last_page": 2,
      "from": 1,
      "to": 15,
      "has_more": true,
      "next_page_url": "http://your-domain.com/api/v1/categories/1/contents?page=2",
      "prev_page_url": null
    },
    "content_type_summary": {
      "audio_count": 10,
      "video_count": 8,
      "book_count": 7,
      "total_count": 25
    }
  }
}
```

**Error Responses:**

**404 Not Found - Category not found or inactive:**
```json
{
  "error": "Category not found or inactive"
}
```

**422 Unprocessable Entity - Validation errors:**
```json
{
  "errors": {
    "per_page": ["The per page field must be at least 1."],
    "page": ["The page field must be at least 1."]
  }
}
```

**500 Internal Server Error:**
```json
{
  "error": "An error occurred while retrieving content items"
}
```

## Data Models

### Category Object

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique category identifier |
| `name` | string | Category name |
| `status` | boolean | Whether the category is active |
| `parent_id` | integer\|null | ID of parent category (null for main categories) |
| `parent_name` | string\|null | Name of parent category |
| `total_contents_count` | integer | Total number of active content items in this category |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

### Content Object

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique content identifier |
| `title` | string | Content title |
| `description` | string\|null | Content description |
| `status` | boolean | Whether the content is active |
| `content_type` | string | Human-readable content type (Audio, Video, Book) |
| `contentable_type` | string | Full class name of the contentable model |
| `contentable_id` | integer | ID of the contentable model |
| `category_id` | integer | ID of the category this content belongs to |
| `category` | object | Basic category information |
| `contentable` | object | Full contentable model data (varies by type) |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

### Contentable Objects

#### Audio Contentable

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique audio identifier |
| `title` | string | Audio title |
| `description` | string\|null | Audio description |
| `category_id` | integer | Category ID |
| `audio_file` | string | Relative path to audio file |
| `audio_url` | string\|null | Full URL to audio file |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

#### Video Contentable

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique video identifier |
| `title` | string | Video title |
| `description` | string\|null | Video description |
| `category_id` | integer | Category ID |
| `youtube_video_id` | string | YouTube video ID |
| `duration` | string\|null | Video duration (e.g., "10:30") |
| `embed_url` | string | YouTube embed URL |
| `watch_url` | string | YouTube watch URL |
| `thumbnail_url` | string | High-quality thumbnail URL |
| `thumbnail_medium_url` | string | Medium-quality thumbnail URL |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

#### Book Contentable

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique book identifier |
| `title` | string | Book title |
| `description` | string\|null | Book description |
| `category_id` | integer | Category ID |
| `pages_count` | integer\|null | Number of pages |
| `published_date` | string\|null | Publication date (YYYY-MM-DD) |
| `publisher` | string\|null | Publisher name |
| `cover_image` | string\|null | Relative path to cover image |
| `cover_image_url` | string\|null | Full URL to cover image |
| `file` | string\|null | Relative path to book file |
| `file_url` | string\|null | Full URL to book file |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |

### Pagination Object

| Field | Type | Description |
|-------|------|-------------|
| `current_page` | integer | Current page number |
| `per_page` | integer | Number of items per page |
| `total` | integer | Total number of items |
| `last_page` | integer | Last page number |
| `from` | integer\|null | First item number on current page |
| `to` | integer\|null | Last item number on current page |
| `has_more` | boolean | Whether there are more pages |
| `next_page_url` | string\|null | URL for next page |
| `prev_page_url` | string\|null | URL for previous page |

### Content Type Summary Object

| Field | Type | Description |
|-------|------|-------------|
| `audio_count` | integer | Number of audio content items |
| `video_count` | integer | Number of video content items |
| `book_count` | integer | Number of book content items |
| `total_count` | integer | Total number of content items |

## Usage Examples

### Basic Request - Get All Content for a Category

```bash
curl -X GET "http://your-domain.com/api/v1/categories/1/contents" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### Paginated Request

```bash
curl -X GET "http://your-domain.com/api/v1/categories/1/contents?per_page=10&page=2" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### JavaScript/Fetch Example

```javascript
async function getCategoryContents(categoryId, page = 1, perPage = 15) {
  try {
    const response = await fetch(
      `http://your-domain.com/api/v1/categories/${categoryId}/contents?per_page=${perPage}&page=${page}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching category contents:', error);
    throw error;
  }
}

// Usage
getCategoryContents(1, 1, 20)
  .then(data => {
    console.log('Category:', data.category.name);
    console.log('Contents:', data.contents);
    console.log('Summary:', data.content_type_summary);
  })
  .catch(error => console.error('Error:', error));
```

## Key Features

- ✅ **Polymorphic Content Support**: Handles Audio, Video, and Book content types through a unified interface
- ✅ **Clean Response Structure**: No wrapper fields like 'success' or 'message' - only essential data
- ✅ **Comprehensive Content Data**: Includes full contentable model data with type-specific fields
- ✅ **Pagination Support**: Uses generic parameter names ('per_page', 'page') for consistency
- ✅ **Content Type Summary**: Provides counts of each content type in the category
- ✅ **Category Information**: Includes full category details and hierarchy information
- ✅ **Error Handling**: Appropriate HTTP status codes and clear error messages
- ✅ **Active Content Only**: Returns only active categories and content items
- ✅ **Ordered Results**: Content items ordered by creation date (newest first)
- ✅ **URL Generation**: Automatic generation of file URLs for audio, video thumbnails, book covers, and files

## Implementation Notes

- Only active categories (status = true) can be accessed
- Only active content items (status = true) are returned
- Content items are ordered by creation date (newest first)
- File URLs are automatically generated using Laravel's asset() helper
- The API maintains consistency with existing endpoint patterns
- Validation errors return only the 'errors' field
- Server errors return only the 'error' field
- All timestamps are in UTC and follow ISO 8601 format
