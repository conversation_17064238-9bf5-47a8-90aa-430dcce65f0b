# Category and Audio Models Implementation

This document outlines the implementation of Category and Audio models for the Laravel Filament admin panel.

## Overview

The implementation includes:
- ✅ Category model with proper fields and relationships
- ✅ Audio model with proper fields and relationships
- ✅ Database migrations with proper indexes and constraints
- ✅ Filament admin resources for both models
- ✅ JSON translations for English and Arabic
- ✅ Model factories for testing
- ✅ Comprehensive test coverage
- ✅ Sample data seeder

## Database Structure

### Categories Table
- `id` (Primary Key)
- `name` (String, Required) - The category name
- `status` (<PERSON><PERSON><PERSON>, Default: true) - Active/inactive status
- `category_type` (Enum) - One of: 'audio', 'video', 'reference', 'tweet'
- `created_at` (Timestamp)
- `updated_at` (Timestamp)
- **Indexes**: status, category_type, [status, category_type]

### Audio Table
- `id` (Primary Key)
- `title` (String, Required) - The audio title
- `description` (Text, Nullable) - Audio description
- `audio_file` (String) - Path/filename for the audio file
- `category_id` (Foreign Key) - References categories.id with cascade delete
- `created_at` (Timestamp)
- `updated_at` (Timestamp)
- **Indexes**: category_id, title

## Models

### Category Model (`app/Models/Category.php`)
- **Fillable**: name, status, category_type
- **Casts**: status (boolean), category_type (string)
- **Relationships**: hasMany Audio
- **Scopes**: 
  - `active()` - Filter active categories
  - `ofType($type)` - Filter by category type

### Audio Model (`app/Models/Audio.php`)
- **Fillable**: title, description, audio_file, category_id
- **Casts**: category_id (integer)
- **Relationships**: belongsTo Category
- **Scopes**:
  - `inCategory($categoryId)` - Filter by category
  - `byCategoryType($type)` - Filter by category type

## Filament Resources

### CategoryResource (`app/Filament/Resources/CategoryResource.php`)
- **Navigation Icon**: heroicon-o-folder
- **Form Fields**:
  - Name (TextInput, required, max 255)
  - Category Type (Select with options)
  - Status (Toggle, default true)
- **Table Columns**:
  - Name (searchable, sortable)
  - Category Type (badge with colors)
  - Status (icon column)
  - Audio Count (relationship count)
  - Timestamps (toggleable)
- **Filters**:
  - Category Type (select filter)
  - Active/Inactive status filters
- **Actions**: Edit, Delete with confirmation

### AudioResource (`app/Filament/Resources/AudioResource.php`)
- **Navigation Icon**: heroicon-o-musical-note
- **Form Fields**:
  - Title (TextInput, required, max 255)
  - Description (Textarea, nullable, max 1000)
  - Category (Select with relationship, searchable)
  - Audio File (FileUpload with audio file types)
- **Table Columns**:
  - Title (searchable, sortable)
  - Description (limited with tooltip)
  - Category (badge with type-based colors)
  - Audio File (basename with tooltip)
  - Timestamps (toggleable)
- **Filters**:
  - Category (relationship filter)
  - Category Type (custom query filter)
- **Actions**: Edit, Delete with confirmation

## File Structure

```
app/
├── Models/
│   ├── Category.php                    # Category model
│   └── Audio.php                       # Audio model
├── Filament/
│   └── Resources/
│       ├── CategoryResource.php        # Category admin interface
│       ├── CategoryResource/Pages/     # Category CRUD pages
│       ├── AudioResource.php           # Audio admin interface
│       └── AudioResource/Pages/        # Audio CRUD pages

database/
├── migrations/
│   ├── *_create_categories_table.php   # Categories table migration
│   └── *_create_audio_table.php        # Audio table migration
├── factories/
│   ├── CategoryFactory.php             # Category factory
│   └── AudioFactory.php                # Audio factory
└── seeders/
    └── CategorySeeder.php               # Sample data seeder

lang/
├── en.json                             # English translations
└── ar.json                             # Arabic translations

tests/
└── Feature/
    └── CategoryAudioTest.php            # Model tests
```

## Usage

### Creating Categories
```php
$category = Category::create([
    'name' => 'Islamic Lectures',
    'category_type' => 'audio',
    'status' => true,
]);
```

### Creating Audio Files
```php
$audio = Audio::create([
    'title' => 'Lecture Title',
    'description' => 'Optional description',
    'audio_file' => 'audio/filename.mp3',
    'category_id' => $category->id,
]);
```

### Using Relationships
```php
// Get all audio files for a category
$audioFiles = $category->audio;

// Get category for an audio file
$category = $audio->category;

// Count audio files in a category
$count = $category->audio()->count();
```

### Using Scopes
```php
// Get active categories
$activeCategories = Category::active()->get();

// Get audio categories
$audioCategories = Category::ofType('audio')->get();

// Get audio files by category type
$audioFiles = Audio::byCategoryType('audio')->get();
```

## Testing

Run the tests with:
```bash
php artisan test --filter=CategoryAudioTest
```

## Sample Data

Populate with sample data:
```bash
php artisan db:seed --class=CategorySeeder
```

## File Uploads

Audio files are stored in `storage/app/public/audio/` and accessible via the storage link.

Supported formats: MP3, WAV, OGG

## Admin Panel Access

Access the admin panel at: `http://your-domain.com/admin`

The Categories and Audio sections will be available in the navigation menu.
