# Video Model Implementation

This document outlines the implementation of the Video model for storing YouTube videos in the Laravel Filament admin panel.

## Overview

The Video model implementation includes:
- ✅ Video model with YouTube-specific fields and methods
- ✅ Database migration with proper indexes and constraints
- ✅ Filament VideoResource for admin panel management
- ✅ YouTube URL extraction and validation functionality
- ✅ Comprehensive test coverage
- ✅ Integration with existing Category system using CategoryType enum
- ✅ Factory and seeder support
- ✅ JSON translations for English and Arabic

## Database Structure

### Videos Table
- `id` (Primary Key)
- `title` (String, Required) - The video title
- `description` (Text, Nullable) - Video description
- `youtube_video_id` (String, Required, Unique) - YouTube video ID for embedding
- `duration` (String, Nullable) - Video duration (e.g., "10:30")
- `category_id` (Foreign Key) - References categories.id with cascade delete
- `created_at` (Timestamp)
- `updated_at` (Timestamp)
- **Indexes**: category_id, title, youtube_video_id (unique)

## Model Features

### Video Model (`app/Models/Video.php`)

#### Fillable Fields
- `title`, `description`, `youtube_video_id`, `duration`, `category_id`

#### Relationships
- `belongsTo` Category

#### Accessor Methods (using Laravel Attribute)
- `embedUrl()` - Returns YouTube embed URL
- `watchUrl()` - Returns YouTube watch URL  
- `thumbnailUrl()` - Returns high-quality thumbnail URL
- `thumbnailMediumUrl()` - Returns medium-quality thumbnail URL

#### Static Methods
- `extractVideoId(string $url)` - Extracts YouTube video ID from various URL formats
- `isValidYouTubeUrl(string $url)` - Validates if URL is a valid YouTube URL

#### Scope Methods
- `inCategory($categoryId)` - Filter by category
- `byCategoryType(CategoryType $type)` - Filter by category type
- `videoCategories()` - Filter to only video-type categories

### Category Model Updates
- Added `videos()` relationship method: `hasMany(Video::class)`

## YouTube Integration

### Supported URL Formats
The model can extract video IDs from these YouTube URL formats:
- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://www.youtube.com/embed/VIDEO_ID`
- `https://www.youtube.com/watch?v=VIDEO_ID&t=10s` (with parameters)

### Generated URLs
From a video ID, the model generates:
- **Embed URL**: `https://www.youtube.com/embed/{video_id}`
- **Watch URL**: `https://www.youtube.com/watch?v={video_id}`
- **Thumbnail URL**: `https://img.youtube.com/vi/{video_id}/maxresdefault.jpg`
- **Medium Thumbnail**: `https://img.youtube.com/vi/{video_id}/mqdefault.jpg`

## Filament Integration

### VideoResource (`app/Filament/Resources/VideoResource.php`)

#### Form Fields
- **Title**: TextInput (required, max 255)
- **Description**: Textarea (nullable, max 1000)
- **YouTube URL**: TextInput with live validation and auto-extraction
- **YouTube Video ID**: TextInput (readonly, auto-filled)
- **Duration**: TextInput (optional, format: MM:SS or HH:MM:SS)
- **Category**: Select (filtered to video-type categories only)

#### Table Columns
- **Thumbnail**: ImageColumn showing video thumbnail
- **Title**: TextColumn (searchable, sortable, with tooltip)
- **Description**: TextColumn (limited with tooltip)
- **Category**: TextColumn with badge and color coding
- **Duration**: TextColumn (sortable)
- **Video ID**: TextColumn (copyable, toggleable)
- **Timestamps**: DateTimeColumn (toggleable, hidden by default)

#### Actions
- **Watch**: Opens YouTube video in new tab
- **Edit**: Standard edit action
- **Delete**: With confirmation modal

#### Filters
- **Category**: SelectFilter (video categories only)

## Factory and Seeder

### VideoFactory (`database/factories/VideoFactory.php`)
- Generates unique YouTube video IDs using regex pattern
- Creates realistic video titles and descriptions
- Associates with video-type categories
- Includes `withVideoId(string $videoId)` method for testing

### CategorySeeder Updates
- Creates sample videos for video-type categories
- Maintains existing audio functionality
- Creates 2 videos per predefined video category
- Creates 1-3 videos per random video category

## Testing

### VideoTest (`tests/Feature/VideoTest.php`)
Comprehensive test coverage including:

1. **Relationship Tests**
   - Video belongs to category
   - Category can have multiple videos

2. **Scope Tests**
   - `inCategory()` scope functionality
   - `byCategoryType()` scope functionality
   - `videoCategories()` scope functionality

3. **YouTube Functionality Tests**
   - URL extraction from various formats
   - URL validation
   - Invalid URL handling

4. **Accessor Tests**
   - Embed URL generation
   - Watch URL generation
   - Thumbnail URL generation

5. **Factory Tests**
   - Valid video creation
   - Category type validation

6. **Integration Tests**
   - Manual video creation with YouTube URL extraction

## Usage Examples

### Creating Videos

```php
// Using factory
$video = Video::factory()->create();

// Manual creation with YouTube URL
$video = Video::create([
    'title' => 'Educational Video',
    'description' => 'Learn something new',
    'youtube_video_id' => Video::extractVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ'),
    'duration' => '3:45',
    'category_id' => $videoCategory->id,
]);

// Using factory with specific video ID
$video = Video::factory()->withVideoId('dQw4w9WgXcQ')->create();
```

### Using Relationships

```php
// Get all videos for a category
$videos = $category->videos;

// Get category for a video
$category = $video->category;

// Count videos in a category
$count = $category->videos()->count();
```

### Using YouTube Methods

```php
// Extract video ID from URL
$videoId = Video::extractVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

// Validate YouTube URL
$isValid = Video::isValidYouTubeUrl('https://youtu.be/dQw4w9WgXcQ');

// Get video URLs
$embedUrl = $video->embed_url;
$watchUrl = $video->watch_url;
$thumbnailUrl = $video->thumbnail_url;
```

### Using Scopes

```php
// Get videos by category
$categoryVideos = Video::inCategory($categoryId)->get();

// Get videos by category type
$allVideoTypeVideos = Video::byCategoryType(CategoryType::Video)->get();

// Get only videos from video categories
$videoOnlyVideos = Video::videoCategories()->get();
```

## Translations

### English (`lang/en.json`)
- `videos`, `video`, `youtube_url`, `youtube_video_id`, `duration`, `thumbnail`
- Form placeholders and help texts
- Action labels and success messages

### Arabic (`lang/ar.json`)
- `فيديوهات`, `فيديو`, `رابط يوتيوب`, `معرف فيديو يوتيوب`, `المدة`, `الصورة المصغرة`
- Localized form guidance and messages

## File Structure

```
app/
├── Models/
│   ├── Video.php                       # Video model
│   └── Category.php                    # Updated with videos relationship
├── Filament/
│   └── Resources/
│       ├── VideoResource.php           # Video admin interface
│       └── VideoResource/Pages/        # Video CRUD pages

database/
├── migrations/
│   └── *_create_videos_table.php       # Videos table migration
├── factories/
│   └── VideoFactory.php                # Video factory
└── seeders/
    └── CategorySeeder.php               # Updated seeder

tests/
└── Feature/
    └── VideoTest.php                    # Video model tests

lang/
├── en.json                             # English translations
└── ar.json                             # Arabic translations
```

## Admin Panel Access

The Video management is accessible at `/admin/videos` with:
- Create new videos with YouTube URL auto-extraction
- Edit existing videos
- Filter by video categories
- View video thumbnails in table
- Direct links to watch videos on YouTube
- Copy video IDs to clipboard

## Integration Notes

- ✅ Seamlessly integrates with existing Category system
- ✅ Uses CategoryType enum for type safety
- ✅ Follows same patterns as Audio model
- ✅ Maintains consistency with existing codebase
- ✅ All tests passing (13 tests, 64 assertions)
- ✅ Database properly seeded with sample data
