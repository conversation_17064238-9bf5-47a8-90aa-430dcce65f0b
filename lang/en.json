{"admins": "Admins", "admin": "Admin", "admin_plural": "Admins", "user_management": "User Management", "admins_list": "Admins", "create_admin": "Create Admin", "edit_admin": "Edit Admin", "view_admin": "View Admin", "id": "ID", "name": "Name", "email": "Email Address", "email_verified_at": "Email Verified At", "password": "Password", "password_confirmation": "Password Confirmation", "created_at": "Created At", "updated_at": "Updated At", "name_placeholder": "Enter admin name", "email_placeholder": "Enter email address", "password_placeholder": "Enter password", "password_confirmation_placeholder": "Confirm password", "name_help": "The full name of the admin user", "email_help": "A valid email address for login and notifications", "email_verified_at_help": "When the email address was verified (leave empty for unverified)", "password_help": "Minimum 8 characters required", "password_confirmation_help": "Must match the password above", "id_column": "ID", "name_column": "Name", "email_column": "Email", "email_verified_column": "<PERSON><PERSON>", "created_at_column": "Created At", "updated_at_column": "Updated At", "verified_filter": "Verified Only", "unverified_filter": "Unverified Only", "all_admins_filter": "All Admins", "verify_email": "<PERSON><PERSON><PERSON>", "resend_verification": "Resend Verification", "admin_created": "Admin created successfully!", "admin_updated": "Admin updated successfully!", "admin_deleted": "Admin deleted successfully!", "admins_bulk_deleted": "Selected admins deleted successfully!", "email_verified": "Email verified successfully!", "verification_sent": "Verification email sent!", "name_required": "Name is required", "name_max": "Name cannot exceed 255 characters", "email_required": "Email is required", "email_invalid": "Please enter a valid email address", "email_unique": "This email is already taken", "email_max": "Email cannot exceed 255 characters", "password_required": "Password is required", "password_min": "Password must be at least 8 characters", "password_confirmed": "Password confirmation does not match", "verified": "Verified", "unverified": "Unverified", "active": "Active", "inactive": "Inactive", "delete_confirmation": "Are you sure you want to delete this admin?", "bulk_delete_confirmation": "Are you sure you want to delete the selected admins?", "verify_email_confirmation": "Are you sure you want to mark this email as verified?", "categories": "Categories", "category": "Category", "category_type": "Category Type", "status": "Status", "audio": "Audio", "audios": "Audios", "video": "Video", "videos": "Videos", "reference": "Reference", "references": "References", "tweet": "Tweet", "tweets": "Tweets", "category_name_placeholder": "Enter category name", "category_name_help": "The name of the category", "category_type_placeholder": "Select category type", "category_type_help": "The type of content this category will contain", "category_status_help": "Whether this category is active or inactive", "active_filter": "Active Only", "inactive_filter": "Inactive Only", "category_deleted": "Category deleted successfully!", "categories_bulk_deleted": "Selected categories deleted successfully!", "audio_count": "Audio Count", "audio_item": "Audio Item", "title": "Title", "description": "Description", "audio_file": "Audio File", "select_category": "Select a category", "audio_title_placeholder": "Enter audio title", "audio_title_help": "The title of the audio file", "audio_description_placeholder": "Enter audio description", "audio_description_help": "Optional description of the audio content", "audio_category_help": "Select the category this audio belongs to", "audio_file_help": "Upload an audio file (MP3, WAV, OGG formats supported)", "audio_deleted": "Audio deleted successfully!", "audio_bulk_deleted": "Selected audio files deleted successfully!", "youtube_url": "YouTube URL", "youtube_video_id": "YouTube Video ID", "duration": "Duration", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "video_id": "Video ID", "watch": "Watch", "video_title_placeholder": "Enter video title", "video_title_help": "The title of the YouTube video", "video_description_placeholder": "Enter video description", "video_description_help": "Optional description of the video content", "youtube_url_placeholder": "https://www.youtube.com/watch?v=...", "youtube_url_help": "Enter a valid YouTube URL and the video ID will be extracted automatically", "youtube_video_id_help": "This field is automatically filled when you enter a YouTube URL", "duration_placeholder": "e.g., 10:30", "duration_help": "Video duration in MM:SS or HH:MM:SS format", "video_category_help": "Select the video category this video belongs to", "video_id_copied": "Video ID copied to clipboard!", "video_deleted": "Video deleted successfully!", "videos_bulk_deleted": "Selected videos deleted successfully!", "parent_category": "Parent Category", "select_parent_category": "Select parent category (optional)", "parent_category_help": "Choose a parent category to create a hierarchical structure", "no_parent": "No Parent", "subcategories_count": "Subcategories", "videos_count": "Videos Count", "parent_categories_only": "Parent Categories Only", "subcategories_only": "Subcategories Only", "category_cannot_be_own_parent": "A category cannot be its own parent", "circular_reference_detected": "Circular reference detected - this would create an infinite loop", "contents": "Contents", "content": "Content", "content_plural": "Contents", "content_management": "Content Management", "create_content": "Create Content", "edit_content": "Edit Content", "view_content": "View Content", "basic_information": "Basic Information", "audio_details": "Audio Details", "video_details": "Video Details", "book_details": "Book Details", "content_type": "Content Type", "select_content_type": "Select content type", "content_type_help": "Choose the type of content you want to create", "content_title_placeholder": "Enter content title", "content_title_help": "The main title for this content item", "content_description_placeholder": "Enter content description", "content_description_help": "Optional description explaining what this content is about", "content_category_help": "Select the category this content belongs to", "content_status_help": "Whether this content is active and visible to users", "preview": "Preview", "details": "Details", "content_details": "Content Details", "has_file": "Has File", "has_cover_image": "Has Cover Image", "view": "View", "content_deleted": "Content deleted successfully!", "content_bulk_deleted": "Selected content items deleted successfully!", "book": "Book", "books": "Books", "pages_count": "Pages Count", "published_date": "Published Date", "publisher": "Publisher", "cover_image": "Cover Image", "book_file": "Book File", "book_title_placeholder": "Enter book title", "book_title_help": "The title of the book", "book_description_placeholder": "Enter book description", "book_description_help": "Optional description of the book content", "pages_count_placeholder": "e.g., 250", "pages_count_help": "Total number of pages in the book", "published_date_placeholder": "Select publication date", "published_date_help": "When the book was originally published", "publisher_placeholder": "Enter publisher name", "publisher_help": "The name of the publishing company or organization", "cover_image_help": "Upload a cover image for the book (JPEG, PNG, WebP formats supported)", "book_file_help": "Upload the book file (PDF, EPUB formats supported)", "book_category_help": "Select the category this book belongs to", "book_deleted": "Book deleted successfully!", "books_bulk_deleted": "Selected books deleted successfully!"}