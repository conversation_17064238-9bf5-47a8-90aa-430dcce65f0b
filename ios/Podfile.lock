PODS:
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - open_file_ios (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - OrderedSet

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  syncfusion_flutter_pdfviewer: 90dc48305d2e33d4aa20681d1e98ddeda891bc14
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.16.2
