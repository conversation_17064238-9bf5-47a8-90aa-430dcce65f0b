# ContentResource JSON Translations Documentation

This document outlines the comprehensive JSON translation keys added for the unified ContentResource in the Laravel Filament admin interface.

## Overview

The ContentResource translations follow the existing JSON translation pattern used in the project, using simple English words as keys rather than hierarchical dot notation. All translations are available in both English and Arabic.

## Translation Categories

### 1. Navigation and Labels

| Key | English | Arabic |
|-----|---------|--------|
| `contents` | Contents | المحتويات |
| `content` | Content | محتوى |
| `content_plural` | Contents | المحتويات |
| `content_management` | Content Management | إدارة المحتوى |
| `create_content` | Create Content | إنشاء محتوى |
| `edit_content` | Edit Content | تعديل المحتوى |
| `view_content` | View Content | عرض المحتوى |

### 2. Section Headings

| Key | English | Arabic |
|-----|---------|--------|
| `basic_information` | Basic Information | المعلومات الأساسية |
| `audio_details` | Audio Details | تفاصيل الصوت |
| `video_details` | Video Details | تفاصيل الفيديو |
| `book_details` | Book Details | تفاصيل الكتاب |

### 3. Form Field Labels

| Key | English | Arabic |
|-----|---------|--------|
| `content_type` | Content Type | نوع المحتوى |
| `select_content_type` | Select content type | اختر نوع المحتوى |
| `preview` | Preview | معاينة |
| `details` | Details | التفاصيل |
| `content_details` | Content Details | تفاصيل المحتوى |
| `has_file` | Has File | يحتوي على ملف |
| `has_cover_image` | Has Cover Image | يحتوي على صورة غلاف |

### 4. Book-Specific Fields

| Key | English | Arabic |
|-----|---------|--------|
| `book` | Book | كتاب |
| `books` | Books | الكتب |
| `pages_count` | Pages Count | عدد الصفحات |
| `published_date` | Published Date | تاريخ النشر |
| `publisher` | Publisher | الناشر |
| `cover_image` | Cover Image | صورة الغلاف |
| `book_file` | Book File | ملف الكتاب |

### 5. Placeholder Text

| Key | English | Arabic |
|-----|---------|--------|
| `content_title_placeholder` | Enter content title | أدخل عنوان المحتوى |
| `content_description_placeholder` | Enter content description | أدخل وصف المحتوى |
| `book_title_placeholder` | Enter book title | أدخل عنوان الكتاب |
| `book_description_placeholder` | Enter book description | أدخل وصف الكتاب |
| `pages_count_placeholder` | e.g., 250 | مثال: 250 |
| `published_date_placeholder` | Select publication date | اختر تاريخ النشر |
| `publisher_placeholder` | Enter publisher name | أدخل اسم الناشر |

### 6. Help Text

| Key | English | Arabic |
|-----|---------|--------|
| `content_type_help` | Choose the type of content you want to create | اختر نوع المحتوى الذي تريد إنشاؤه |
| `content_title_help` | The main title for this content item | العنوان الرئيسي لهذا المحتوى |
| `content_description_help` | Optional description explaining what this content is about | وصف اختياري يوضح ما يتعلق به هذا المحتوى |
| `content_category_help` | Select the category this content belongs to | اختر الفئة التي ينتمي إليها هذا المحتوى |
| `content_status_help` | Whether this content is active and visible to users | ما إذا كان هذا المحتوى نشطاً ومرئياً للمستخدمين |
| `book_title_help` | The title of the book | عنوان الكتاب |
| `book_description_help` | Optional description of the book content | وصف اختياري لمحتوى الكتاب |
| `pages_count_help` | Total number of pages in the book | العدد الإجمالي للصفحات في الكتاب |
| `published_date_help` | When the book was originally published | متى تم نشر الكتاب أصلاً |
| `publisher_help` | The name of the publishing company or organization | اسم شركة النشر أو المؤسسة |
| `cover_image_help` | Upload a cover image for the book (JPEG, PNG, WebP formats supported) | ارفع صورة غلاف للكتاب (تنسيقات JPEG، PNG، WebP مدعومة) |
| `book_file_help` | Upload the book file (PDF, EPUB formats supported) | ارفع ملف الكتاب (تنسيقات PDF، EPUB مدعومة) |
| `book_category_help` | Select the category this book belongs to | اختر الفئة التي ينتمي إليها هذا الكتاب |

### 7. Actions and Messages

| Key | English | Arabic |
|-----|---------|--------|
| `view` | View | عرض |
| `content_deleted` | Content deleted successfully! | تم حذف المحتوى بنجاح! |
| `content_bulk_deleted` | Selected content items deleted successfully! | تم حذف عناصر المحتوى المحددة بنجاح! |
| `book_deleted` | Book deleted successfully! | تم حذف الكتاب بنجاح! |
| `books_bulk_deleted` | Selected books deleted successfully! | تم حذف الكتب المحددة بنجاح! |

## Usage in ContentResource

The translations are used throughout the ContentResource using Laravel's `__()` helper function:

```php
// Navigation labels
public static function getNavigationLabel(): string
{
    return __('contents');
}

// Form field labels
Forms\Components\TextInput::make('title')
    ->label(__('title'))
    ->placeholder(__('content_title_placeholder'))
    ->helperText(__('content_title_help'))

// Section headings
Forms\Components\Section::make(__('basic_information'))

// Table column headers
Tables\Columns\TextColumn::make('title')
    ->label(__('title'))

// Action labels and messages
Tables\Actions\DeleteAction::make()
    ->successNotificationTitle(__('content_deleted'))
```

## File Locations

- **English translations**: `lang/en.json`
- **Arabic translations**: `lang/ar.json`
- **Test file**: `tests/Feature/ContentResourceTranslationTest.php`

## Testing

All translations are covered by comprehensive tests in `ContentResourceTranslationTest.php` which verify:
- All English translations work correctly
- All Arabic translations work correctly
- Content type options are properly translated
- All 88 translation assertions pass

## Key Features

- ✅ **Consistent Pattern**: Follows existing translation key structure
- ✅ **Simple Keys**: Uses English words instead of dot notation
- ✅ **Bilingual Support**: Complete English and Arabic translations
- ✅ **Comprehensive Coverage**: All UI elements are translated
- ✅ **Test Coverage**: 100% test coverage for all translation keys
- ✅ **RTL Support**: Arabic translations support right-to-left layout
- ✅ **Context-Aware**: Different help text for different content types
