# Hierarchical Categories API Documentation

## Overview

The Hierarchical Categories API provides a clean endpoint for retrieving all main categories (parent categories) along with their immediate subcategories in a structured format. This endpoint is designed to help frontend applications build category navigation menus and understand the hierarchical relationships between categories.

## Base URL

```
http://your-domain.com/api/v1
```

## Authentication

Currently, no authentication is required for this endpoint.

## Endpoint

### Get Hierarchical Categories

Retrieves all main categories (parent categories with `parent_id = null`) that are active, along with their immediate active subcategories. The response always includes audio and video counts for each category.

**Endpoint:** `GET /api/v1/categories`

#### Query Parameters

This endpoint does not accept any query parameters. It returns all active parent categories with their subcategories and content counts.

#### Response Structure

```json
{
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Audio Content",
        "status": true,
        "parent_id": null,
        "audio_count": 5,
        "videos_count": 0,
        "subcategories_count": 3,
        "created_at": "2025-06-02T17:22:22.000000Z",
        "updated_at": "2025-06-02T17:22:22.000000Z",
        "subcategories": [
          {
            "id": 2,
            "name": "Islamic Lectures",
            "status": true,
            "parent_id": 1,
            "audio_count": 3,
            "videos_count": 0,
            "subcategories_count": 0,
            "created_at": "2025-06-02T17:22:22.000000Z",
            "updated_at": "2025-06-02T17:22:22.000000Z"
          }
        ]
      }
    ]
  }
}
```

## Data Models

### Category Object

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique category identifier |
| `name` | string | Category name |
| `status` | boolean | Whether the category is active |
| `parent_id` | integer\|null | ID of parent category (null for main categories) |
| `audio_count` | integer | Number of audio records in this category |
| `videos_count` | integer | Number of video records in this category |
| `subcategories_count` | integer | Number of active subcategories |
| `created_at` | string | ISO 8601 timestamp |
| `updated_at` | string | ISO 8601 timestamp |
| `subcategories` | array | Array of immediate subcategory objects |

## Usage Examples

### Basic Request - Get All Main Categories with Subcategories
```bash
curl -X GET "http://your-domain.com/api/v1/categories" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### JavaScript/Fetch Example
```javascript
fetch('/api/v1/categories', {
  method: 'GET',
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Categories:', data.data.categories);
  // Build navigation menu from hierarchical data
})
.catch(error => console.error('Error:', error));
```

## Error Responses

### Server Error (500)
```json
{
  "error": "An error occurred while retrieving categories"
}
```

## Features

- **Hierarchical Structure**: Returns parent categories with their immediate subcategories
- **Active Categories Only**: Only returns categories with `status = true`
- **Alphabetical Ordering**: Categories and subcategories are ordered alphabetically by name
- **Content Counts**: Always includes audio and video counts for each category
- **Clean Response**: No wrapper fields - follows the same pattern as other API endpoints
- **Performance Optimized**: Efficient database queries with eager loading
- **Simple Interface**: No query parameters needed - straightforward endpoint

## Use Cases

1. **Navigation Menus**: Build hierarchical navigation menus for frontend applications
2. **Category Selection**: Provide category options in forms and filters
3. **Content Organization**: Display content organization structure to users
4. **Analytics**: Show category statistics with content counts
5. **Mobile Apps**: Lightweight endpoint for mobile category browsing

## Notes

- Only returns two levels of hierarchy (parent → subcategories)
- Inactive categories are excluded from the response
- Subcategories are limited to immediate children only
- Content counts include direct associations only (not recursive)
- All timestamps are in UTC and follow ISO 8601 format
- Response follows the same clean structure as other API endpoints (no 'success'/'message' wrapper fields)

## Testing

The API includes comprehensive test coverage:

- 5 test methods covering all functionality
- 77 assertions validating response structure and data
- Tests for hierarchical structure, counts, ordering, and edge cases

Run tests with:
```bash
php artisan test --filter=CategoryControllerTest
```
