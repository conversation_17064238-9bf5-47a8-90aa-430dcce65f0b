<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SetLocaleCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'locale:set {locale : The locale to set (en, ar)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set the application locale in the .env file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $locale = $this->argument('locale');

        // Validate locale
        $supportedLocales = ['en', 'ar'];
        if (!in_array($locale, $supportedLocales)) {
            $this->error("Unsupported locale: {$locale}");
            $this->info("Supported locales: " . implode(', ', $supportedLocales));
            return 1;
        }

        // Check if .env file exists
        $envPath = base_path('.env');
        if (!File::exists($envPath)) {
            $this->error('.env file not found');
            return 1;
        }

        // Read .env file
        $envContent = File::get($envPath);

        // Update APP_LOCALE
        if (preg_match('/^APP_LOCALE=.*$/m', $envContent)) {
            $envContent = preg_replace('/^APP_LOCALE=.*$/m', "APP_LOCALE={$locale}", $envContent);
        } else {
            $envContent .= "\nAPP_LOCALE={$locale}";
        }

        // Write back to .env file
        File::put($envPath, $envContent);

        $this->info("Application locale set to: {$locale}");
        $this->info("Please restart your server for changes to take effect.");

        return 0;
    }
}
