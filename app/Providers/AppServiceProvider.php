<?php

namespace App\Providers;

use App\Models\Article;
use App\Models\Audio;
use App\Models\Book;
use App\Models\Post;
use App\Models\Video;
use Filament\Resources\Pages\Page;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Page::inlineLabels(true);

        // Configure morph map for polymorphic relationships
        Relation::morphMap([
            'audio' => Audio::class,
            'video' => Video::class,
            'book' => Book::class,
            'article' => Article::class,
            'post' => Post::class,
        ]);
    }
}
