<?php

namespace App\Filament\Resources\ContentResource\Pages;

use App\Filament\Resources\ContentResource;
use App\Models\Audio;
use App\Models\Book;
use App\Models\Video;
use Filament\Resources\Pages\CreateRecord;

class CreateContent extends CreateRecord
{
    protected static string $resource = ContentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Extract contentable data
        $contentableData = $data['contentable'] ?? [];
        unset($data['contentable']);

        // Create the contentable model first
        if (!empty($data['contentable_type']) && !empty($contentableData)) {
            $contentableModel = $this->createContentableModel($data['contentable_type'], $contentableData);
            $data['contentable_id'] = $contentableModel->id;
        }

        return $data;
    }

    private function createContentableModel(string $type, array $data)
    {
        return match ($type) {
            'App\\Models\\Audio' => Audio::create($data),
            'App\\Models\\Video' => Video::create($data),
            'App\\Models\\Book' => Book::create($data),
            default => throw new \InvalidArgumentException("Unknown contentable type: {$type}"),
        };
    }
}
