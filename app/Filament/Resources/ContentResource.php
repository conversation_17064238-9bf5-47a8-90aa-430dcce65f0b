<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContentResource\Pages;
use App\Models\Content;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ContentResource extends Resource
{
    protected static ?string $model = Content::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-duplicate';

    public static function getNavigationLabel(): string
    {
        return __('contents');
    }

    public static function getModelLabel(): string
    {
        return __('content');
    }

    public static function getPluralModelLabel(): string
    {
        return __('contents');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('basic_information'))
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('title'))
                            ->placeholder(__('content_title_placeholder'))
                            ->helperText(__('content_title_help'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label(__('description'))
                            ->placeholder(__('content_description_placeholder'))
                            ->helperText(__('content_description_help'))
                            ->rows(3)
                            ->maxLength(1000),
                        Forms\Components\Select::make('category_id')
                            ->label(__('category'))
                            ->placeholder(__('select_category'))
                            ->helperText(__('content_category_help'))
                            ->relationship('category', 'name', function (Builder $query) {
                                return $query->where('status', true);
                            })
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('contentable_type')
                            ->label(__('content_type'))
                            ->placeholder(__('select_content_type'))
                            ->helperText(__('content_type_help'))
                            ->options([
                                'App\\Models\\Audio' => __('audio'),
                                'App\\Models\\Video' => __('video'),
                                'App\\Models\\Book' => __('book'),
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set) {
                                // Clear contentable_id when type changes
                                $set('contentable_id', null);
                            }),
                        Forms\Components\Toggle::make('status')
                            ->label(__('status'))
                            ->helperText(__('content_status_help'))
                            ->default(true),
                    ]),

                // Audio-specific fields
                Forms\Components\Section::make(__('audio_details'))
                    ->schema([
                        Forms\Components\FileUpload::make('contentable.audio_file')
                            ->label(__('audio_file'))
                            ->helperText(__('audio_file_help'))
                            ->acceptedFileTypes(['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/ogg'])
                            ->directory('audio')
                            ->required(),
                    ])
                    ->visible(fn(Forms\Get $get): bool => $get('contentable_type') === 'App\\Models\\Audio'),

                // Video-specific fields
                Forms\Components\Section::make(__('video_details'))
                    ->schema([
                        Forms\Components\TextInput::make('contentable.youtube_url')
                            ->label(__('youtube_url'))
                            ->placeholder(__('youtube_url_placeholder'))
                            ->helperText(__('youtube_url_help'))
                            ->url()
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                if ($state) {
                                    $videoId = \App\Models\Video::extractVideoId($state);
                                    if ($videoId) {
                                        $set('contentable.youtube_video_id', $videoId);
                                    }
                                }
                            }),
                        Forms\Components\TextInput::make('contentable.youtube_video_id')
                            ->label(__('youtube_video_id'))
                            ->helperText(__('youtube_video_id_help'))
                            ->required()
                            ->maxLength(11)
                            ->readOnly(),
                        Forms\Components\TextInput::make('contentable.duration')
                            ->label(__('duration'))
                            ->placeholder(__('duration_placeholder'))
                            ->helperText(__('duration_help'))
                            ->maxLength(10),
                    ])
                    ->visible(fn(Forms\Get $get): bool => $get('contentable_type') === 'App\\Models\\Video'),

                // Book-specific fields
                Forms\Components\Section::make(__('book_details'))
                    ->schema([
                        Forms\Components\TextInput::make('contentable.pages_count')
                            ->label(__('pages_count'))
                            ->placeholder(__('pages_count_placeholder'))
                            ->helperText(__('pages_count_help'))
                            ->numeric()
                            ->minValue(1),
                        Forms\Components\DatePicker::make('contentable.published_date')
                            ->label(__('published_date'))
                            ->placeholder(__('published_date_placeholder'))
                            ->helperText(__('published_date_help'))
                            ->native(false),
                        Forms\Components\TextInput::make('contentable.publisher')
                            ->label(__('publisher'))
                            ->placeholder(__('publisher_placeholder'))
                            ->helperText(__('publisher_help'))
                            ->maxLength(255),
                        Forms\Components\FileUpload::make('contentable.cover_image')
                            ->label(__('cover_image'))
                            ->helperText(__('cover_image_help'))
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->directory('books/covers')
                            ->image()
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('3:4')
                            ->imageResizeTargetWidth('300')
                            ->imageResizeTargetHeight('400'),
                        Forms\Components\FileUpload::make('contentable.file')
                            ->label(__('book_file'))
                            ->helperText(__('book_file_help'))
                            ->acceptedFileTypes(['application/pdf', 'application/epub+zip'])
                            ->directory('books/files'),
                    ])
                    ->visible(fn(Forms\Get $get): bool => $get('contentable_type') === 'App\\Models\\Book'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Thumbnail/Preview column - shows different previews based on content type
                Tables\Columns\ImageColumn::make('preview')
                    ->label(__('preview'))
                    ->size(60)
                    ->square()
                    ->getStateUsing(function ($record) {
                        if ($record->contentable_type === 'App\\Models\\Video' && $record->contentable) {
                            return $record->contentable->thumbnail_medium_url;
                        } elseif ($record->contentable_type === 'App\\Models\\Book' && $record->contentable && $record->contentable->cover_image) {
                            return asset('storage/' . $record->contentable->cover_image);
                        }
                        return null;
                    })
                    ->defaultImageUrl(function ($record) {
                        return match ($record->contentable_type) {
                            'App\\Models\\Audio' => asset('assets/images/audio-placeholder.png'),
                            'App\\Models\\Video' => asset('assets/images/video-placeholder.png'),
                            'App\\Models\\Book' => asset('assets/images/book-placeholder.png'),
                            default => asset('assets/images/content-placeholder.png'),
                        };
                    }),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('title'))
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('description'))
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('category.name')
                    ->label(__('category'))
                    ->badge()
                    ->color('success')
                    ->sortable(),
                Tables\Columns\TextColumn::make('contentable_type')
                    ->label(__('content_type'))
                    ->formatStateUsing(fn(string $state): string => class_basename($state))
                    ->badge()
                    ->color(fn(string $state): string => match (class_basename($state)) {
                        'Audio' => 'primary',
                        'Video' => 'success',
                        'Book' => 'warning',
                        default => 'gray',
                    })
                    ->sortable(),
                // Content-specific details column
                Tables\Columns\TextColumn::make('content_details')
                    ->label(__('details'))
                    ->getStateUsing(function ($record) {
                        if (!$record->contentable) return '-';

                        return match ($record->contentable_type) {
                            'App\\Models\\Audio' => $record->contentable->audio_file ? basename($record->contentable->audio_file) : '-',
                            'App\\Models\\Video' => $record->contentable->duration ?? $record->contentable->youtube_video_id ?? '-',
                            'App\\Models\\Book' => $record->contentable->publisher ?? ($record->contentable->pages_count ? $record->contentable->pages_count . ' pages' : '-'),
                            default => '-',
                        };
                    })
                    ->limit(20),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('status'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label(__('category'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('contentable_type')
                    ->label(__('content_type'))
                    ->options([
                        'App\\Models\\Audio' => __('audio'),
                        'App\\Models\\Video' => __('video'),
                        'App\\Models\\Book' => __('book'),
                    ]),
                Tables\Filters\TernaryFilter::make('status')
                    ->label(__('status'))
                    ->boolean()
                    ->trueLabel(__('active'))
                    ->falseLabel(__('inactive'))
                    ->native(false),
            ])
            ->actions([
                // Content-type specific actions
                Tables\Actions\Action::make('view_content')
                    ->label(__('view'))
                    ->icon(function ($record) {
                        return match ($record->contentable_type) {
                            'App\\Models\\Audio' => 'heroicon-o-play',
                            'App\\Models\\Video' => 'heroicon-o-play',
                            'App\\Models\\Book' => 'heroicon-o-eye',
                            default => 'heroicon-o-eye',
                        };
                    })
                    ->url(function ($record) {
                        if ($record->contentable_type === 'App\\Models\\Video' && $record->contentable) {
                            return $record->contentable->watch_url;
                        } elseif ($record->contentable_type === 'App\\Models\\Book' && $record->contentable && $record->contentable->file) {
                            return asset('storage/' . $record->contentable->file);
                        } elseif ($record->contentable_type === 'App\\Models\\Audio' && $record->contentable && $record->contentable->audio_file) {
                            return asset('storage/' . $record->contentable->audio_file);
                        }
                        return null;
                    })
                    ->openUrlInNewTab()
                    ->visible(function ($record) {
                        return match ($record->contentable_type) {
                            'App\\Models\\Video' => $record->contentable && $record->contentable->youtube_video_id,
                            'App\\Models\\Book' => $record->contentable && $record->contentable->file,
                            'App\\Models\\Audio' => $record->contentable && $record->contentable->audio_file,
                            default => false,
                        };
                    }),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('delete_confirmation'))
                    ->successNotificationTitle(__('content_deleted')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('bulk_delete_confirmation'))
                        ->successNotificationTitle(__('content_bulk_deleted')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContents::route('/'),
            'create' => Pages\CreateContent::route('/create'),
            'edit' => Pages\EditContent::route('/{record}/edit'),
        ];
    }
}
