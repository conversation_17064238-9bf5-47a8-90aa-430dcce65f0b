<?php

namespace App\Filament\Resources\AdminResource\Pages;

use App\Filament\Resources\AdminResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAdmin extends EditRecord
{
    protected static string $resource = AdminResource::class;

    public function getTitle(): string
    {
        return __('edit_admin');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return __('admin_updated');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalHeading(__('delete_confirmation'))
                ->successNotificationTitle(__('admin_deleted')),
        ];
    }
}
