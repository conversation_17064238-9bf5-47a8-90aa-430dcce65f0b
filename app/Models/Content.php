<?php

namespace App\Models;

use App\Enums\ContentType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Content extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'status',
        'category_id',
        'contentable_type',
        'contentable_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'status' => 'boolean',
            'category_id' => 'integer',
        ];
    }

    /**
     * Get the category that owns the content.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the contentable model (Audio, Video, etc.).
     */
    public function contentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include active content.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeInCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to filter by content type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('contentable_type', $type);
    }

    /**
     * Check if this content is of a specific type.
     */
    public function isType(string $type): bool
    {
        return $this->contentable_type === $type;
    }

    /**
     * Get the content type name (e.g., 'Audio', 'Video').
     */
    public function getContentTypeName(): string
    {
        if (!$this->contentable_type) {
            return 'Unknown';
        }

        $contentType = ContentType::fromValue($this->contentable_type);
        return $contentType?->getLabel() ?? class_basename($this->contentable_type);
    }

    /**
     * Get the ContentType enum for this content.
     */
    public function getContentTypeEnum(): ?ContentType
    {
        return ContentType::fromValue($this->contentable_type);
    }

    /**
     * Check if this content is of a specific ContentType.
     */
    public function isContentType(ContentType $type): bool
    {
        return $this->contentable_type === $type->value;
    }

    /**
     * Get the icon for this content type.
     */
    public function getContentTypeIcon(): string
    {
        $contentType = $this->getContentTypeEnum();
        return $contentType?->getIcon() ?? 'heroicon-o-question-mark-circle';
    }

    /**
     * Get the color for this content type.
     */
    public function getContentTypeColor(): string
    {
        $contentType = $this->getContentTypeEnum();
        return $contentType?->getColor() ?? 'gray';
    }
}
