<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Category extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'status',
        'parent_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'status' => 'boolean',
            'parent_id' => 'integer',
        ];
    }

    /**
     * Get all audio files for this category through content.
     */
    public function audio(): HasManyThrough
    {
        return $this->hasManyThrough(
            Audio::class,
            Content::class,
            'category_id', // Foreign key on contents table
            'id', // Foreign key on audios table
            'id', // Local key on categories table
            'contentable_id' // Local key on contents table
        )->where('contents.contentable_type', 'audio');
    }

    /**
     * Get all videos for this category through content.
     */
    public function videos(): HasManyThrough
    {
        return $this->hasManyThrough(
            Video::class,
            Content::class,
            'category_id', // Foreign key on contents table
            'id', // Foreign key on videos table
            'id', // Local key on categories table
            'contentable_id' // Local key on contents table
        )->where('contents.contentable_type', 'video');
    }

    /**
     * Get all content for this category.
     */
    public function contents(): HasMany
    {
        return $this->hasMany(Content::class);
    }

    /**
     * Get all books for this category through content.
     */
    public function books(): HasManyThrough
    {
        return $this->hasManyThrough(
            Book::class,
            Content::class,
            'category_id', // Foreign key on contents table
            'id', // Foreign key on books table
            'id', // Local key on categories table
            'contentable_id' // Local key on contents table
        )->where('contents.contentable_type', 'book');
    }

    /**
     * Get the parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * Get all subcategories (children) for this category.
     */
    public function subcategories(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Check if this category has subcategories.
     */
    public function isParent(): bool
    {
        return $this->subcategories()->exists();
    }

    /**
     * Check if this category has a parent.
     */
    public function isChild(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * Get all ancestor categories up the hierarchy.
     */
    public function getAncestors()
    {
        $ancestors = collect();
        $current = $this->parent;

        while ($current) {
            $ancestors->push($current);
            $current = $current->parent;
        }

        return $ancestors;
    }

    /**
     * Get all descendant categories down the hierarchy.
     */
    public function getDescendants()
    {
        $descendants = collect();

        foreach ($this->subcategories as $subcategory) {
            $descendants->push($subcategory);
            $descendants = $descendants->merge($subcategory->getDescendants());
        }

        return $descendants;
    }
}
