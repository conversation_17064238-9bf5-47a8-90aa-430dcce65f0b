<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\HierarchicalCategoryCollection;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CategoryController extends Controller
{
    /**
     * Get all main categories (parent categories) with their immediate subcategories.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCategories(Request $request): JsonResponse
    {
        try {
            // Get parent categories (where parent_id is null) that are active
            $categories = Category::query()
                ->whereNull('parent_id')
                ->where('status', true)
                ->withCount(['audio', 'videos'])
                ->with(['subcategories' => function ($query) {
                    $query->where('status', true)
                        ->withCount(['audio', 'videos'])
                        ->orderBy('name');
                }])
                ->orderBy('name')
                ->get();

            return response()->json([
                'data' => new HierarchicalCategoryCollection($categories),
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error retrieving hierarchical categories: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
            ]);

            return response()->json([
                'error' => 'An error occurred while retrieving categories',
            ], 500);
        }
    }
}
