<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CategoryContentCollection;
use App\Models\Category;
use App\Models\Content;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class ContentController extends Controller
{
    /**
     * Get all content items belonging to a specific category.
     *
     * @param Request $request
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getCategoryContents(Request $request, int $categoryId): JsonResponse
    {
        try {
            // Validate query parameters
            $validated = $request->validate([
                'per_page' => 'integer|min:1|max:100',
                'page' => 'integer|min:1',
            ]);

            // Check if category exists and is active
            $category = Category::where('id', $categoryId)
                ->where('status', true)
                ->first();

            if (!$category) {
                return response()->json([
                    'error' => 'Category not found or inactive',
                ], 404);
            }

            // Set pagination parameters
            $perPage = $validated['per_page'] ?? 15;
            $page = $validated['page'] ?? 1;

            // Get content items for this category with their contentable models
            $contents = Content::query()
                ->where('category_id', $categoryId)
                ->where('status', true)
                ->with(['contentable', 'category'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            return response()->json([
                'data' => new CategoryContentCollection($contents, $category),
            ], 200);

        } catch (ValidationException $e) {
            return response()->json([
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error retrieving category contents: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
                'category_id' => $categoryId,
            ]);

            return response()->json([
                'error' => 'An error occurred while retrieving content items',
            ], 500);
        }
    }
}
