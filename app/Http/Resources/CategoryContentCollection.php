<?php

namespace App\Http\Resources;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryContentCollection extends ResourceCollection
{
    protected Category $category;

    /**
     * Create a new resource collection instance.
     *
     * @param LengthAwarePaginator $resource
     * @param Category $category
     */
    public function __construct($resource, Category $category)
    {
        parent::__construct($resource);
        $this->category = $category;
    }

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'category' => [
                'id' => $this->category->id,
                'name' => $this->category->name,
                'status' => $this->category->status,
                'parent_id' => $this->category->parent_id,
                'parent_name' => $this->category->parent?->name,
                'total_contents_count' => $this->category->contents()->where('status', true)->count(),
                'created_at' => $this->category->created_at?->toISOString(),
                'updated_at' => $this->category->updated_at?->toISOString(),
            ],
            'contents' => ApiContentResource::collection($this->collection),
            'pagination' => [
                'current_page' => $this->currentPage(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'last_page' => $this->lastPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'has_more' => $this->hasMorePages(),
                'next_page_url' => $this->nextPageUrl(),
                'prev_page_url' => $this->previousPageUrl(),
            ],
            'content_type_summary' => $this->getContentTypeSummary(),
        ];
    }

    /**
     * Get a summary of content types in this category.
     *
     * @return array
     */
    private function getContentTypeSummary(): array
    {
        $summary = [
            'audio_count' => 0,
            'video_count' => 0,
            'book_count' => 0,
            'total_count' => 0,
        ];

        foreach ($this->collection as $content) {
            $summary['total_count']++;
            
            match ($content->contentable_type) {
                'App\\Models\\Audio' => $summary['audio_count']++,
                'App\\Models\\Video' => $summary['video_count']++,
                'App\\Models\\Book' => $summary['book_count']++,
                default => null,
            };
        }

        return $summary;
    }
}
