<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class HierarchicalCategoryCollection extends ResourceCollection
{
    /**
     * Create a new resource collection instance.
     *
     * @param mixed $resource
     */
    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'categories' => $this->collection->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'status' => $category->status,
                    'parent_id' => $category->parent_id,
                    'audio_count' => $category->audio_count ?? 0,
                    'videos_count' => $category->videos_count ?? 0,
                    'subcategories_count' => $category->subcategories->count(),
                    'created_at' => $category->created_at?->toISOString(),
                    'updated_at' => $category->updated_at?->toISOString(),
                    'subcategories' => $category->subcategories->map(function ($subcategory) {
                        return [
                            'id' => $subcategory->id,
                            'name' => $subcategory->name,
                            'status' => $subcategory->status,
                            'parent_id' => $subcategory->parent_id,
                            'audio_count' => $subcategory->audio_count ?? 0,
                            'videos_count' => $subcategory->videos_count ?? 0,
                            'subcategories_count' => 0, // Subcategories don't have their own subcategories in this response
                            'created_at' => $subcategory->created_at?->toISOString(),
                            'updated_at' => $subcategory->updated_at?->toISOString(),
                        ];
                    }),
                ];
            }),
        ];
    }
}
