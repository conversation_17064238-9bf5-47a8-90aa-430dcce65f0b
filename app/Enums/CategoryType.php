<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum CategoryType: string implements HasLabel
{
    case Audio = 'audio';
    case Video = 'video';
    case Reference = 'reference';
    case Tweet = 'tweet';

    /**
     * Get the human-readable label for the enum case.
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::Audio => __('audios'),
            self::Video => __('videos'),
            self::Reference => __('references'),
            self::Tweet => __('tweets'),
        };
    }

    /**
     * Get the color associated with the enum case for Filament badges.
     */
    public function getColor(): string
    {
        return match ($this) {
            self::Audio => 'primary',
            self::Video => 'success',
            self::Reference => 'warning',
            self::Tweet => 'danger',
        };
    }

    /**
     * Get the icon associated with the enum case.
     */
    public function getIcon(): string
    {
        return match ($this) {
            self::Audio => 'heroicon-o-musical-note',
            self::Video => 'heroicon-o-video-camera',
            self::Reference => 'heroicon-o-book-open',
            self::Tweet => 'heroicon-o-chat-bubble-left-ellipsis',
        };
    }

    /**
     * Get all enum values as an array.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all enum cases as options for Filament select fields.
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->getLabel();
        }
        return $options;
    }

    /**
     * Get a specific case by its value.
     */
    public static function fromValue(string $value): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->value === $value) {
                return $case;
            }
        }
        return null;
    }
}
