<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasColor;

enum ContentType: string implements HasLabel, HasIcon, HasColor
{
    case Audio = 'audio';
    case Video = 'video';
    case Book = 'book';

    /**
     * Get the human-readable label for the enum case.
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::Audio => __('audio'),
            self::Video => __('video'),
            self::Book => __('book'),
        };
    }

    /**
     * Get the icon associated with the enum case.
     */
    public function getIcon(): string
    {
        return match ($this) {
            self::Audio => 'heroicon-o-musical-note',
            self::Video => 'heroicon-o-video-camera',
            self::Book => 'heroicon-o-book-open',
        };
    }

    /**
     * Get the color associated with the enum case for Filament badges.
     */
    public function getColor(): string
    {
        return match ($this) {
            self::Audio => 'primary',
            self::Video => 'success',
            self::Book => 'warning',
        };
    }

    /**
     * Get all enum values as an array.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all enum cases as options for Filament select fields.
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->getLabel();
        }
        return $options;
    }

    /**
     * Get a specific case by its value.
     */
    public static function fromValue(string $value): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->value === $value) {
                return $case;
            }
        }
        return null;
    }

    /**
     * Get the default image URL for this content type.
     */
    public function getDefaultImageUrl(): string
    {
        return match ($this) {
            self::Audio => asset('assets/images/audio-placeholder.png'),
            self::Video => asset('assets/images/video-placeholder.png'),
            self::Book => asset('assets/images/book-placeholder.png'),
        };
    }

    /**
     * Get the model class name for this content type.
     */
    public function getModelClass(): string
    {
        return match ($this) {
            self::Audio => \App\Models\Audio::class,
            self::Video => \App\Models\Video::class,
            self::Book => \App\Models\Book::class,
        };
    }
}
