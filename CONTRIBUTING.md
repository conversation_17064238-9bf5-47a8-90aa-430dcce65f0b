# Contribution Guide for Flutter-Riverpod-2.0-Template

Thank you for your interest in contributing to the Flutter-Riverpod-2.0-Template project. This guide will help you understand how to contribute effectively to the project.

## Getting Started

Before you start contributing, make sure you have the following prerequisites:

- Familiarity with Flutter and Dart programming.
- Basic understanding of Riverpod 2.0 for state management.
- Knowledge of Freezed for immutable state management.
- Familiarity with Go Router for routing.
- Understanding of Hive for local storage.
- Familiarity with Easy Localization for app localization.

## Setting Up Your Development Environment

1. **Fork the Repository**: Fork the Flutter-Riverpod-2.0-Template repository to your GitHub account.
2. **Clone the Repository**: Clone the forked repository to your local machine.
3. **Install Dependencies**: Run `flutter pub get` and `dart run build_runner build` to install all the necessary dependencies.
4. **Run the Project**: Run the project on your local machine to ensure everything is set up correctly.

## Contribution Guidelines

### Code Style and Quality

- **Follow Dart Style Guide**: Ensure your code adheres to the Dart style guide.
- **Use Linting**: The project uses Flutter Lints for stricter linting rules. Make sure your code passes the lint checks.
- **Write Tests**: Write tests for your code to ensure it works as expected.
- **Document Your Code**: Add comments and documentation to your code to make it easier for others to understand.

### Feature Contributions

- **Feature-First Folder Structure**: Organize your code following the feature-first folder structure.
- **Use Riverpod and Freezed**: Use Riverpod for state management and Freezed for immutable state.
- **Routing with Go Router**: Implement routing using Go Router with fade and slide transitions.
- **Local Storage with Hive**: Use Hive for platform-independent storage.
- **Localization with Easy Localization**: Implement localization using Easy Localization.

### Pull Requests

- **Create a New Branch**: Always create a new branch for your feature or bug fix.
- **Commit Your Changes**: Commit your changes with a clear and concise commit message.
- **Open a Pull Request**: Open a pull request with a detailed description of your changes.

### Contributing to Documentation

- **Improve Existing Documentation**: If you find any part of the documentation lacking, feel free to improve it.
- **Add New Documentation**: If you add a new feature or make significant changes, update the documentation accordingly.

### Contributing to the Community

- **Report Bugs**: If you find any bugs, report them using GitHub issues.
- **Suggest Features**: If you have any feature suggestions, add them to the GitHub issues.
- **Review Pull Requests**: Review other contributors' pull requests and provide feedback.

### Additional Contributions

- **Localization**: Help add more language support for the localization.
- **Examples**: Add more examples for the logic classes, especially for handling API calls.
- **Contributor Page**: Contribute to the development of a contributor page in the app.

## Conclusion

Contributing to the Flutter-Riverpod-2.0-Template project is a great way to improve your skills and give back to the community. Follow the guidelines above, and don't hesitate to reach out if you have any questions or need further assistance. Thank you for your contribution!
