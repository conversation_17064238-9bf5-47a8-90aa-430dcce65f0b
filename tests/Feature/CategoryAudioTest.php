<?php

use App\Enums\CategoryType;
use App\Models\Audio;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('audio models can be created without category_id', function () {
    $audio1 = Audio::factory()->create();
    $audio2 = Audio::factory()->create();

    expect($audio1->audio_file)->toBeString();
    expect($audio2->audio_file)->toBeString();
    expect($audio1->id)->not->toBe($audio2->id);
});

test('audio factory creates valid audio files', function () {
    $audio = Audio::factory()->create();

    expect($audio->audio_file)->toBeString();
    expect($audio->audio_file)->toContain('audio/');
    expect($audio->audio_file)->toContain('.mp3');
});

test('category scopes work correctly', function () {
    Category::factory()->create(['status' => true]);
    Category::factory()->create(['status' => false]);
    Category::factory()->create(['name' => 'Audio Category']);
    Category::factory()->create(['name' => 'Video Category']);

    expect(Category::active()->count())->toBeGreaterThanOrEqual(1);
});

test('audio models have proper attributes', function () {
    $audio = Audio::factory()->create();

    expect($audio)->toHaveKey('audio_file');
    expect($audio)->not->toHaveKey('category_id');
    expect($audio)->not->toHaveKey('title');
    expect($audio)->not->toHaveKey('description');
});

test('enum provides correct labels and colors', function () {
    expect(CategoryType::Audio->getLabel())->toBe(__('audios'));
    expect(CategoryType::Video->getLabel())->toBe(__('videos'));
    expect(CategoryType::Reference->getLabel())->toBe(__('references'));
    expect(CategoryType::Tweet->getLabel())->toBe(__('tweets'));

    expect(CategoryType::Audio->getColor())->toBe('primary');
    expect(CategoryType::Video->getColor())->toBe('success');
    expect(CategoryType::Reference->getColor())->toBe('warning');
    expect(CategoryType::Tweet->getColor())->toBe('danger');
});

test('enum helper methods work correctly', function () {
    $values = CategoryType::values();
    expect($values)->toContain('audio', 'video', 'reference', 'tweet');

    $options = CategoryType::options();
    expect($options)->toHaveKey('audio');
    expect($options)->toHaveKey('video');
    expect($options)->toHaveKey('reference');
    expect($options)->toHaveKey('tweet');

    expect(CategoryType::fromValue('audio'))->toBe(CategoryType::Audio);
    expect(CategoryType::fromValue('invalid'))->toBeNull();
});
