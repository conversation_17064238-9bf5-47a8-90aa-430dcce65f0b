<?php

namespace Tests\Feature;

use App\Models\Admin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_login_page(): void
    {
        $response = $this->get('/admin/login');

        $response->assertStatus(200);
    }

    public function test_admin_can_authenticate_with_valid_credentials(): void
    {
        $admin = Admin::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Test that we can authenticate the admin manually
        $this->assertTrue(auth('admin')->attempt([
            'email' => '<EMAIL>',
            'password' => 'password',
        ]));

        $this->assertAuthenticatedAs($admin, 'admin');
    }

    public function test_admin_cannot_authenticate_with_invalid_credentials(): void
    {
        Admin::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Test that authentication fails with wrong password
        $this->assertFalse(auth('admin')->attempt([
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]));

        $this->assertGuest('admin');
    }

    public function test_admin_can_access_dashboard_when_authenticated(): void
    {
        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin');

        $response->assertStatus(200);
    }

    public function test_guest_cannot_access_admin_dashboard(): void
    {
        $response = $this->get('/admin');

        $response->assertRedirect('/admin/login');
    }

    public function test_admin_model_implements_filament_user_contract(): void
    {
        $admin = new Admin();

        $this->assertInstanceOf(\Filament\Models\Contracts\FilamentUser::class, $admin);
    }
}
