<?php

use App\Models\Audio;
use App\Models\Category;
use App\Models\Video;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test data for each test
    createTestData();
});

function createTestData()
{
    // Create parent categories
    $audioParent = Category::factory()->create([
        'name' => 'Audio Content',
        'status' => true,
        'parent_id' => null,
    ]);

    $videoParent = Category::factory()->create([
        'name' => 'Video Content',
        'status' => true,
        'parent_id' => null,
    ]);

    $inactiveParent = Category::factory()->create([
        'name' => 'Inactive Parent',
        'status' => false,
        'parent_id' => null,
    ]);

    // Create subcategories
    $audioSub1 = Category::factory()->create([
        'name' => 'Islamic Lectures',
        'status' => true,
        'parent_id' => $audioParent->id,
    ]);

    $audioSub2 = Category::factory()->create([
        'name' => 'Quran Recitations',
        'status' => true,
        'parent_id' => $audioParent->id,
    ]);

    $videoSub1 = Category::factory()->create([
        'name' => 'Educational Videos',
        'status' => true,
        'parent_id' => $videoParent->id,
    ]);

    $inactiveSub = Category::factory()->create([
        'name' => 'Inactive Sub',
        'status' => false,
        'parent_id' => $audioParent->id,
    ]);

    // Create content (note: category relationships now handled through Content model)
    Audio::factory(3)->create();
    Audio::factory(2)->create();
    Video::factory(2)->create();
    Audio::factory(1)->create();

    return [
        'audioParent' => $audioParent,
        'videoParent' => $videoParent,
        'inactiveParent' => $inactiveParent,
        'audioSub1' => $audioSub1,
        'audioSub2' => $audioSub2,
        'videoSub1' => $videoSub1,
        'inactiveSub' => $inactiveSub,
    ];
}

test('get hierarchical categories returns success response', function () {
    $response = $this->getJson('/api/v1/categories');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'categories' => [
                    '*' => [
                        'id',
                        'name',
                        'status',
                        'parent_id',
                        'audio_count',
                        'videos_count',
                        'subcategories_count',
                        'created_at',
                        'updated_at',
                        'subcategories' => [
                            '*' => [
                                'id',
                                'name',
                                'status',
                                'parent_id',
                                'audio_count',
                                'videos_count',
                                'subcategories_count',
                                'created_at',
                                'updated_at',
                            ]
                        ]
                    ]
                ]
            ]
        ]);

    $data = $response->json('data.categories');

    // Should only return active parent categories
    expect($data)->toHaveCount(2);

    // Check that parent categories have null parent_id
    foreach ($data as $category) {
        expect($category['parent_id'])->toBeNull();
        expect($category['status'])->toBeTrue();
    }
});

test('hierarchical categories include correct subcategories', function () {
    $response = $this->getJson('/api/v1/categories');

    $response->assertStatus(200);

    $categories = $response->json('data.categories');

    // Find the Audio Content category
    $audioCategory = collect($categories)->firstWhere('name', 'Audio Content');
    expect($audioCategory)->not->toBeNull();

    // Should have 2 active subcategories (inactive one should be excluded)
    expect($audioCategory['subcategories'])->toHaveCount(2);

    $subcategoryNames = collect($audioCategory['subcategories'])->pluck('name')->toArray();
    expect($subcategoryNames)->toContain('Islamic Lectures', 'Quran Recitations');
    expect($subcategoryNames)->not->toContain('Inactive Sub');
});

test('hierarchical categories include correct counts', function () {
    $response = $this->getJson('/api/v1/categories');

    $response->assertStatus(200);

    $categories = $response->json('data.categories');

    // Find the Audio Content category
    $audioCategory = collect($categories)->firstWhere('name', 'Audio Content');

    // Parent should have 1 direct audio file
    expect($audioCategory['audio_count'])->toBe(1);
    expect($audioCategory['videos_count'])->toBe(0);
    expect($audioCategory['subcategories_count'])->toBe(2);

    // Check subcategory counts
    $lecturesSubcategory = collect($audioCategory['subcategories'])->firstWhere('name', 'Islamic Lectures');
    expect($lecturesSubcategory['audio_count'])->toBe(3);
    expect($lecturesSubcategory['videos_count'])->toBe(0);

    $recitationsSubcategory = collect($audioCategory['subcategories'])->firstWhere('name', 'Quran Recitations');
    expect($recitationsSubcategory['audio_count'])->toBe(2);
    expect($recitationsSubcategory['videos_count'])->toBe(0);
});



test('empty response when no parent categories exist', function () {
    // Delete all categories
    Category::truncate();

    $response = $this->getJson('/api/v1/categories');

    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'categories' => []
            ]
        ]);
});

test('categories are ordered alphabetically', function () {
    $response = $this->getJson('/api/v1/categories');

    $response->assertStatus(200);

    $categories = $response->json('data.categories');
    $categoryNames = collect($categories)->pluck('name')->toArray();

    // Should be ordered: Audio Content, Video Content
    expect($categoryNames)->toBe(['Audio Content', 'Video Content']);

    // Check subcategories are also ordered
    $audioCategory = collect($categories)->firstWhere('name', 'Audio Content');
    $subcategoryNames = collect($audioCategory['subcategories'])->pluck('name')->toArray();

    // Should be ordered: Islamic Lectures, Quran Recitations
    expect($subcategoryNames)->toBe(['Islamic Lectures', 'Quran Recitations']);
});
