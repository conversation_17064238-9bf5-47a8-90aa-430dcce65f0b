<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\App;
use Tests\TestCase;

class ContentResourceTranslationTest extends TestCase
{
    public function test_english_content_translations_work(): void
    {
        App::setLocale('en');

        // Navigation and Labels
        $this->assertEquals('Contents', __('contents'));
        $this->assertEquals('Content', __('content'));
        $this->assertEquals('Contents', __('content_plural'));
        $this->assertEquals('Content Management', __('content_management'));
        $this->assertEquals('Create Content', __('create_content'));
        $this->assertEquals('Edit Content', __('edit_content'));
        $this->assertEquals('View Content', __('view_content'));

        // Section headings
        $this->assertEquals('Basic Information', __('basic_information'));
        $this->assertEquals('Audio Details', __('audio_details'));
        $this->assertEquals('Video Details', __('video_details'));
        $this->assertEquals('Book Details', __('book_details'));

        // Form field labels
        $this->assertEquals('Content Type', __('content_type'));
        $this->assertEquals('Select content type', __('select_content_type'));
        $this->assertEquals('Preview', __('preview'));
        $this->assertEquals('Details', __('details'));
        $this->assertEquals('Has File', __('has_file'));
        $this->assertEquals('Has Cover Image', __('has_cover_image'));

        // Book-specific fields
        $this->assertEquals('Book', __('book'));
        $this->assertEquals('Books', __('books'));
        $this->assertEquals('Pages Count', __('pages_count'));
        $this->assertEquals('Published Date', __('published_date'));
        $this->assertEquals('Publisher', __('publisher'));
        $this->assertEquals('Cover Image', __('cover_image'));
        $this->assertEquals('Book File', __('book_file'));

        // Placeholders
        $this->assertEquals('Enter content title', __('content_title_placeholder'));
        $this->assertEquals('Enter content description', __('content_description_placeholder'));
        $this->assertEquals('Enter book title', __('book_title_placeholder'));
        $this->assertEquals('e.g., 250', __('pages_count_placeholder'));
        $this->assertEquals('Enter publisher name', __('publisher_placeholder'));

        // Help text
        $this->assertEquals('Choose the type of content you want to create', __('content_type_help'));
        $this->assertEquals('The main title for this content item', __('content_title_help'));
        $this->assertEquals('Optional description explaining what this content is about', __('content_description_help'));
        $this->assertEquals('Upload the book file (PDF, EPUB formats supported)', __('book_file_help'));
        $this->assertEquals('Upload a cover image for the book (JPEG, PNG, WebP formats supported)', __('cover_image_help'));

        // Actions and messages
        $this->assertEquals('View', __('view'));
        $this->assertEquals('Content deleted successfully!', __('content_deleted'));
        $this->assertEquals('Selected content items deleted successfully!', __('content_bulk_deleted'));
        $this->assertEquals('Book deleted successfully!', __('book_deleted'));
        $this->assertEquals('Selected books deleted successfully!', __('books_bulk_deleted'));
    }

    public function test_arabic_content_translations_work(): void
    {
        App::setLocale('ar');

        // Navigation and Labels
        $this->assertEquals('المحتويات', __('contents'));
        $this->assertEquals('محتوى', __('content'));
        $this->assertEquals('المحتويات', __('content_plural'));
        $this->assertEquals('إدارة المحتوى', __('content_management'));
        $this->assertEquals('إنشاء محتوى', __('create_content'));
        $this->assertEquals('تعديل المحتوى', __('edit_content'));
        $this->assertEquals('عرض المحتوى', __('view_content'));

        // Section headings
        $this->assertEquals('المعلومات الأساسية', __('basic_information'));
        $this->assertEquals('تفاصيل الصوت', __('audio_details'));
        $this->assertEquals('تفاصيل الفيديو', __('video_details'));
        $this->assertEquals('تفاصيل الكتاب', __('book_details'));

        // Form field labels
        $this->assertEquals('نوع المحتوى', __('content_type'));
        $this->assertEquals('اختر نوع المحتوى', __('select_content_type'));
        $this->assertEquals('معاينة', __('preview'));
        $this->assertEquals('التفاصيل', __('details'));
        $this->assertEquals('يحتوي على ملف', __('has_file'));
        $this->assertEquals('يحتوي على صورة غلاف', __('has_cover_image'));

        // Book-specific fields
        $this->assertEquals('كتاب', __('book'));
        $this->assertEquals('الكتب', __('books'));
        $this->assertEquals('عدد الصفحات', __('pages_count'));
        $this->assertEquals('تاريخ النشر', __('published_date'));
        $this->assertEquals('الناشر', __('publisher'));
        $this->assertEquals('صورة الغلاف', __('cover_image'));
        $this->assertEquals('ملف الكتاب', __('book_file'));

        // Placeholders
        $this->assertEquals('أدخل عنوان المحتوى', __('content_title_placeholder'));
        $this->assertEquals('أدخل وصف المحتوى', __('content_description_placeholder'));
        $this->assertEquals('أدخل عنوان الكتاب', __('book_title_placeholder'));
        $this->assertEquals('مثال: 250', __('pages_count_placeholder'));
        $this->assertEquals('أدخل اسم الناشر', __('publisher_placeholder'));

        // Help text
        $this->assertEquals('اختر نوع المحتوى الذي تريد إنشاؤه', __('content_type_help'));
        $this->assertEquals('العنوان الرئيسي لهذا المحتوى', __('content_title_help'));
        $this->assertEquals('وصف اختياري يوضح ما يتعلق به هذا المحتوى', __('content_description_help'));
        $this->assertEquals('ارفع ملف الكتاب (تنسيقات PDF، EPUB مدعومة)', __('book_file_help'));
        $this->assertEquals('ارفع صورة غلاف للكتاب (تنسيقات JPEG، PNG، WebP مدعومة)', __('cover_image_help'));

        // Actions and messages
        $this->assertEquals('عرض', __('view'));
        $this->assertEquals('تم حذف المحتوى بنجاح!', __('content_deleted'));
        $this->assertEquals('تم حذف عناصر المحتوى المحددة بنجاح!', __('content_bulk_deleted'));
        $this->assertEquals('تم حذف الكتاب بنجاح!', __('book_deleted'));
        $this->assertEquals('تم حذف الكتب المحددة بنجاح!', __('books_bulk_deleted'));
    }

    public function test_content_type_options_translations(): void
    {
        App::setLocale('en');
        $this->assertEquals('Audio', __('audio'));
        $this->assertEquals('Video', __('video'));
        $this->assertEquals('Book', __('book'));
        $this->assertEquals('Active', __('active'));
        $this->assertEquals('Inactive', __('inactive'));

        App::setLocale('ar');
        $this->assertEquals('صوتي', __('audio'));
        $this->assertEquals('فيديو', __('video'));
        $this->assertEquals('كتاب', __('book'));
        $this->assertEquals('نشط', __('active'));
        $this->assertEquals('غير نشط', __('inactive'));
    }
}
