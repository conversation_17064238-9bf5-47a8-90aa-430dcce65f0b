<?php

use App\Models\Audio;
use App\Models\Category;
use App\Models\Video;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('category can have parent and subcategories', function () {
    $parent = Category::factory()->create(['name' => 'Parent Category']);
    $child1 = Category::factory()->withParent($parent)->create(['name' => 'Child 1']);
    $child2 = Category::factory()->withParent($parent)->create(['name' => 'Child 2']);

    expect($parent->subcategories)->toHaveCount(2);
    expect($child1->parent->id)->toBe($parent->id);
    expect($child2->parent->id)->toBe($parent->id);
});

test('category helper methods work correctly', function () {
    $parent = Category::factory()->create(['name' => 'Parent Category']);
    $child = Category::factory()->withParent($parent)->create(['name' => 'Child Category']);

    expect($parent->isParent())->toBeTrue();
    expect($parent->isChild())->toBeFalse();
    expect($child->isParent())->toBeFalse();
    expect($child->isChild())->toBeTrue();
});

test('category ancestors and descendants work correctly', function () {
    $grandparent = Category::factory()->create(['name' => 'Grandparent']);
    $parent = Category::factory()->withParent($grandparent)->create(['name' => 'Parent']);
    $child = Category::factory()->withParent($parent)->create(['name' => 'Child']);

    $ancestors = $child->getAncestors();
    expect($ancestors)->toHaveCount(2);
    expect($ancestors->pluck('name')->toArray())->toContain('Parent', 'Grandparent');

    $descendants = $grandparent->getDescendants();
    expect($descendants)->toHaveCount(2);
    expect($descendants->pluck('name')->toArray())->toContain('Parent', 'Child');
});

test('audio and video models can be created independently', function () {
    $parent = Category::factory()->create(['name' => 'Audio Content']);
    $audioCategory = Category::factory()->withParent($parent)->create(['name' => 'Lectures']);
    $videoCategory = Category::factory()->withParent($parent)->create(['name' => 'Educational Videos']);

    $audio = Audio::factory()->create();
    $video = Video::factory()->create();

    expect($audio->audio_file)->toBeString();
    expect($video->youtube_video_id)->toBeString();
    expect($audioCategory->parent->name)->toBe('Audio Content');
    expect($videoCategory->parent->name)->toBe('Audio Content');
});

test('category deletion cascades to subcategories', function () {
    $parent = Category::factory()->create(['name' => 'Parent Category']);
    $child = Category::factory()->withParent($parent)->create(['name' => 'Child Category']);

    $parentId = $parent->id;
    $childId = $child->id;

    $parent->delete();

    expect(Category::find($parentId))->toBeNull();
    expect(Category::find($childId))->toBeNull();
});

test('category factory methods work correctly', function () {
    $parent = Category::factory()->parent()->create();
    $child = Category::factory()->withParent($parent)->create();

    expect($parent->parent_id)->toBeNull();
    expect($child->parent_id)->toBe($parent->id);
});

test('active scope works with hierarchical categories', function () {
    $activeParent = Category::factory()->active()->create();
    $inactiveParent = Category::factory()->inactive()->create();
    $activeChild = Category::factory()->withParent($activeParent)->active()->create();
    $inactiveChild = Category::factory()->withParent($inactiveParent)->inactive()->create();

    $activeCategories = Category::active()->get();
    $activeCategoryIds = $activeCategories->pluck('id')->toArray();

    expect($activeCategoryIds)->toContain($activeParent->id);
    expect($activeCategoryIds)->toContain($activeChild->id);
    expect($activeCategoryIds)->not->toContain($inactiveParent->id);
    expect($activeCategoryIds)->not->toContain($inactiveChild->id);
});

test('categories can have hierarchical structure for content organization', function () {
    $mediaParent = Category::factory()->create(['name' => 'Media Content']);
    $audioCategory = Category::factory()->withParent($mediaParent)->create(['name' => 'Audio']);
    $videoCategory = Category::factory()->withParent($mediaParent)->create(['name' => 'Video']);

    // Create content models (categorization now handled through Content model)
    $audioFiles = Audio::factory(2)->create();
    $videoFiles = Video::factory(3)->create();

    // Test category hierarchy structure
    expect($mediaParent->subcategories)->toHaveCount(2);
    expect($audioCategory->parent->id)->toBe($mediaParent->id);
    expect($videoCategory->parent->id)->toBe($mediaParent->id);
    expect($audioCategory->name)->toBe('Audio');
    expect($videoCategory->name)->toBe('Video');

    // Test that content models were created successfully
    expect($audioFiles)->toHaveCount(2);
    expect($videoFiles)->toHaveCount(3);
});
