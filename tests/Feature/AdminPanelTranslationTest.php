<?php

namespace Tests\Feature;

use App\Models\Admin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Tests\TestCase;

class AdminPanelTranslationTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_panel_displays_english_translations(): void
    {
        App::setLocale('en');

        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin');

        $response->assertStatus(200);
        // The page should contain default Filament translations (not overridden)
    }

    public function test_admin_panel_displays_arabic_translations(): void
    {
        App::setLocale('ar');

        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin');

        $response->assertStatus(200);
        // The page should contain default Filament translations (not overridden)
    }

    public function test_admin_resource_list_page_shows_translations(): void
    {
        App::setLocale('en');

        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin/admins');

        $response->assertStatus(200);
        // Should show English admin translations
        $response->assertSee('Admins'); // From admins
    }

    public function test_admin_resource_list_page_shows_arabic_translations(): void
    {
        App::setLocale('ar');

        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin/admins');

        $response->assertStatus(200);
        // Should show Arabic admin translations
        $response->assertSee('المديرون'); // From admins
    }

    public function test_admin_create_page_shows_english_form_labels(): void
    {
        App::setLocale('en');

        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin/admins/create');

        $response->assertStatus(200);
        // Should show English form field labels
        $response->assertSee('Name'); // From name
        $response->assertSee('Email Address'); // From email
    }

    public function test_admin_create_page_shows_arabic_form_labels(): void
    {
        App::setLocale('ar');

        $admin = Admin::factory()->create();

        $response = $this->actingAs($admin, 'admin')->get('/admin/admins/create');

        $response->assertStatus(200);
        // Should show Arabic form field labels
        $response->assertSee('الاسم'); // From name
        $response->assertSee('البريد الإلكتروني'); // From email
    }

    public function test_admin_resource_methods_return_correct_translations(): void
    {
        App::setLocale('en');

        $this->assertEquals('Admins', \App\Filament\Resources\AdminResource::getNavigationLabel());
        $this->assertEquals('Admin', \App\Filament\Resources\AdminResource::getModelLabel());
        $this->assertEquals('Admins', \App\Filament\Resources\AdminResource::getPluralModelLabel());

        App::setLocale('ar');

        $this->assertEquals('المديرون', \App\Filament\Resources\AdminResource::getNavigationLabel());
        $this->assertEquals('مدير', \App\Filament\Resources\AdminResource::getModelLabel());
        $this->assertEquals('المديرون', \App\Filament\Resources\AdminResource::getPluralModelLabel());
    }
}
