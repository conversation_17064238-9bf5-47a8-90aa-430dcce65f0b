<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\App;
use Tests\TestCase;

class TranslationTest extends TestCase
{
    public function test_english_json_translations_work(): void
    {
        App::setLocale('en');

        // Test admin translations
        $this->assertEquals('Admins', __('admins'));
        $this->assertEquals('Admin', __('admin'));
        $this->assertEquals('Create Admin', __('create_admin'));
        $this->assertEquals('Name', __('name'));
        $this->assertEquals('Enter admin name', __('name_placeholder'));
        $this->assertEquals('Admin created successfully!', __('admin_created'));

        // Test that we're not overriding default Filament translations
        $this->assertEquals('save', __('save')); // Should fallback to key since we don't override it
    }

    public function test_arabic_json_translations_work(): void
    {
        App::setLocale('ar');

        // Test admin translations
        $this->assertEquals('المشرفين', __('admins'));
        $this->assertEquals('مشرف', __('admin'));
        $this->assertEquals('إنشاء مشرف', __('create_admin'));
        $this->assertEquals('الاسم', __('name'));
        $this->assertEquals('أدخل الاسم', __('name_placeholder'));
        $this->assertEquals('تم إنشاء المشرف بنجاح!', __('admin_created'));

        // Test that we're not overriding default Filament translations
        $this->assertEquals('save', __('save')); // Should fallback to key since we don't override it
    }

    public function test_fallback_to_english_when_translation_missing(): void
    {
        App::setLocale('ar');

        // Test a key that doesn't exist - should fallback to the key itself
        $this->assertEquals('non_existent_key', __('non_existent_key'));
    }

    public function test_admin_specific_translations_only(): void
    {
        App::setLocale('en');

        // Test that we only have admin-specific translations
        $this->assertEquals('Admins', __('admins'));
        $this->assertEquals('Admin created successfully!', __('admin_created'));

        // Test that Filament defaults are not overridden
        $this->assertEquals('edit', __('edit')); // Should return key (uses Filament default)
        $this->assertEquals('delete', __('delete')); // Should return key (uses Filament default)
        $this->assertEquals('create', __('create')); // Should return key (uses Filament default)
    }

    public function test_locale_switching(): void
    {
        // Test switching between locales
        App::setLocale('en');
        $this->assertEquals('Admins', __('admins'));

        App::setLocale('ar');
        $this->assertEquals('المشرفين', __('admins'));

        App::setLocale('en');
        $this->assertEquals('Admins', __('admins'));
    }
}
